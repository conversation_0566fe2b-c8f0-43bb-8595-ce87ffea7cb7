#!/bin/bash

# Build iOS Framework for TuShen Core
set -e

echo "Building iOS Framework for TuShen Core..."

# Navigate to rust directory
cd rust

# Build for iOS simulator (arm64)
echo "Building for iOS Simulator (arm64)..."
cargo build --release --target aarch64-apple-ios-sim

# Build for iOS device (arm64)
echo "Building for iOS Device (arm64)..."
cargo build --release --target aarch64-apple-ios

# Create framework directory structure
FRAMEWORK_DIR="../ios/Frameworks/TushenCore.framework"
echo "Creating framework directory: $FRAMEWORK_DIR"
rm -rf "$FRAMEWORK_DIR"
mkdir -p "$FRAMEWORK_DIR"

# For iOS, we need to use the simulator library for simulator builds
# and device library for device builds. Since both are arm64, we can't combine them.
# We'll use the simulator library by default since we're testing on simulator.
echo "Using iOS simulator library for compatibility..."
cp target/aarch64-apple-ios-sim/release/libtushen_core.a "$FRAMEWORK_DIR/TushenCore"

# Create Info.plist
echo "Creating Info.plist..."
cat > "$FRAMEWORK_DIR/Info.plist" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleDevelopmentRegion</key>
    <string>en</string>
    <key>CFBundleExecutable</key>
    <string>TushenCore</string>
    <key>CFBundleIdentifier</key>
    <string>com.tushen.core</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundleName</key>
    <string>TushenCore</string>
    <key>CFBundlePackageType</key>
    <string>FMWK</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0.0</string>
    <key>CFBundleVersion</key>
    <string>1</string>
    <key>MinimumOSVersion</key>
    <string>11.0</string>
</dict>
</plist>
EOF

# Create Headers directory and copy header
echo "Creating Headers directory..."
mkdir -p "$FRAMEWORK_DIR/Headers"
cp ../ios/Classes/bridge_generated.h "$FRAMEWORK_DIR/Headers/"

# Create module.modulemap
echo "Creating module.modulemap..."
cat > "$FRAMEWORK_DIR/Headers/module.modulemap" << EOF
framework module TushenCore {
    header "bridge_generated.h"
    export *
}
EOF

echo "iOS Framework built successfully at: $FRAMEWORK_DIR"
echo "Framework contents:"
ls -la "$FRAMEWORK_DIR"
echo "Headers:"
ls -la "$FRAMEWORK_DIR/Headers"
