#!/bin/bash

# <PERSON>ript to copy Rust library to Flutter app bundle

set -e

# Get the directory of this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Define paths
RUST_LIB_PATH="$PROJECT_ROOT/rust/target/release/libtushen_core.dylib"
FLUTTER_APP_PATH="$PROJECT_ROOT/build/macos/Build/Products/Debug/tushen.app/Contents/MacOS"

echo "Copying Rust library to Flutter app bundle..."
echo "Source: $RUST_LIB_PATH"
echo "Destination: $FLUTTER_APP_PATH"

# Check if Rust library exists
if [ ! -f "$RUST_LIB_PATH" ]; then
    echo "Error: Rust library not found at $RUST_LIB_PATH"
    echo "Please build the Rust library first with: cd rust && cargo build --release"
    exit 1
fi

# Check if Flutter app bundle exists
if [ ! -d "$FLUTTER_APP_PATH" ]; then
    echo "Error: Flutter app bundle not found at $FLUTTER_APP_PATH"
    echo "Please build the Flutter app first with: flutter build macos"
    exit 1
fi

# Copy the library
cp "$RUST_LIB_PATH" "$FLUTTER_APP_PATH/"

echo "Successfully copied Rust library to Flutter app bundle"
