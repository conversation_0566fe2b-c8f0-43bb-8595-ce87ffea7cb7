#!/usr/bin/env ruby

require 'xcodeproj'

# Path to the Xcode project
project_path = 'macos/Runner.xcodeproj'

# Open the project
project = Xcodeproj::Project.open(project_path)

# Find the Runner target
runner_target = project.targets.find { |target| target.name == 'Runner' }

if runner_target.nil?
  puts "Error: Could not find Runner target"
  exit 1
end

# Check if the script already exists
existing_script = runner_target.build_phases.find do |phase|
  phase.is_a?(Xcodeproj::Project::Object::PBXShellScriptBuildPhase) &&
  phase.name == 'Copy Rust Library'
end

if existing_script
  puts "Rust library copy script already exists"
  exit 0
end

# Create a new shell script build phase
script_phase = runner_target.new_shell_script_build_phase('Copy Rust Library')

# Set the script content
script_content = <<~SCRIPT
# Copy Rust library to the app bundle
echo "Starting Rust library copy script..."
echo "PROJECT_DIR: $PROJECT_DIR"
echo "BUILT_PRODUCTS_DIR: $BUILT_PRODUCTS_DIR"
echo "PRODUCT_NAME: $PRODUCT_NAME"

RUST_LIB_PATH="$PROJECT_DIR/rust/target/release/libtushen_core.dylib"
APP_MACOS_PATH="$BUILT_PRODUCTS_DIR/$PRODUCT_NAME.app/Contents/MacOS"

echo "Looking for Rust library at: $RUST_LIB_PATH"
echo "Target app MacOS path: $APP_MACOS_PATH"

if [ -f "$RUST_LIB_PATH" ]; then
    echo "Found Rust library, copying..."
    cp "$RUST_LIB_PATH" "$APP_MACOS_PATH/"
    if [ $? -eq 0 ]; then
        echo "✅ Rust library copied successfully"
        ls -la "$APP_MACOS_PATH/libtushen_core.dylib"
    else
        echo "❌ Failed to copy Rust library"
    fi
else
    echo "⚠️  Warning: Rust library not found at $RUST_LIB_PATH"
    echo "Please run 'cargo build --release' in the rust directory"
    echo "Available files in rust/target/release/:"
    ls -la "$PROJECT_DIR/rust/target/release/" || echo "rust/target/release/ directory not found"
fi
SCRIPT

script_phase.shell_script = script_content

# Move the script to run after the "Bundle Framework" phase
bundle_framework_phase = runner_target.build_phases.find do |phase|
  phase.is_a?(Xcodeproj::Project::Object::PBXShellScriptBuildPhase) &&
  phase.name == 'Bundle Framework'
end

if bundle_framework_phase
  # Remove the script from its current position
  runner_target.build_phases.delete(script_phase)
  
  # Find the index of the Bundle Framework phase
  bundle_index = runner_target.build_phases.index(bundle_framework_phase)
  
  # Insert the script after the Bundle Framework phase
  runner_target.build_phases.insert(bundle_index + 1, script_phase)
end

# Save the project
project.save

puts "Successfully added Rust library copy script to Xcode project"
puts "The script will automatically copy libtushen_core.dylib to the app bundle during build"
