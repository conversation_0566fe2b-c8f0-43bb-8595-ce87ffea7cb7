#!/usr/bin/env ruby

require 'xcodeproj'

# Path to the Xcode project
project_path = 'macos/Runner.xcodeproj'

# Open the project
project = Xcodeproj::Project.open(project_path)

# Find the Runner target
runner_target = project.targets.find { |target| target.name == 'Runner' }

if runner_target.nil?
  puts "Error: Could not find Runner target"
  exit 1
end

# Find and remove the existing script
existing_script = runner_target.build_phases.find do |phase|
  phase.is_a?(Xcodeproj::Project::Object::PBXShellScriptBuildPhase) &&
  phase.name == 'Copy Rust Library'
end

if existing_script
  runner_target.build_phases.delete(existing_script)
  project.save
  puts "Removed existing Rust library copy script"
else
  puts "No existing Rust library copy script found"
end
