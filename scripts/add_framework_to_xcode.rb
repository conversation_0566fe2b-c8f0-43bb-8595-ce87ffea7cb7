#!/usr/bin/env ruby

require 'xcodeproj'

# Path to the Xcode project
project_path = 'ios/Runner.xcodeproj'
framework_path = 'ios/Frameworks/TushenCore.framework'

# Open the project
project = Xcodeproj::Project.open(project_path)

# Get the main target
target = project.targets.find { |t| t.name == 'Runner' }

# Check if framework is already added
existing_framework = target.frameworks_build_phase.files.find do |file|
  file.file_ref && file.file_ref.path && file.file_ref.path.include?('TushenCore.framework')
end

if existing_framework
  puts "TushenCore.framework is already added to the project"
else
  # Add the framework to the project
  framework_ref = project.new_file(framework_path)
  
  # Add to the target
  target.frameworks_build_phase.add_file_reference(framework_ref)
  
  # Set the framework as embedded
  embed_frameworks_phase = target.build_phases.find { |phase| phase.class == Xcodeproj::Project::Object::PBXCopyFilesBuildPhase && phase.name == 'Embed Frameworks' }
  
  if embed_frameworks_phase.nil?
    embed_frameworks_phase = target.new_copy_files_build_phase('Embed Frameworks')
    embed_frameworks_phase.dst_subfolder_spec = '10' # Frameworks
  end
  
  embed_frameworks_phase.add_file_reference(framework_ref)
  
  puts "Added TushenCore.framework to the project"
end

# Add framework search paths
target.build_configurations.each do |config|
  framework_search_paths = config.build_settings['FRAMEWORK_SEARCH_PATHS'] || []
  framework_search_paths = [framework_search_paths] unless framework_search_paths.is_a?(Array)

  # Ensure $(inherited) is first
  unless framework_search_paths.include?('$(inherited)')
    framework_search_paths.unshift('$(inherited)')
  end

  new_path = '$(PROJECT_DIR)/Frameworks'
  unless framework_search_paths.include?(new_path)
    framework_search_paths << new_path
    config.build_settings['FRAMEWORK_SEARCH_PATHS'] = framework_search_paths
    puts "Added framework search path: #{new_path}"
  end
end

# Save the project
project.save

puts "Project updated successfully!"
