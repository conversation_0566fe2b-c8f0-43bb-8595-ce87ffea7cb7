/// Filter types available for image editing
enum FilterType {
  // Basic filters
  vintage,
  blackWhite,
  sepia,
  blur,
  sharpen,

  // Adjustment filters
  brightness,
  contrast,
  saturation,
  hue,
  gamma,

  // Artistic filters
  oilPainting,
  watercolor,
  sketch,
  cartoon,

  // Portrait filters
  skinSmooth,
  eyeBrighten,
  teethWhiten,

  // Landscape filters
  skyEnhance,
  foliageBoost,
  sunsetWarm,

  // Architecture filters
  structureEnhance,
  perspectiveCorrect,

  // Food filters
  foodVibrant,
  warmTone,
}

/// Filter category for organization
enum FilterCategory {
  basic,
  adjustment,
  artistic,
  portrait,
  landscape,
  architecture,
  food,
}

/// Editing step type
enum EditingStepType {
  filter,
  brightness,
  contrast,
  saturation,
  rotation,
  crop,
  resize,
}

/// Represents a single editing step in the history
class EditingStep {
  final EditingStepType type;
  final FilterType? filterType;
  final double? filterIntensity;
  final double? brightness;
  final double? contrast;
  final double? saturation;
  final double? rotation;
  final Map<String, dynamic>? customData;

  const EditingStep({
    required this.type,
    this.filterType,
    this.filterIntensity,
    this.brightness,
    this.contrast,
    this.saturation,
    this.rotation,
    this.customData,
  });

  @override
  String toString() {
    return 'EditingStep(type: $type, filterType: $filterType, filterIntensity: $filterIntensity, brightness: $brightness, contrast: $contrast, saturation: $saturation, rotation: $rotation)';
  }
}

/// Filter information for UI display
class FilterInfo {
  final FilterType type;
  final String name;
  final String description;
  final FilterCategory category;
  final String? iconPath;

  const FilterInfo({
    required this.type,
    required this.name,
    required this.description,
    required this.category,
    this.iconPath,
  });
}

/// Available filters with their display information
class FilterRegistry {
  static const List<FilterInfo> availableFilters = [
    // Basic filters
    FilterInfo(
      type: FilterType.vintage,
      name: '复古',
      description: '温暖的复古色调',
      category: FilterCategory.basic,
    ),
    FilterInfo(
      type: FilterType.blackWhite,
      name: '黑白',
      description: '经典黑白效果',
      category: FilterCategory.basic,
    ),
    FilterInfo(
      type: FilterType.sepia,
      name: '棕褐色',
      description: '怀旧棕褐色调',
      category: FilterCategory.basic,
    ),
    FilterInfo(
      type: FilterType.blur,
      name: '模糊',
      description: '柔和模糊效果',
      category: FilterCategory.basic,
    ),
    FilterInfo(
      type: FilterType.sharpen,
      name: '锐化',
      description: '增强图像清晰度',
      category: FilterCategory.basic,
    ),

    // Artistic filters
    FilterInfo(
      type: FilterType.oilPainting,
      name: '油画',
      description: '油画艺术效果',
      category: FilterCategory.artistic,
    ),
    FilterInfo(
      type: FilterType.watercolor,
      name: '水彩',
      description: '水彩画效果',
      category: FilterCategory.artistic,
    ),
    FilterInfo(
      type: FilterType.sketch,
      name: '素描',
      description: '铅笔素描效果',
      category: FilterCategory.artistic,
    ),
    FilterInfo(
      type: FilterType.cartoon,
      name: '卡通',
      description: '卡通风格效果',
      category: FilterCategory.artistic,
    ),

    // Portrait filters
    FilterInfo(
      type: FilterType.skinSmooth,
      name: '磨皮',
      description: '平滑肌肤效果',
      category: FilterCategory.portrait,
    ),
    FilterInfo(
      type: FilterType.eyeBrighten,
      name: '亮眼',
      description: '增亮眼部效果',
      category: FilterCategory.portrait,
    ),
    FilterInfo(
      type: FilterType.teethWhiten,
      name: '美白牙齿',
      description: '牙齿美白效果',
      category: FilterCategory.portrait,
    ),

    // Landscape filters
    FilterInfo(
      type: FilterType.skyEnhance,
      name: '天空增强',
      description: '增强天空色彩',
      category: FilterCategory.landscape,
    ),
    FilterInfo(
      type: FilterType.foliageBoost,
      name: '植被增强',
      description: '增强绿色植被',
      category: FilterCategory.landscape,
    ),
    FilterInfo(
      type: FilterType.sunsetWarm,
      name: '日落暖调',
      description: '温暖日落色调',
      category: FilterCategory.landscape,
    ),

    // Food filters
    FilterInfo(
      type: FilterType.foodVibrant,
      name: '美食鲜艳',
      description: '增强食物色彩',
      category: FilterCategory.food,
    ),
    FilterInfo(
      type: FilterType.warmTone,
      name: '暖色调',
      description: '温暖色调效果',
      category: FilterCategory.food,
    ),
  ];

  /// Get filters by category
  static List<FilterInfo> getFiltersByCategory(FilterCategory category) {
    return availableFilters
        .where((filter) => filter.category == category)
        .toList();
  }

  /// Get filter info by type
  static FilterInfo? getFilterInfo(FilterType type) {
    try {
      return availableFilters.firstWhere((filter) => filter.type == type);
    } catch (e) {
      return null;
    }
  }

  /// Get all categories
  static List<FilterCategory> getAllCategories() {
    return FilterCategory.values;
  }

  /// Get category display name
  static String getCategoryName(FilterCategory category) {
    switch (category) {
      case FilterCategory.basic:
        return '基础';
      case FilterCategory.adjustment:
        return '调整';
      case FilterCategory.artistic:
        return '艺术';
      case FilterCategory.portrait:
        return '人像';
      case FilterCategory.landscape:
        return '风景';
      case FilterCategory.architecture:
        return '建筑';
      case FilterCategory.food:
        return '美食';
    }
  }
}

/// Image editing state
class ImageEditingState {
  final bool isLoading;
  final String? error;
  final bool hasChanges;
  final FilterType? selectedFilter;
  final double filterIntensity;
  final double brightness;
  final double contrast;
  final double saturation;
  final double rotation;

  const ImageEditingState({
    this.isLoading = false,
    this.error,
    this.hasChanges = false,
    this.selectedFilter,
    this.filterIntensity = 1.0,
    this.brightness = 0.0,
    this.contrast = 1.0,
    this.saturation = 1.0,
    this.rotation = 0.0,
  });

  ImageEditingState copyWith({
    bool? isLoading,
    String? error,
    bool? hasChanges,
    FilterType? selectedFilter,
    double? filterIntensity,
    double? brightness,
    double? contrast,
    double? saturation,
    double? rotation,
  }) {
    return ImageEditingState(
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      hasChanges: hasChanges ?? this.hasChanges,
      selectedFilter: selectedFilter ?? this.selectedFilter,
      filterIntensity: filterIntensity ?? this.filterIntensity,
      brightness: brightness ?? this.brightness,
      contrast: contrast ?? this.contrast,
      saturation: saturation ?? this.saturation,
      rotation: rotation ?? this.rotation,
    );
  }
}
