import 'package:flutter/material.dart';

/// Adjustment tools for image editing
class AdjustmentTools extends StatelessWidget {
  final double brightness;
  final double contrast;
  final double saturation;
  final ValueChanged<double> onBrightnessChanged;
  final ValueChanged<double> onContrastChanged;
  final ValueChanged<double> onSaturationChanged;
  final VoidCallback? onReset;

  const AdjustmentTools({
    super.key,
    required this.brightness,
    required this.contrast,
    required this.saturation,
    required this.onBrightnessChanged,
    required this.onContrastChanged,
    required this.onSaturationChanged,
    this.onReset,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: colorScheme.surface,
        border: Border(
          top: BorderSide(color: colorScheme.outline.withValues(alpha: 0.2)),
        ),
      ),
      child: Column(
        children: [
          // Header with reset button
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '调整',
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (onReset != null)
                  TextButton(
                    onPressed: onReset,
                    child: const Text('重置'),
                  ),
              ],
            ),
          ),
          
          // Adjustment sliders
          Expanded(
            child: ListView(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              children: [
                _buildAdjustmentSlider(
                  context: context,
                  icon: Icons.brightness_6,
                  label: '亮度',
                  value: brightness,
                  min: -100.0,
                  max: 100.0,
                  divisions: 40,
                  onChanged: onBrightnessChanged,
                ),
                const SizedBox(height: 16),
                
                _buildAdjustmentSlider(
                  context: context,
                  icon: Icons.contrast,
                  label: '对比度',
                  value: contrast,
                  min: 0.0,
                  max: 2.0,
                  divisions: 40,
                  onChanged: onContrastChanged,
                ),
                const SizedBox(height: 16),
                
                _buildAdjustmentSlider(
                  context: context,
                  icon: Icons.palette,
                  label: '饱和度',
                  value: saturation,
                  min: 0.0,
                  max: 2.0,
                  divisions: 40,
                  onChanged: onSaturationChanged,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdjustmentSlider({
    required BuildContext context,
    required IconData icon,
    required String label,
    required double value,
    required double min,
    required double max,
    required int divisions,
    required ValueChanged<double> onChanged,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Format value for display
    String formatValue(double val) {
      if (label == '亮度') {
        return val >= 0 ? '+${val.round()}' : '${val.round()}';
      } else {
        return '${(val * 100).round()}%';
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              color: colorScheme.onSurface.withValues(alpha: 0.6),
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: theme.textTheme.labelMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const Spacer(),
            Text(
              formatValue(value),
              style: theme.textTheme.labelMedium?.copyWith(
                color: colorScheme.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            trackHeight: 4,
            thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
            overlayShape: const RoundSliderOverlayShape(overlayRadius: 16),
          ),
          child: Slider(
            value: value,
            min: min,
            max: max,
            divisions: divisions,
            onChanged: onChanged,
          ),
        ),
      ],
    );
  }
}

/// Quick adjustment buttons for common operations
class QuickAdjustmentButtons extends StatelessWidget {
  final VoidCallback? onAutoEnhance;
  final VoidCallback? onRotateLeft;
  final VoidCallback? onRotateRight;
  final VoidCallback? onFlipHorizontal;
  final VoidCallback? onFlipVertical;

  const QuickAdjustmentButtons({
    super.key,
    this.onAutoEnhance,
    this.onRotateLeft,
    this.onRotateRight,
    this.onFlipHorizontal,
    this.onFlipVertical,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      height: 80,
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        children: [
          if (onAutoEnhance != null)
            _buildQuickButton(
              context: context,
              icon: Icons.auto_fix_high,
              label: '自动增强',
              onTap: onAutoEnhance!,
            ),
          
          if (onRotateLeft != null)
            _buildQuickButton(
              context: context,
              icon: Icons.rotate_left,
              label: '左转',
              onTap: onRotateLeft!,
            ),
          
          if (onRotateRight != null)
            _buildQuickButton(
              context: context,
              icon: Icons.rotate_right,
              label: '右转',
              onTap: onRotateRight!,
            ),
          
          if (onFlipHorizontal != null)
            _buildQuickButton(
              context: context,
              icon: Icons.flip,
              label: '水平翻转',
              onTap: onFlipHorizontal!,
            ),
          
          if (onFlipVertical != null)
            _buildQuickButton(
              context: context,
              icon: Icons.flip,
              label: '垂直翻转',
              onTap: onFlipVertical!,
            ),
        ],
      ),
    );
  }

  Widget _buildQuickButton({
    required BuildContext context,
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Padding(
      padding: const EdgeInsets.only(right: 16),
      child: GestureDetector(
        onTap: onTap,
        child: Column(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: colorScheme.onSurface.withValues(alpha: 0.8),
                size: 20,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: theme.textTheme.labelSmall?.copyWith(
                color: colorScheme.onSurface.withValues(alpha: 0.8),
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
