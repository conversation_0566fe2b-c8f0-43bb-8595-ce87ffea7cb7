import 'package:flutter/material.dart';
import '../models/collage_models.dart';
import '../../../core/design_system/theme.dart';

/// Layout selector widget for collage creation
class LayoutSelector extends StatelessWidget {
  final CollageLayoutConfig? selectedLayout;
  final ValueChanged<CollageLayoutConfig> onLayoutSelected;
  final int imageCount;

  const LayoutSelector({
    super.key,
    this.selectedLayout,
    required this.onLayoutSelected,
    required this.imageCount,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final availableLayouts = CollageLayoutConfig.availableLayouts
        .where((layout) => layout.maxImages >= imageCount)
        .toList();

    return Container(
      height: 120,
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              '选择布局',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: availableLayouts.length,
              itemBuilder: (context, index) {
                final layout = availableLayouts[index];
                final isSelected = selectedLayout?.type == layout.type;
                
                return Padding(
                  padding: const EdgeInsets.only(right: 12),
                  child: _LayoutCard(
                    layout: layout,
                    isSelected: isSelected,
                    onTap: () => onLayoutSelected(layout),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

class _LayoutCard extends StatelessWidget {
  final CollageLayoutConfig layout;
  final bool isSelected;
  final VoidCallback onTap;

  const _LayoutCard({
    required this.layout,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: 80,
        decoration: BoxDecoration(
          color: isSelected 
              ? colorScheme.primary.withOpacity(0.1)
              : colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected 
                ? colorScheme.primary
                : colorScheme.outline.withOpacity(0.3),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Layout preview
            Container(
              width: 40,
              height: 40,
              margin: const EdgeInsets.only(bottom: 8),
              child: _LayoutPreview(layout: layout),
            ),
            
            // Layout name
            Text(
              layout.name,
              style: theme.textTheme.labelSmall?.copyWith(
                color: isSelected 
                    ? colorScheme.primary
                    : colorScheme.onSurface,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}

class _LayoutPreview extends StatelessWidget {
  final CollageLayoutConfig layout;

  const _LayoutPreview({required this.layout});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    
    return CustomPaint(
      size: const Size(40, 40),
      painter: _LayoutPreviewPainter(
        layout: layout,
        color: colorScheme.primary,
        backgroundColor: colorScheme.surface,
      ),
    );
  }
}

class _LayoutPreviewPainter extends CustomPainter {
  final CollageLayoutConfig layout;
  final Color color;
  final Color backgroundColor;

  _LayoutPreviewPainter({
    required this.layout,
    required this.color,
    required this.backgroundColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final strokePaint = Paint()
      ..color = color.withOpacity(0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    // Draw background
    final backgroundPaint = Paint()
      ..color = backgroundColor
      ..style = PaintingStyle.fill;
    
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(0, 0, size.width, size.height),
        const Radius.circular(4),
      ),
      backgroundPaint,
    );

    // Draw layout regions
    for (final region in layout.regions.take(6)) { // Limit to 6 regions for preview
      final rect = Rect.fromLTWH(
        region.x * size.width / 100,
        region.y * size.height / 100,
        region.width * size.width / 100,
        region.height * size.height / 100,
      );

      // Fill region
      canvas.drawRRect(
        RRect.fromRectAndRadius(rect, Radius.circular(region.borderRadius * 0.5)),
        paint..color = color.withOpacity(0.6),
      );

      // Draw border
      canvas.drawRRect(
        RRect.fromRectAndRadius(rect, Radius.circular(region.borderRadius * 0.5)),
        strokePaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is! _LayoutPreviewPainter ||
           oldDelegate.layout != layout ||
           oldDelegate.color != color ||
           oldDelegate.backgroundColor != backgroundColor;
  }
}

/// Layout info widget showing details about selected layout
class LayoutInfo extends StatelessWidget {
  final CollageLayoutConfig layout;

  const LayoutInfo({
    super.key,
    required this.layout,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: colorScheme.primary.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.primary.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Icon(
            layout.icon,
            color: colorScheme.primary,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  layout.name,
                  style: theme.textTheme.titleSmall?.copyWith(
                    color: colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  layout.description,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              '最多 ${layout.maxImages} 张',
              style: theme.textTheme.labelSmall?.copyWith(
                color: colorScheme.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
