import 'package:flutter/material.dart';
import '../models/collage_models.dart';
import '../../../core/design_system/theme.dart';

/// Style editor widget for customizing collage appearance
class StyleEditor extends StatelessWidget {
  final CollageStyle style;
  final ValueChanged<CollageStyle> onStyleChanged;

  const StyleEditor({
    super.key,
    required this.style,
    required this.onStyleChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '样式设置',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          
          // Background color
          _BackgroundColorPicker(
            selectedColor: style.backgroundColor,
            onColorChanged: (color) {
              onStyleChanged(style.copyWith(backgroundColor: color));
            },
          ),
          
          const SizedBox(height: 20),
          
          // Spacing
          _SpacingSlider(
            spacing: style.spacing,
            onSpacingChanged: (spacing) {
              onStyleChanged(style.copyWith(spacing: spacing));
            },
          ),
          
          const SizedBox(height: 20),
          
          // Border settings
          _BorderSettings(
            borderWidth: style.borderWidth,
            borderColor: style.borderColor,
            onBorderChanged: (width, color) {
              onStyleChanged(style.copyWith(
                borderWidth: width,
                borderColor: color,
              ));
            },
          ),
          
          const SizedBox(height: 20),
          
          // Corner radius
          _CornerRadiusSlider(
            cornerRadius: style.cornerRadius,
            onRadiusChanged: (radius) {
              onStyleChanged(style.copyWith(cornerRadius: radius));
            },
          ),
          
          const SizedBox(height: 20),
          
          // Shadow toggle
          _ShadowToggle(
            hasShadow: style.hasShadow,
            onShadowChanged: (hasShadow) {
              onStyleChanged(style.copyWith(hasShadow: hasShadow));
            },
          ),
        ],
      ),
    );
  }
}

class _BackgroundColorPicker extends StatelessWidget {
  final Color selectedColor;
  final ValueChanged<Color> onColorChanged;

  const _BackgroundColorPicker({
    required this.selectedColor,
    required this.onColorChanged,
  });

  static const List<Color> _predefinedColors = [
    Colors.white,
    Colors.black,
    Colors.grey,
    Color(0xFFF5F5F5),
    Color(0xFFE3F2FD),
    Color(0xFFE8F5E8),
    Color(0xFFFFF3E0),
    Color(0xFFFCE4EC),
    Color(0xFFF3E5F5),
    Color(0xFFE0F2F1),
  ];

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '背景颜色',
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _predefinedColors.map((color) {
            final isSelected = color.value == selectedColor.value;
            return GestureDetector(
              onTap: () => onColorChanged(color),
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isSelected 
                        ? theme.colorScheme.primary
                        : Colors.grey.withOpacity(0.3),
                    width: isSelected ? 3 : 1,
                  ),
                ),
                child: isSelected
                    ? Icon(
                        Icons.check,
                        color: color == Colors.white || color == Color(0xFFF5F5F5)
                            ? Colors.black
                            : Colors.white,
                        size: 20,
                      )
                    : null,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
}

class _SpacingSlider extends StatelessWidget {
  final double spacing;
  final ValueChanged<double> onSpacingChanged;

  const _SpacingSlider({
    required this.spacing,
    required this.onSpacingChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '间距',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              '${spacing.round()}px',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Slider(
          value: spacing,
          min: 0,
          max: 20,
          divisions: 20,
          onChanged: onSpacingChanged,
        ),
      ],
    );
  }
}

class _BorderSettings extends StatelessWidget {
  final double borderWidth;
  final Color borderColor;
  final Function(double, Color) onBorderChanged;

  const _BorderSettings({
    required this.borderWidth,
    required this.borderColor,
    required this.onBorderChanged,
  });

  static const List<Color> _borderColors = [
    Colors.transparent,
    Colors.black,
    Colors.white,
    Colors.grey,
    Color(0xFF2196F3),
    Color(0xFF4CAF50),
    Color(0xFFFF9800),
    Color(0xFFE91E63),
  ];

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '边框',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              '${borderWidth.round()}px',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Slider(
          value: borderWidth,
          min: 0,
          max: 10,
          divisions: 10,
          onChanged: (width) => onBorderChanged(width, borderColor),
        ),
        if (borderWidth > 0) ...[
          const SizedBox(height: 12),
          Text(
            '边框颜色',
            style: theme.textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: _borderColors.map((color) {
              final isSelected = color.value == borderColor.value;
              return GestureDetector(
                onTap: () => onBorderChanged(borderWidth, color),
                child: Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: color == Colors.transparent ? Colors.white : color,
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(
                      color: isSelected 
                          ? theme.colorScheme.primary
                          : Colors.grey.withOpacity(0.3),
                      width: isSelected ? 2 : 1,
                    ),
                  ),
                  child: color == Colors.transparent
                      ? Icon(
                          Icons.block,
                          color: Colors.red,
                          size: 16,
                        )
                      : isSelected
                          ? Icon(
                              Icons.check,
                              color: color == Colors.white ? Colors.black : Colors.white,
                              size: 16,
                            )
                          : null,
                ),
              );
            }).toList(),
          ),
        ],
      ],
    );
  }
}

class _CornerRadiusSlider extends StatelessWidget {
  final double cornerRadius;
  final ValueChanged<double> onRadiusChanged;

  const _CornerRadiusSlider({
    required this.cornerRadius,
    required this.onRadiusChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '圆角',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              '${cornerRadius.round()}px',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Slider(
          value: cornerRadius,
          min: 0,
          max: 20,
          divisions: 20,
          onChanged: onRadiusChanged,
        ),
      ],
    );
  }
}

class _ShadowToggle extends StatelessWidget {
  final bool hasShadow;
  final ValueChanged<bool> onShadowChanged;

  const _ShadowToggle({
    required this.hasShadow,
    required this.onShadowChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          '阴影效果',
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        Switch(
          value: hasShadow,
          onChanged: onShadowChanged,
        ),
      ],
    );
  }
}
