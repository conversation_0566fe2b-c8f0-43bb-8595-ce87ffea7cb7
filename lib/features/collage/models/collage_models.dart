import 'package:flutter/material.dart';

/// Collage layout types
enum CollageLayoutType {
  grid,
  mosaic,
  freeform,
  magazine,
  polaroid,
}

/// Collage layout configuration
class CollageLayoutConfig {
  final CollageLayoutType type;
  final String name;
  final String description;
  final IconData icon;
  final int maxImages;
  final double aspectRatio;
  final List<CollageRegion> regions;

  const CollageLayoutConfig({
    required this.type,
    required this.name,
    required this.description,
    required this.icon,
    required this.maxImages,
    required this.aspectRatio,
    required this.regions,
  });

  static List<CollageLayoutConfig> get availableLayouts => [
    // Grid layouts
    CollageLayoutConfig(
      type: CollageLayoutType.grid,
      name: '网格布局',
      description: '经典网格排列',
      icon: Icons.grid_view,
      maxImages: 9,
      aspectRatio: 1.0,
      regions: _generateGridRegions(3, 3),
    ),
    
    // Mosaic layout
    CollageLayoutConfig(
      type: CollageLayoutType.mosaic,
      name: '马赛克布局',
      description: '不规则拼接',
      icon: Icons.view_module,
      maxImages: 6,
      aspectRatio: 1.2,
      regions: _generateMosaicRegions(),
    ),
    
    // Magazine layout
    CollageLayoutConfig(
      type: CollageLayoutType.magazine,
      name: '杂志布局',
      description: '杂志风格排版',
      icon: Icons.article,
      maxImages: 5,
      aspectRatio: 1.4,
      regions: _generateMagazineRegions(),
    ),
    
    // Polaroid layout
    CollageLayoutConfig(
      type: CollageLayoutType.polaroid,
      name: '拍立得风格',
      description: '复古拍立得效果',
      icon: Icons.photo_camera_back,
      maxImages: 4,
      aspectRatio: 1.1,
      regions: _generatePolaroidRegions(),
    ),
    
    // Freeform layout
    CollageLayoutConfig(
      type: CollageLayoutType.freeform,
      name: '自由布局',
      description: '自由拖拽排列',
      icon: Icons.open_with,
      maxImages: 8,
      aspectRatio: 1.0,
      regions: _generateFreeformRegions(),
    ),
  ];

  static List<CollageRegion> _generateGridRegions(int rows, int cols) {
    final regions = <CollageRegion>[];
    final cellWidth = 100.0 / cols;
    final cellHeight = 100.0 / rows;
    
    for (int row = 0; row < rows; row++) {
      for (int col = 0; col < cols; col++) {
        regions.add(CollageRegion(
          x: col * cellWidth,
          y: row * cellHeight,
          width: cellWidth,
          height: cellHeight,
          rotation: 0,
          borderRadius: 4.0,
        ));
      }
    }
    return regions;
  }

  static List<CollageRegion> _generateMosaicRegions() {
    return [
      const CollageRegion(x: 0, y: 0, width: 60, height: 40, rotation: 0),
      const CollageRegion(x: 60, y: 0, width: 40, height: 60, rotation: 0),
      const CollageRegion(x: 0, y: 40, width: 30, height: 30, rotation: 0),
      const CollageRegion(x: 30, y: 40, width: 30, height: 30, rotation: 0),
      const CollageRegion(x: 0, y: 70, width: 60, height: 30, rotation: 0),
      const CollageRegion(x: 60, y: 60, width: 40, height: 40, rotation: 0),
    ];
  }

  static List<CollageRegion> _generateMagazineRegions() {
    return [
      const CollageRegion(x: 0, y: 0, width: 70, height: 50, rotation: 0),
      const CollageRegion(x: 70, y: 0, width: 30, height: 25, rotation: 0),
      const CollageRegion(x: 70, y: 25, width: 30, height: 25, rotation: 0),
      const CollageRegion(x: 0, y: 50, width: 50, height: 50, rotation: 0),
      const CollageRegion(x: 50, y: 50, width: 50, height: 50, rotation: 0),
    ];
  }

  static List<CollageRegion> _generatePolaroidRegions() {
    return [
      const CollageRegion(x: 5, y: 5, width: 40, height: 40, rotation: -5, borderRadius: 8.0),
      const CollageRegion(x: 55, y: 10, width: 40, height: 40, rotation: 3, borderRadius: 8.0),
      const CollageRegion(x: 10, y: 55, width: 40, height: 40, rotation: 2, borderRadius: 8.0),
      const CollageRegion(x: 50, y: 60, width: 40, height: 40, rotation: -3, borderRadius: 8.0),
    ];
  }

  static List<CollageRegion> _generateFreeformRegions() {
    return [
      const CollageRegion(x: 10, y: 10, width: 35, height: 35, rotation: 0),
      const CollageRegion(x: 55, y: 5, width: 35, height: 35, rotation: 0),
      const CollageRegion(x: 5, y: 55, width: 35, height: 35, rotation: 0),
      const CollageRegion(x: 50, y: 50, width: 35, height: 35, rotation: 0),
      const CollageRegion(x: 25, y: 30, width: 30, height: 30, rotation: 0),
    ];
  }
}

/// Collage region definition
class CollageRegion {
  final double x;
  final double y;
  final double width;
  final double height;
  final double rotation;
  final double borderRadius;

  const CollageRegion({
    required this.x,
    required this.y,
    required this.width,
    required this.height,
    this.rotation = 0,
    this.borderRadius = 4.0,
  });

  CollageRegion copyWith({
    double? x,
    double? y,
    double? width,
    double? height,
    double? rotation,
    double? borderRadius,
  }) {
    return CollageRegion(
      x: x ?? this.x,
      y: y ?? this.y,
      width: width ?? this.width,
      height: height ?? this.height,
      rotation: rotation ?? this.rotation,
      borderRadius: borderRadius ?? this.borderRadius,
    );
  }
}

/// Collage image item
class CollageImageItem {
  final String imagePath;
  final CollageRegion region;
  final double scale;
  final Offset offset;
  final bool isSelected;

  const CollageImageItem({
    required this.imagePath,
    required this.region,
    this.scale = 1.0,
    this.offset = Offset.zero,
    this.isSelected = false,
  });

  CollageImageItem copyWith({
    String? imagePath,
    CollageRegion? region,
    double? scale,
    Offset? offset,
    bool? isSelected,
  }) {
    return CollageImageItem(
      imagePath: imagePath ?? this.imagePath,
      region: region ?? this.region,
      scale: scale ?? this.scale,
      offset: offset ?? this.offset,
      isSelected: isSelected ?? this.isSelected,
    );
  }
}

/// Collage style configuration
class CollageStyle {
  final Color backgroundColor;
  final double spacing;
  final double borderWidth;
  final Color borderColor;
  final double cornerRadius;
  final bool hasShadow;

  const CollageStyle({
    this.backgroundColor = Colors.white,
    this.spacing = 8.0,
    this.borderWidth = 0.0,
    this.borderColor = Colors.transparent,
    this.cornerRadius = 8.0,
    this.hasShadow = false,
  });

  CollageStyle copyWith({
    Color? backgroundColor,
    double? spacing,
    double? borderWidth,
    Color? borderColor,
    double? cornerRadius,
    bool? hasShadow,
  }) {
    return CollageStyle(
      backgroundColor: backgroundColor ?? this.backgroundColor,
      spacing: spacing ?? this.spacing,
      borderWidth: borderWidth ?? this.borderWidth,
      borderColor: borderColor ?? this.borderColor,
      cornerRadius: cornerRadius ?? this.cornerRadius,
      hasShadow: hasShadow ?? this.hasShadow,
    );
  }
}
