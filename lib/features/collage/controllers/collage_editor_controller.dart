import 'package:flutter/material.dart';
import '../models/collage_models.dart';
import '../../../core/services/image_service.dart';

/// Collage editor controller
class CollageEditorController extends ChangeNotifier {
  final ImageService _imageService;

  CollageEditorController(this._imageService);

  // State
  bool _isLoading = false;
  String? _error;
  List<String> _imagePaths = [];
  CollageLayoutConfig? _selectedLayout;
  List<CollageImageItem> _collageItems = [];
  CollageStyle _style = const CollageStyle();
  int? _selectedItemIndex;
  bool _isPreviewMode = false;

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  List<String> get imagePaths => _imagePaths;
  CollageLayoutConfig? get selectedLayout => _selectedLayout;
  List<CollageImageItem> get collageItems => _collageItems;
  CollageStyle get style => _style;
  int? get selectedItemIndex => _selectedItemIndex;
  bool get isPreviewMode => _isPreviewMode;
  bool get canSave => _collageItems.isNotEmpty && _selectedLayout != null;

  /// Initialize with image paths
  void initialize(List<String> imagePaths) {
    _imagePaths = imagePaths;
    _selectedLayout = null;
    _collageItems.clear();
    _selectedItemIndex = null;
    _isPreviewMode = false;
    _clearError();
    notifyListeners();
  }

  /// Select layout
  void selectLayout(CollageLayoutConfig layout) {
    _selectedLayout = layout;
    _generateCollageItems();
    notifyListeners();
  }

  /// Generate collage items based on selected layout
  void _generateCollageItems() {
    if (_selectedLayout == null) return;

    _collageItems.clear();
    final regions = _selectedLayout!.regions;
    final maxImages = _selectedLayout!.maxImages;

    for (
      int i = 0;
      i < regions.length && i < maxImages && i < _imagePaths.length;
      i++
    ) {
      _collageItems.add(
        CollageImageItem(imagePath: _imagePaths[i], region: regions[i]),
      );
    }
  }

  /// Select collage item
  void selectItem(int index) {
    if (index >= 0 && index < _collageItems.length) {
      _selectedItemIndex = index;
      notifyListeners();
    }
  }

  /// Deselect current item
  void deselectItem() {
    _selectedItemIndex = null;
    notifyListeners();
  }

  /// Update item position
  void updateItemPosition(int index, Offset newOffset) {
    if (index >= 0 && index < _collageItems.length) {
      final item = _collageItems[index];
      _collageItems[index] = item.copyWith(offset: newOffset);
      notifyListeners();
    }
  }

  /// Update item scale
  void updateItemScale(int index, double newScale) {
    if (index >= 0 && index < _collageItems.length) {
      final item = _collageItems[index];
      _collageItems[index] = item.copyWith(scale: newScale.clamp(0.5, 3.0));
      notifyListeners();
    }
  }

  /// Update item region
  void updateItemRegion(int index, CollageRegion newRegion) {
    if (index >= 0 && index < _collageItems.length) {
      final item = _collageItems[index];
      _collageItems[index] = item.copyWith(region: newRegion);
      notifyListeners();
    }
  }

  /// Swap images between two positions
  void swapImages(int fromIndex, int toIndex) {
    if (fromIndex >= 0 &&
        fromIndex < _collageItems.length &&
        toIndex >= 0 &&
        toIndex < _collageItems.length &&
        fromIndex != toIndex) {
      final fromItem = _collageItems[fromIndex];
      final toItem = _collageItems[toIndex];

      _collageItems[fromIndex] = fromItem.copyWith(imagePath: toItem.imagePath);
      _collageItems[toIndex] = toItem.copyWith(imagePath: fromItem.imagePath);

      notifyListeners();
    }
  }

  /// Update collage style
  void updateStyle(CollageStyle newStyle) {
    _style = newStyle;
    notifyListeners();
  }

  /// Update background color
  void updateBackgroundColor(Color color) {
    _style = _style.copyWith(backgroundColor: color);
    notifyListeners();
  }

  /// Update spacing
  void updateSpacing(double spacing) {
    _style = _style.copyWith(spacing: spacing);
    notifyListeners();
  }

  /// Update border
  void updateBorder(double width, Color color) {
    _style = _style.copyWith(borderWidth: width, borderColor: color);
    notifyListeners();
  }

  /// Toggle preview mode
  void togglePreviewMode() {
    _isPreviewMode = !_isPreviewMode;
    if (_isPreviewMode) {
      _selectedItemIndex = null;
    }
    notifyListeners();
  }

  /// Add more images
  Future<void> addMoreImages() async {
    try {
      _setLoading(true);
      final imageDataList = await _imageService.pickMultipleImages();

      if (imageDataList.isNotEmpty) {
        final newPaths = imageDataList.map((img) => img.path).toList();
        _imagePaths.addAll(newPaths);

        // Regenerate collage items if layout is selected
        if (_selectedLayout != null) {
          _generateCollageItems();
        }
      }
    } catch (e) {
      _setError('Failed to add images: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Remove image
  void removeImage(int index) {
    if (index >= 0 && index < _imagePaths.length) {
      _imagePaths.removeAt(index);

      // Regenerate collage items if layout is selected
      if (_selectedLayout != null) {
        _generateCollageItems();
      }

      // Clear selection if removed item was selected
      if (_selectedItemIndex == index) {
        _selectedItemIndex = null;
      } else if (_selectedItemIndex != null && _selectedItemIndex! > index) {
        _selectedItemIndex = _selectedItemIndex! - 1;
      }

      notifyListeners();
    }
  }

  /// Save collage
  Future<ImageData?> saveCollage() async {
    if (!canSave) {
      _setError('Cannot save: no layout selected or no images');
      return null;
    }

    try {
      _setLoading(true);
      _clearError();

      // TODO: Implement actual collage creation using Rust backend
      // For now, return the first image as placeholder
      final firstImageData = await _imageService.loadImageFromPath(
        _imagePaths.first,
      );
      return firstImageData;
    } catch (e) {
      _setError('Failed to save collage: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  /// Reset to initial state
  void reset() {
    _imagePaths.clear();
    _selectedLayout = null;
    _collageItems.clear();
    _style = const CollageStyle();
    _selectedItemIndex = null;
    _isPreviewMode = false;
    _clearError();
    notifyListeners();
  }

  // Private methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  @override
  void dispose() {
    super.dispose();
  }
}
