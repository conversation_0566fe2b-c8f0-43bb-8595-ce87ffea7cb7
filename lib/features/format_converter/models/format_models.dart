/// Image format definitions and utilities
enum ImageFormat { jpeg, png, webp, heic, bmp, tiff, gif }

extension ImageFormatExtension on ImageFormat {
  /// Display name for the format
  String get displayName {
    switch (this) {
      case ImageFormat.jpeg:
        return 'JPEG';
      case ImageFormat.png:
        return 'PNG';
      case ImageFormat.webp:
        return 'WebP';
      case ImageFormat.heic:
        return 'HEIC';
      case ImageFormat.bmp:
        return 'BMP';
      case ImageFormat.tiff:
        return 'TIFF';
      case ImageFormat.gif:
        return 'GIF';
    }
  }

  /// File extension for the format
  String get extension {
    switch (this) {
      case ImageFormat.jpeg:
        return 'jpg';
      case ImageFormat.png:
        return 'png';
      case ImageFormat.webp:
        return 'webp';
      case ImageFormat.heic:
        return 'heic';
      case ImageFormat.bmp:
        return 'bmp';
      case ImageFormat.tiff:
        return 'tiff';
      case ImageFormat.gif:
        return 'gif';
    }
  }

  /// MIME type for the format
  String get mimeType {
    switch (this) {
      case ImageFormat.jpeg:
        return 'image/jpeg';
      case ImageFormat.png:
        return 'image/png';
      case ImageFormat.webp:
        return 'image/webp';
      case ImageFormat.heic:
        return 'image/heic';
      case ImageFormat.bmp:
        return 'image/bmp';
      case ImageFormat.tiff:
        return 'image/tiff';
      case ImageFormat.gif:
        return 'image/gif';
    }
  }

  /// Description of the format
  String get description {
    switch (this) {
      case ImageFormat.jpeg:
        return '有损压缩，适合照片';
      case ImageFormat.png:
        return '无损压缩，支持透明';
      case ImageFormat.webp:
        return '现代格式，高压缩率';
      case ImageFormat.heic:
        return 'Apple格式，高效压缩';
      case ImageFormat.bmp:
        return '无压缩，文件较大';
      case ImageFormat.tiff:
        return '专业格式，支持多层';
      case ImageFormat.gif:
        return '支持动画，256色';
    }
  }

  /// Whether the format supports quality settings
  bool get supportsQuality {
    switch (this) {
      case ImageFormat.jpeg:
      case ImageFormat.webp:
      case ImageFormat.heic:
        return true;
      case ImageFormat.png:
      case ImageFormat.bmp:
      case ImageFormat.tiff:
      case ImageFormat.gif:
        return false;
    }
  }

  /// Whether the format supports transparency
  bool get supportsTransparency {
    switch (this) {
      case ImageFormat.png:
      case ImageFormat.webp:
      case ImageFormat.gif:
        return true;
      case ImageFormat.jpeg:
      case ImageFormat.heic:
      case ImageFormat.bmp:
      case ImageFormat.tiff:
        return false;
    }
  }

  /// Whether the format supports animation
  bool get supportsAnimation {
    switch (this) {
      case ImageFormat.gif:
      case ImageFormat.webp:
        return true;
      case ImageFormat.jpeg:
      case ImageFormat.png:
      case ImageFormat.heic:
      case ImageFormat.bmp:
      case ImageFormat.tiff:
        return false;
    }
  }

  /// Typical compression ratio compared to uncompressed
  double get typicalCompressionRatio {
    switch (this) {
      case ImageFormat.jpeg:
        return 0.1; // 10% of original size
      case ImageFormat.png:
        return 0.3; // 30% of original size
      case ImageFormat.webp:
        return 0.08; // 8% of original size
      case ImageFormat.heic:
        return 0.05; // 5% of original size
      case ImageFormat.bmp:
        return 1.0; // No compression
      case ImageFormat.tiff:
        return 0.8; // Minimal compression
      case ImageFormat.gif:
        return 0.4; // 40% of original size
    }
  }
}

extension ImageFormatUtils on ImageFormat {
  /// Get format from file extension
  static ImageFormat? fromExtension(String extension) {
    final ext = extension.toLowerCase().replaceAll('.', '');
    switch (ext) {
      case 'jpg':
      case 'jpeg':
        return ImageFormat.jpeg;
      case 'png':
        return ImageFormat.png;
      case 'webp':
        return ImageFormat.webp;
      case 'heic':
      case 'heif':
        return ImageFormat.heic;
      case 'bmp':
        return ImageFormat.bmp;
      case 'tiff':
      case 'tif':
        return ImageFormat.tiff;
      case 'gif':
        return ImageFormat.gif;
      default:
        return null;
    }
  }

  /// Get format from MIME type
  static ImageFormat? fromMimeType(String mimeType) {
    switch (mimeType.toLowerCase()) {
      case 'image/jpeg':
        return ImageFormat.jpeg;
      case 'image/png':
        return ImageFormat.png;
      case 'image/webp':
        return ImageFormat.webp;
      case 'image/heic':
      case 'image/heif':
        return ImageFormat.heic;
      case 'image/bmp':
        return ImageFormat.bmp;
      case 'image/tiff':
        return ImageFormat.tiff;
      case 'image/gif':
        return ImageFormat.gif;
      default:
        return null;
    }
  }
}

/// Conversion options for image format conversion
class ConversionOptions {
  final ImageFormat targetFormat;
  final double quality;
  final double? scale;
  final bool preserveExif;
  final bool optimizeSize;
  final Map<String, dynamic> customOptions;

  const ConversionOptions({
    required this.targetFormat,
    this.quality = 0.9,
    this.scale,
    this.preserveExif = true,
    this.optimizeSize = false,
    this.customOptions = const {},
  });

  Map<String, dynamic> toJson() {
    return {
      'target_format': targetFormat.extension,
      'quality': quality,
      'scale': scale,
      'preserve_exif': preserveExif,
      'optimize_size': optimizeSize,
      'custom_options': customOptions,
    };
  }

  factory ConversionOptions.fromJson(Map<String, dynamic> json) {
    return ConversionOptions(
      targetFormat:
          ImageFormatUtils.fromExtension(json['target_format']) ??
          ImageFormat.jpeg,
      quality: (json['quality'] as num?)?.toDouble() ?? 0.9,
      scale: (json['scale'] as num?)?.toDouble(),
      preserveExif: json['preserve_exif'] as bool? ?? true,
      optimizeSize: json['optimize_size'] as bool? ?? false,
      customOptions: json['custom_options'] as Map<String, dynamic>? ?? {},
    );
  }

  ConversionOptions copyWith({
    ImageFormat? targetFormat,
    double? quality,
    double? scale,
    bool? preserveExif,
    bool? optimizeSize,
    Map<String, dynamic>? customOptions,
  }) {
    return ConversionOptions(
      targetFormat: targetFormat ?? this.targetFormat,
      quality: quality ?? this.quality,
      scale: scale ?? this.scale,
      preserveExif: preserveExif ?? this.preserveExif,
      optimizeSize: optimizeSize ?? this.optimizeSize,
      customOptions: customOptions ?? this.customOptions,
    );
  }
}

/// Result of image format conversion
class ConversionResult {
  final String outputPath;
  final ImageFormat sourceFormat;
  final ImageFormat targetFormat;
  final int originalSize;
  final int convertedSize;
  final double compressionRatio;
  final Duration processingTime;
  final Map<String, dynamic> metadata;

  const ConversionResult({
    required this.outputPath,
    required this.sourceFormat,
    required this.targetFormat,
    required this.originalSize,
    required this.convertedSize,
    required this.compressionRatio,
    required this.processingTime,
    this.metadata = const {},
  });

  factory ConversionResult.fromJson(Map<String, dynamic> json) {
    return ConversionResult(
      outputPath: json['output_path'] as String,
      sourceFormat:
          ImageFormatUtils.fromExtension(json['source_format']) ??
          ImageFormat.jpeg,
      targetFormat:
          ImageFormatUtils.fromExtension(json['target_format']) ??
          ImageFormat.jpeg,
      originalSize: json['original_size'] as int,
      convertedSize: json['converted_size'] as int,
      compressionRatio: (json['compression_ratio'] as num).toDouble(),
      processingTime: Duration(milliseconds: json['processing_time_ms'] as int),
      metadata: json['metadata'] as Map<String, dynamic>? ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'output_path': outputPath,
      'source_format': sourceFormat.extension,
      'target_format': targetFormat.extension,
      'original_size': originalSize,
      'converted_size': convertedSize,
      'compression_ratio': compressionRatio,
      'processing_time_ms': processingTime.inMilliseconds,
      'metadata': metadata,
    };
  }

  /// Calculate size reduction percentage
  double get sizeReductionPercentage {
    if (originalSize == 0) return 0.0;
    return ((originalSize - convertedSize) / originalSize) * 100;
  }

  /// Whether the conversion reduced file size
  bool get reducedSize => convertedSize < originalSize;

  /// Human readable size comparison
  String get sizeComparison {
    final originalMB = originalSize / (1024 * 1024);
    final convertedMB = convertedSize / (1024 * 1024);

    if (reducedSize) {
      return '${originalMB.toStringAsFixed(1)}MB → ${convertedMB.toStringAsFixed(1)}MB (减少${sizeReductionPercentage.toStringAsFixed(1)}%)';
    } else {
      final increasePercentage =
          ((convertedSize - originalSize) / originalSize) * 100;
      return '${originalMB.toStringAsFixed(1)}MB → ${convertedMB.toStringAsFixed(1)}MB (增加${increasePercentage.toStringAsFixed(1)}%)';
    }
  }
}
