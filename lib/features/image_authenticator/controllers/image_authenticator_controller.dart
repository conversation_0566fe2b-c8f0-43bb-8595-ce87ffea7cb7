import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../models/authentication_data.dart';
import '../../../core/services/image_processor_service.dart';
import '../../../core/services/storage_service.dart';

class ImageAuthenticatorController extends ChangeNotifier {
  final ImageProcessorService _imageProcessorService;
  final StorageService _storageService;
  final ImagePicker _imagePicker = ImagePicker();

  // State variables
  File? _selectedImage;
  AuthenticationData? _authenticationResult;
  bool _isProcessing = false;
  String? _errorMessage;
  AuthenticationConfig _config = const AuthenticationConfig();
  double _comparisonSliderValue = 0.5; // 0.0 = original, 1.0 = analysis result
  int _currentAnalysisIndex = 0;

  ImageAuthenticatorController(
    this._imageProcessorService,
    this._storageService,
  );

  // Getters
  File? get selectedImage => _selectedImage;
  AuthenticationData? get authenticationResult => _authenticationResult;
  bool get isProcessing => _isProcessing;
  String? get errorMessage => _errorMessage;
  AuthenticationConfig get config => _config;
  double get comparisonSliderValue => _comparisonSliderValue;
  int get currentAnalysisIndex => _currentAnalysisIndex;
  bool get hasResult => _authenticationResult != null;
  bool get hasImage => _selectedImage != null;

  List<AnalysisResult> get analysisResults =>
      _authenticationResult?.analysisResults ?? [];
  AnalysisResult? get currentAnalysisResult =>
      analysisResults.isNotEmpty &&
          _currentAnalysisIndex < analysisResults.length
      ? analysisResults[_currentAnalysisIndex]
      : null;

  /// Pick image from gallery
  Future<void> pickImage() async {
    try {
      _clearError();
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 100, // Keep original quality for authentication
      );

      if (image != null) {
        _selectedImage = File(image.path);
        _authenticationResult = null; // Clear previous results
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to pick image: $e');
    }
  }

  /// Take photo with camera
  Future<void> takePhoto() async {
    try {
      _clearError();
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        imageQuality: 100, // Keep original quality for authentication
      );

      if (image != null) {
        _selectedImage = File(image.path);
        _authenticationResult = null; // Clear previous results
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to take photo: $e');
    }
  }

  /// Authenticate the selected image
  Future<void> authenticateImage() async {
    if (_selectedImage == null) {
      _setError('No image selected');
      return;
    }

    try {
      _setProcessing(true);
      _clearError();

      // Call the image processor service for authentication
      final result = await _imageProcessorService.authenticateImage(
        imagePath: _selectedImage!.path,
        config: _config,
      );

      _authenticationResult = result;
      _currentAnalysisIndex = 0;
      _comparisonSliderValue = 0.5;

      notifyListeners();
    } catch (e) {
      _setError('Authentication failed: $e');
    } finally {
      _setProcessing(false);
    }
  }

  /// Update authentication configuration
  void updateConfig(AuthenticationConfig newConfig) {
    _config = newConfig;
    notifyListeners();
  }

  /// Update comparison slider value
  void updateComparisonSlider(double value) {
    _comparisonSliderValue = value.clamp(0.0, 1.0);
    notifyListeners();
  }

  /// Switch to different analysis result
  void switchAnalysisResult(int index) {
    if (index >= 0 && index < analysisResults.length) {
      _currentAnalysisIndex = index;
      notifyListeners();
    }
  }

  /// Clear current image and results
  void clearImage() {
    _selectedImage = null;
    _authenticationResult = null;
    _currentAnalysisIndex = 0;
    _comparisonSliderValue = 0.5;
    _clearError();
    notifyListeners();
  }

  /// Save authentication result
  Future<void> saveResult() async {
    if (_authenticationResult == null) {
      _setError('No result to save');
      return;
    }

    try {
      _setProcessing(true);

      // Save to storage service (TODO: implement saveAuthenticationResult method)
      // await _storageService.saveAuthenticationResult(_authenticationResult!);

      // Show success message or navigate back
      _clearError();
    } catch (e) {
      _setError('Failed to save result: $e');
    } finally {
      _setProcessing(false);
    }
  }

  // Private helper methods
  void _setProcessing(bool processing) {
    _isProcessing = processing;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
