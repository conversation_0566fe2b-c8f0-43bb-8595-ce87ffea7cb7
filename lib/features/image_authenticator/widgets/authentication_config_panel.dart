import 'package:flutter/material.dart';
import '../models/authentication_data.dart';

class AuthenticationConfigPanel extends StatelessWidget {
  final AuthenticationConfig config;
  final ValueChanged<AuthenticationConfig> onConfigChanged;

  const AuthenticationConfigPanel({
    super.key,
    required this.config,
    required this.onConfigChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1A1A1A),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              const Icon(Icons.tune, color: Colors.white, size: 20),
              const SizedBox(width: 8),
              const Text(
                'Authentication Settings',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Analysis algorithms toggles
          Text(
            'Analysis Algorithms',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.8),
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),

          _buildToggleOption(
            'Error Level Analysis (ELA)',
            'Detects JPEG compression artifacts',
            config.enableEla,
            (value) => onConfigChanged(config.copyWith(enableEla: value)),
          ),

          _buildToggleOption(
            'Copy-Move Detection',
            'Finds duplicated regions in the image',
            config.enableCopyMove,
            (value) => onConfigChanged(config.copyWith(enableCopyMove: value)),
          ),

          _buildToggleOption(
            'JPEG Artifacts Analysis',
            'Analyzes compression patterns',
            config.enableJpegArtifacts,
            (value) =>
                onConfigChanged(config.copyWith(enableJpegArtifacts: value)),
          ),

          _buildToggleOption(
            'Noise Pattern Analysis',
            'Detects inconsistent noise patterns',
            config.enableNoiseAnalysis,
            (value) =>
                onConfigChanged(config.copyWith(enableNoiseAnalysis: value)),
          ),

          _buildToggleOption(
            'Metadata Analysis',
            'Examines EXIF and metadata integrity',
            config.enableMetadataAnalysis,
            (value) =>
                onConfigChanged(config.copyWith(enableMetadataAnalysis: value)),
          ),

          const SizedBox(height: 16),

          // Advanced settings
          Text(
            'Advanced Settings',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.8),
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),

          // ELA Quality
          _buildSliderOption(
            context,
            'ELA Quality',
            'JPEG quality for Error Level Analysis',
            config.elaQuality,
            50.0,
            95.0,
            (value) => onConfigChanged(config.copyWith(elaQuality: value)),
            suffix: '%',
          ),

          // Block Size
          _buildSliderOption(
            context,
            'Block Size',
            'Analysis block size for copy-move detection',
            config.blockSize.toDouble(),
            8.0,
            32.0,
            (value) =>
                onConfigChanged(config.copyWith(blockSize: value.round())),
            suffix: 'px',
          ),

          // Threshold
          _buildSliderOption(
            context,
            'Detection Threshold',
            'Sensitivity threshold for manipulation detection',
            config.threshold,
            0.1,
            0.9,
            (value) => onConfigChanged(config.copyWith(threshold: value)),
            suffix: '',
          ),
        ],
      ),
    );
  }

  Widget _buildToggleOption(
    String title,
    String description,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.03),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.6),
                    fontSize: 11,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: Colors.white,
            activeTrackColor: Colors.white.withValues(alpha: 0.3),
            inactiveThumbColor: Colors.white.withValues(alpha: 0.6),
            inactiveTrackColor: Colors.white.withValues(alpha: 0.1),
          ),
        ],
      ),
    );
  }

  Widget _buildSliderOption(
    BuildContext context,
    String title,
    String description,
    double value,
    double min,
    double max,
    ValueChanged<double> onChanged, {
    String suffix = '',
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.03),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      description,
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.6),
                        fontSize: 11,
                      ),
                    ),
                  ],
                ),
              ),
              Text(
                '${value.toStringAsFixed(suffix.isEmpty ? 1 : 0)}$suffix',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 13,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: Colors.white,
              inactiveTrackColor: Colors.white.withValues(alpha: 0.2),
              thumbColor: Colors.white,
              overlayColor: Colors.white.withValues(alpha: 0.1),
              trackHeight: 2,
              thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 6),
            ),
            child: Slider(
              value: value,
              min: min,
              max: max,
              onChanged: onChanged,
            ),
          ),
        ],
      ),
    );
  }
}
