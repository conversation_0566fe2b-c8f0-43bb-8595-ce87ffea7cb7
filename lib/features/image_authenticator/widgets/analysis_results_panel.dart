import 'package:flutter/material.dart';
import '../models/authentication_data.dart';

class AnalysisResultsPanel extends StatelessWidget {
  final AuthenticationData authenticationData;
  final int currentAnalysisIndex;
  final ValueChanged<int> onAnalysisChanged;

  const AnalysisResultsPanel({
    super.key,
    required this.authenticationData,
    required this.currentAnalysisIndex,
    required this.onAnalysisChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      decoration: BoxDecoration(
        color: const Color(0xFF1A1A1A),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // Overall result header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: _getOverallResultColor(authenticationData.isAuthentic),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  authenticationData.isAuthentic ? Icons.verified : Icons.warning,
                  color: Colors.white,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        authenticationData.isAuthentic ? 'Authentic' : 'Potentially Modified',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Confidence: ${(authenticationData.overallConfidence * 100).toInt()}%',
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.9),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                Text(
                  '${authenticationData.analysisResults.length} Tests',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.8),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),

          // Analysis tabs
          Container(
            height: 50,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 8),
              itemCount: authenticationData.analysisResults.length,
              itemBuilder: (context, index) {
                final analysis = authenticationData.analysisResults[index];
                final isSelected = index == currentAnalysisIndex;
                
                return GestureDetector(
                  onTap: () => onAnalysisChanged(index),
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: isSelected 
                          ? Colors.white.withValues(alpha: 0.1)
                          : Colors.transparent,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: isSelected 
                            ? Colors.white.withValues(alpha: 0.3)
                            : Colors.white.withValues(alpha: 0.1),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: _getConfidenceColor(analysis.confidence),
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          analysis.analysisType,
                          style: TextStyle(
                            color: isSelected ? Colors.white : Colors.white.withValues(alpha: 0.7),
                            fontSize: 12,
                            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),

          // Current analysis details
          Expanded(
            child: _buildAnalysisDetails(),
          ),
        ],
      ),
    );
  }

  Widget _buildAnalysisDetails() {
    if (currentAnalysisIndex >= authenticationData.analysisResults.length) {
      return const Center(
        child: Text(
          'No analysis selected',
          style: TextStyle(color: Colors.white),
        ),
      );
    }

    final analysis = authenticationData.analysisResults[currentAnalysisIndex];

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Analysis type and confidence
          Row(
            children: [
              Expanded(
                child: Text(
                  analysis.analysisType,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: _getConfidenceColor(analysis.confidence),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  '${(analysis.confidence * 100).toInt()}% Confidence',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Description
          Text(
            analysis.description,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.8),
              fontSize: 14,
              height: 1.5,
            ),
          ),

          const SizedBox(height: 16),

          // Manipulation regions
          if (analysis.regions.isNotEmpty) ...[
            Text(
              'Detected Regions (${analysis.regions.length})',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            ...analysis.regions.map((region) => _buildRegionCard(region)),
          ],

          const SizedBox(height: 16),

          // Technical details
          _buildTechnicalDetails(analysis),
        ],
      ),
    );
  }

  Widget _buildRegionCard(ManipulationRegion region) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 6,
            height: 6,
            decoration: BoxDecoration(
              color: _getConfidenceColor(region.confidence),
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  region.manipulationType,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  'Position: (${region.x.toInt()}, ${region.y.toInt()}) • Size: ${region.width.toInt()}×${region.height.toInt()}',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.6),
                    fontSize: 11,
                  ),
                ),
              ],
            ),
          ),
          Text(
            '${(region.confidence * 100).toInt()}%',
            style: TextStyle(
              color: _getConfidenceColor(region.confidence),
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTechnicalDetails(AnalysisResult analysis) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.03),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Technical Details',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Analysis Type: ${analysis.analysisType}\n'
            'Regions Found: ${analysis.regions.length}\n'
            'Visualization: ${analysis.visualizationPath != null ? 'Available' : 'Not Available'}',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.6),
              fontSize: 11,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Color _getOverallResultColor(bool isAuthentic) {
    return isAuthentic 
        ? Colors.green.withValues(alpha: 0.8)
        : Colors.red.withValues(alpha: 0.8);
  }

  Color _getConfidenceColor(double confidence) {
    if (confidence >= 0.8) {
      return Colors.red;
    } else if (confidence >= 0.6) {
      return Colors.orange;
    } else {
      return Colors.green;
    }
  }
}
