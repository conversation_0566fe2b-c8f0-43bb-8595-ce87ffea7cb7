import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'controllers/image_authenticator_controller.dart';
import 'widgets/image_comparison_widget.dart';
import 'widgets/analysis_results_panel.dart';
import 'widgets/authentication_config_panel.dart';
import '../../core/services/image_processor_service.dart';
import '../../core/services/storage_service.dart';

class ImageAuthenticatorScreen extends StatefulWidget {
  const ImageAuthenticatorScreen({super.key});

  @override
  State<ImageAuthenticatorScreen> createState() =>
      _ImageAuthenticatorScreenState();
}

class _ImageAuthenticatorScreenState extends State<ImageAuthenticatorScreen> {
  late final ImageAuthenticatorController _controller;
  bool _showConfigPanel = false;

  @override
  void initState() {
    super.initState();
    _controller = ImageAuthenticatorController(
      GetIt.instance<ImageProcessorService>(),
      GetIt.instance<StorageService>(),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0A0A0A),
      appBar: AppBar(
        backgroundColor: const Color(0xFF1A1A1A),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          'Image Authentication',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              _showConfigPanel ? Icons.close : Icons.settings,
              color: Colors.white,
            ),
            onPressed: () {
              setState(() {
                _showConfigPanel = !_showConfigPanel;
              });
            },
          ),
        ],
      ),
      body: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Column(
            children: [
              // Error message
              if (_controller.errorMessage != null)
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  color: Colors.red.withValues(alpha: 0.1),
                  child: Text(
                    _controller.errorMessage!,
                    style: const TextStyle(color: Colors.red),
                    textAlign: TextAlign.center,
                  ),
                ),

              // Configuration panel
              if (_showConfigPanel)
                AuthenticationConfigPanel(
                  config: _controller.config,
                  onConfigChanged: _controller.updateConfig,
                ),

              // Main content
              Expanded(
                child: _controller.hasImage
                    ? _buildImageAnalysisView()
                    : _buildImageSelectionView(),
              ),

              // Bottom action bar
              if (_controller.hasImage) _buildBottomActionBar(),
            ],
          );
        },
      ),
    );
  }

  Widget _buildImageSelectionView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.security,
            size: 80,
            color: Colors.white.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 24),
          const Text(
            'Image Authentication',
            style: TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'Detect tampering, editing, and manipulation\nusing professional forensics algorithms',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 16,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 48),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildActionButton(
                icon: Icons.photo_library,
                label: 'Choose Photo',
                onPressed: _controller.pickImage,
              ),
              const SizedBox(width: 24),
              _buildActionButton(
                icon: Icons.camera_alt,
                label: 'Take Photo',
                onPressed: _controller.takePhoto,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildImageAnalysisView() {
    return Column(
      children: [
        // Image comparison widget
        Expanded(
          flex: 3,
          child: ImageComparisonWidget(
            originalImage: _controller.selectedImage!,
            analysisResult: _controller.currentAnalysisResult,
            sliderValue: _controller.comparisonSliderValue,
            onSliderChanged: _controller.updateComparisonSlider,
          ),
        ),

        // Analysis results panel
        if (_controller.hasResult)
          Expanded(
            flex: 2,
            child: AnalysisResultsPanel(
              authenticationData: _controller.authenticationResult!,
              currentAnalysisIndex: _controller.currentAnalysisIndex,
              onAnalysisChanged: _controller.switchAnalysisResult,
            ),
          ),
      ],
    );
  }

  Widget _buildBottomActionBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1A1A1A),
        border: Border(
          top: BorderSide(color: Colors.white.withValues(alpha: 0.1), width: 1),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildActionButton(
              icon: Icons.refresh,
              label: 'New Image',
              onPressed: _controller.clearImage,
              variant: 'secondary',
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildActionButton(
              icon: _controller.isProcessing
                  ? Icons.hourglass_empty
                  : Icons.security,
              label: _controller.isProcessing ? 'Analyzing...' : 'Authenticate',
              onPressed: _controller.isProcessing
                  ? null
                  : _controller.authenticateImage,
              variant: 'primary',
            ),
          ),
          if (_controller.hasResult) ...[
            const SizedBox(width: 16),
            Expanded(
              child: _buildActionButton(
                icon: Icons.save,
                label: 'Save Result',
                onPressed: _controller.saveResult,
                variant: 'secondary',
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback? onPressed,
    String variant = 'primary',
  }) {
    final isPrimary = variant == 'primary';
    final isDisabled = onPressed == null;

    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(
        icon,
        color: isDisabled
            ? Colors.white.withValues(alpha: 0.3)
            : isPrimary
            ? Colors.black
            : Colors.white,
        size: 20,
      ),
      label: Text(
        label,
        style: TextStyle(
          color: isDisabled
              ? Colors.white.withValues(alpha: 0.3)
              : isPrimary
              ? Colors.black
              : Colors.white,
          fontWeight: FontWeight.w600,
        ),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: isDisabled
            ? Colors.white.withValues(alpha: 0.1)
            : isPrimary
            ? Colors.white
            : Colors.transparent,
        foregroundColor: isPrimary ? Colors.black : Colors.white,
        side: isPrimary
            ? null
            : BorderSide(
                color: isDisabled
                    ? Colors.white.withValues(alpha: 0.2)
                    : Colors.white.withValues(alpha: 0.3),
              ),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        elevation: 0,
      ),
    );
  }
}
