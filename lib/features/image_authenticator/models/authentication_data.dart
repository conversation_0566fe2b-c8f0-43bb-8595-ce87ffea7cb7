/// Data models for image authentication feature
class AuthenticationData {
  final String imagePath;
  final String originalImagePath;
  final List<AnalysisResult> analysisResults;
  final double overallConfidence;
  final bool isAuthentic;
  final DateTime timestamp;

  const AuthenticationData({
    required this.imagePath,
    required this.originalImagePath,
    required this.analysisResults,
    required this.overallConfidence,
    required this.isAuthentic,
    required this.timestamp,
  });

  factory AuthenticationData.fromJson(Map<String, dynamic> json) {
    return AuthenticationData(
      imagePath: json['imagePath'] as String,
      originalImagePath: json['originalImagePath'] as String,
      analysisResults: (json['analysisResults'] as List)
          .map((e) => AnalysisResult.fromJson(e as Map<String, dynamic>))
          .toList(),
      overallConfidence: (json['overallConfidence'] as num).toDouble(),
      isAuthentic: json['isAuthentic'] as bool,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'imagePath': imagePath,
      'originalImagePath': originalImagePath,
      'analysisResults': analysisResults.map((e) => e.toJson()).toList(),
      'overallConfidence': overallConfidence,
      'isAuthentic': isAuthentic,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}

class AnalysisResult {
  final String analysisType;
  final double confidence;
  final String description;
  final List<ManipulationRegion> regions;
  final String? visualizationPath;

  const AnalysisResult({
    required this.analysisType,
    required this.confidence,
    required this.description,
    required this.regions,
    this.visualizationPath,
  });

  factory AnalysisResult.fromJson(Map<String, dynamic> json) {
    return AnalysisResult(
      analysisType: json['analysisType'] as String,
      confidence: (json['confidence'] as num).toDouble(),
      description: json['description'] as String,
      regions: (json['regions'] as List)
          .map((e) => ManipulationRegion.fromJson(e as Map<String, dynamic>))
          .toList(),
      visualizationPath: json['visualizationPath'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'analysisType': analysisType,
      'confidence': confidence,
      'description': description,
      'regions': regions.map((e) => e.toJson()).toList(),
      'visualizationPath': visualizationPath,
    };
  }
}

class ManipulationRegion {
  final double x;
  final double y;
  final double width;
  final double height;
  final double confidence;
  final String manipulationType;

  const ManipulationRegion({
    required this.x,
    required this.y,
    required this.width,
    required this.height,
    required this.confidence,
    required this.manipulationType,
  });

  factory ManipulationRegion.fromJson(Map<String, dynamic> json) {
    return ManipulationRegion(
      x: (json['x'] as num).toDouble(),
      y: (json['y'] as num).toDouble(),
      width: (json['width'] as num).toDouble(),
      height: (json['height'] as num).toDouble(),
      confidence: (json['confidence'] as num).toDouble(),
      manipulationType: json['manipulationType'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'x': x,
      'y': y,
      'width': width,
      'height': height,
      'confidence': confidence,
      'manipulationType': manipulationType,
    };
  }
}

class AuthenticationConfig {
  final double elaQuality;
  final int blockSize;
  final double threshold;
  final bool enableEla;
  final bool enableCopyMove;
  final bool enableJpegArtifacts;
  final bool enableNoiseAnalysis;
  final bool enableMetadataAnalysis;

  const AuthenticationConfig({
    this.elaQuality = 90.0,
    this.blockSize = 16,
    this.threshold = 0.7,
    this.enableEla = true,
    this.enableCopyMove = true,
    this.enableJpegArtifacts = true,
    this.enableNoiseAnalysis = true,
    this.enableMetadataAnalysis = true,
  });

  factory AuthenticationConfig.fromJson(Map<String, dynamic> json) {
    return AuthenticationConfig(
      elaQuality: (json['elaQuality'] as num?)?.toDouble() ?? 90.0,
      blockSize: json['blockSize'] as int? ?? 16,
      threshold: (json['threshold'] as num?)?.toDouble() ?? 0.7,
      enableEla: json['enableEla'] as bool? ?? true,
      enableCopyMove: json['enableCopyMove'] as bool? ?? true,
      enableJpegArtifacts: json['enableJpegArtifacts'] as bool? ?? true,
      enableNoiseAnalysis: json['enableNoiseAnalysis'] as bool? ?? true,
      enableMetadataAnalysis: json['enableMetadataAnalysis'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'elaQuality': elaQuality,
      'blockSize': blockSize,
      'threshold': threshold,
      'enableEla': enableEla,
      'enableCopyMove': enableCopyMove,
      'enableJpegArtifacts': enableJpegArtifacts,
      'enableNoiseAnalysis': enableNoiseAnalysis,
      'enableMetadataAnalysis': enableMetadataAnalysis,
    };
  }

  AuthenticationConfig copyWith({
    double? elaQuality,
    int? blockSize,
    double? threshold,
    bool? enableEla,
    bool? enableCopyMove,
    bool? enableJpegArtifacts,
    bool? enableNoiseAnalysis,
    bool? enableMetadataAnalysis,
  }) {
    return AuthenticationConfig(
      elaQuality: elaQuality ?? this.elaQuality,
      blockSize: blockSize ?? this.blockSize,
      threshold: threshold ?? this.threshold,
      enableEla: enableEla ?? this.enableEla,
      enableCopyMove: enableCopyMove ?? this.enableCopyMove,
      enableJpegArtifacts: enableJpegArtifacts ?? this.enableJpegArtifacts,
      enableNoiseAnalysis: enableNoiseAnalysis ?? this.enableNoiseAnalysis,
      enableMetadataAnalysis: enableMetadataAnalysis ?? this.enableMetadataAnalysis,
    );
  }
}
