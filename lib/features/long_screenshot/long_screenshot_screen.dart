import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../../core/design_system/design_system.dart';
import '../../core/widgets/widgets.dart';
import '../../l10n/generated/app_localizations.dart';
import 'controllers/long_screenshot_controller.dart';
import 'models/long_screenshot_models.dart';
import 'widgets/capture_mode_selector.dart';
import 'widgets/capture_settings_panel.dart';
import 'widgets/source_images_panel.dart';
import 'widgets/web_page_input.dart';
import 'widgets/capture_progress_overlay.dart';

final getIt = GetIt.instance;

class LongScreenshotScreen extends StatefulWidget {
  const LongScreenshotScreen({super.key});

  @override
  State<LongScreenshotScreen> createState() => _LongScreenshotScreenState();
}

class _LongScreenshotScreenState extends State<LongScreenshotScreen> {
  late LongScreenshotController _controller;

  @override
  void initState() {
    super.initState();
    _controller = getIt<LongScreenshotController>();
    _controller.addListener(_onControllerChanged);
  }

  @override
  void dispose() {
    _controller.removeListener(_onControllerChanged);
    // Don't dispose the controller as it's a singleton
    super.dispose();
  }

  void _onControllerChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: AppBar(
        title: Text(
          '长截屏',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
            color: colorScheme.onSurface,
          ),
        ),
        backgroundColor: colorScheme.surface,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: colorScheme.onSurface),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          if (_controller.canCapture && !_controller.isLoading)
            IconButton(
              icon: Icon(Icons.play_arrow, color: colorScheme.primary),
              onPressed: _startCapture,
              tooltip: '开始截图',
            ),
          if (_controller.isLoading)
            IconButton(
              icon: Icon(Icons.stop, color: colorScheme.error),
              onPressed: _controller.cancelOperation,
              tooltip: '取消',
            ),
        ],
      ),
      body: Stack(
        children: [
          _buildMainContent(context),
          if (_controller.isLoading)
            CaptureProgressOverlay(
              progress: _controller.progress,
              onCancel: _controller.cancelOperation,
            ),
        ],
      ),
    );
  }

  Widget _buildMainContent(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Error display
          if (_controller.error != null)
            Container(
              width: double.infinity,
              margin: const EdgeInsets.only(bottom: 16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.errorContainer,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: colorScheme.error.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.error_outline, color: colorScheme.error, size: 20),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      _controller.error!,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onErrorContainer,
                      ),
                    ),
                  ),
                ],
              ),
            ),

          // Capture mode selector
          Text(
            '截图模式',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 12),
          CaptureModeSelector(
            selectedMode: _controller.settings.mode,
            onModeChanged: (mode) {
              _controller.updateSettings(
                _controller.settings.copyWith(mode: mode),
              );
            },
          ),
          const SizedBox(height: 24),

          // Mode-specific content
          _buildModeSpecificContent(context),

          const SizedBox(height: 24),

          // Capture settings
          Text(
            '截图设置',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 12),
          CaptureSettingsPanel(
            settings: _controller.settings,
            onSettingsChanged: _controller.updateSettings,
          ),

          const SizedBox(height: 32),

          // Start capture button
          if (_controller.canCapture && !_controller.isLoading)
            SizedBox(
              width: double.infinity,
              child: TuShenButton(
                onPressed: _startCapture,
                variant: TuShenButtonVariant.primary,
                size: TuShenButtonSize.large,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(_getStartButtonIcon(), size: 20),
                    const SizedBox(width: 8),
                    Text(_getStartButtonText()),
                  ],
                ),
              ),
            ),

          // Result display
          if (_controller.result != null) ...[
            const SizedBox(height: 24),
            _buildResultDisplay(context),
          ],
        ],
      ),
    );
  }

  Widget _buildModeSpecificContent(BuildContext context) {
    switch (_controller.settings.mode) {
      case CaptureMode.fromImages:
        return SourceImagesPanel(
          sourceImagePaths: _controller.sourceImagePaths,
          onAddImage: _controller.addSourceImage,
          onRemoveImage: _controller.removeSourceImage,
          onReorderImages: _controller.reorderSourceImages,
          onClearImages: _controller.clearSourceImages,
        );

      case CaptureMode.webPage:
        return WebPageInput(
          options: _controller.webPageOptions,
          onOptionsChanged: _controller.setWebPageOptions,
        );

      case CaptureMode.autoScroll:
        return _buildAutoScrollInstructions(context);

      case CaptureMode.manual:
        return _buildManualInstructions(context);
    }
  }

  Widget _buildAutoScrollInstructions(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.primaryContainer.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorScheme.primary.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: colorScheme.primary, size: 20),
              const SizedBox(width: 8),
              Text(
                '自动滚动截图说明',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            '1. 点击开始后，应用将自动滚动页面\n'
            '2. 每次滚动都会自动截图\n'
            '3. 截图完成后自动拼接成长图\n'
            '4. 支持去除重叠区域和状态栏',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface.withOpacity(0.8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildManualInstructions(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.secondaryContainer.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorScheme.secondary.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.touch_app, color: colorScheme.secondary, size: 20),
              const SizedBox(width: 8),
              Text(
                '手动截图说明',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: colorScheme.secondary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            '1. 点击开始进入截图模式\n'
            '2. 手动选择截图区域\n'
            '3. 可以多次截图不同区域\n'
            '4. 完成后自动拼接成长图',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface.withOpacity(0.8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultDisplay(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final result = _controller.result!;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorScheme.outline.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '截图结果',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildResultInfo(
                  '尺寸',
                  '${result.width} × ${result.height}',
                ),
              ),
              Expanded(
                child: _buildResultInfo('文件大小', result.fileSizeFormatted),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _buildResultInfo('源图片数量', '${result.sourceImageCount}'),
              ),
              Expanded(
                child: _buildResultInfo(
                  '处理时间',
                  '${result.processingTime.inSeconds}s',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildResultInfo(String label, String value) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: colorScheme.onSurface.withOpacity(0.6),
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
            color: colorScheme.onSurface,
          ),
        ),
      ],
    );
  }

  IconData _getStartButtonIcon() {
    switch (_controller.settings.mode) {
      case CaptureMode.autoScroll:
        return Icons.play_arrow;
      case CaptureMode.manual:
        return Icons.touch_app;
      case CaptureMode.fromImages:
        return Icons.merge;
      case CaptureMode.webPage:
        return Icons.web;
    }
  }

  String _getStartButtonText() {
    switch (_controller.settings.mode) {
      case CaptureMode.autoScroll:
        return '开始自动截图';
      case CaptureMode.manual:
        return '开始手动截图';
      case CaptureMode.fromImages:
        return '开始拼接图片';
      case CaptureMode.webPage:
        return '开始网页截图';
    }
  }

  void _startCapture() {
    switch (_controller.settings.mode) {
      case CaptureMode.autoScroll:
        _controller.startAutoScrollCapture();
        break;
      case CaptureMode.manual:
        _controller.startManualCapture();
        break;
      case CaptureMode.fromImages:
        _controller.processFromImages();
        break;
      case CaptureMode.webPage:
        _controller.captureWebPage();
        break;
    }
  }
}
