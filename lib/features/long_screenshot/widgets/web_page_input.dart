import 'package:flutter/material.dart';
import '../../../core/design_system/design_system.dart';
import '../../../core/widgets/widgets.dart';
import '../models/long_screenshot_models.dart';

class WebPageInput extends StatefulWidget {
  final WebPageCaptureOptions? options;
  final ValueChanged<WebPageCaptureOptions> onOptionsChanged;

  const WebPageInput({
    super.key,
    required this.options,
    required this.onOptionsChanged,
  });

  @override
  State<WebPageInput> createState() => _WebPageInputState();
}

class _WebPageInputState extends State<WebPageInput> {
  late TextEditingController _urlController;
  late WebPageCaptureOptions _options;

  @override
  void initState() {
    super.initState();
    _options = widget.options ?? const WebPageCaptureOptions(url: '');
    _urlController = TextEditingController(text: _options.url);
  }

  @override
  void dispose() {
    _urlController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: colorScheme.outline.withOpacity(0.2)),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // URL input
          Text(
            '网页地址',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 12),

          TuShenTextField(
            controller: _urlController,
            hint: '请输入网页URL，如：https://example.com',
            prefixIcon: Icons.language,
            onChanged: (value) {
              _updateOptions(_options.copyWith(url: value));
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return '请输入网页地址';
              }
              if (!_isValidUrl(value)) {
                return '请输入有效的网页地址';
              }
              return null;
            },
          ),

          const SizedBox(height: 20),

          // Viewport settings
          Text(
            '视窗设置',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 12),

          _buildViewportSettings(context),

          const SizedBox(height: 20),

          // Capture options
          Text(
            '截图选项',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 12),

          _buildCaptureOptions(context),
        ],
      ),
    );
  }

  Widget _buildViewportSettings(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '宽度',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      height: 40,
                      decoration: BoxDecoration(
                        color: colorScheme.surface,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: colorScheme.outline.withOpacity(0.3),
                        ),
                      ),
                      child: TextField(
                        controller: TextEditingController(
                          text: _options.viewportWidth.toString(),
                        ),
                        keyboardType: TextInputType.number,
                        textAlign: TextAlign.center,
                        style: theme.textTheme.bodyMedium,
                        decoration: const InputDecoration(
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.symmetric(horizontal: 8),
                        ),
                        onChanged: (value) {
                          final width =
                              int.tryParse(value) ?? _options.viewportWidth;
                          _updateOptions(
                            _options.copyWith(viewportWidth: width),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 12),
              Text(
                '×',
                style: theme.textTheme.titleMedium?.copyWith(
                  color: colorScheme.onSurface.withOpacity(0.5),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '高度',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      height: 40,
                      decoration: BoxDecoration(
                        color: colorScheme.surface,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: colorScheme.outline.withOpacity(0.3),
                        ),
                      ),
                      child: TextField(
                        controller: TextEditingController(
                          text: _options.viewportHeight.toString(),
                        ),
                        keyboardType: TextInputType.number,
                        textAlign: TextAlign.center,
                        style: theme.textTheme.bodyMedium,
                        decoration: const InputDecoration(
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.symmetric(horizontal: 8),
                        ),
                        onChanged: (value) {
                          final height =
                              int.tryParse(value) ?? _options.viewportHeight;
                          _updateOptions(
                            _options.copyWith(viewportHeight: height),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Icon(Icons.info_outline, size: 16, color: colorScheme.primary),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  '推荐使用1200×800以获得最佳效果',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.primary,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCaptureOptions(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          _buildSwitchOption(
            context,
            title: '完整页面',
            subtitle: '截取整个页面内容，包括需要滚动的部分',
            value: _options.fullPage,
            onChanged: (value) =>
                _updateOptions(_options.copyWith(fullPage: value)),
          ),
          const SizedBox(height: 12),
          _buildSwitchOption(
            context,
            title: '移除广告',
            subtitle: '尝试自动检测并移除页面中的广告',
            value: _options.removeAds,
            onChanged: (value) =>
                _updateOptions(_options.copyWith(removeAds: value)),
          ),
          const SizedBox(height: 12),
          _buildSwitchOption(
            context,
            title: '深色模式',
            subtitle: '使用深色主题截取页面',
            value: _options.darkMode,
            onChanged: (value) =>
                _updateOptions(_options.copyWith(darkMode: value)),
          ),
          const SizedBox(height: 16),

          // Delay setting
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '等待时间',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                        color: colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      '页面加载完成后的等待时间',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                  ],
                ),
              ),
              Text(
                '${(_options.delay / 1000).toStringAsFixed(1)}s',
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: colorScheme.primary,
              inactiveTrackColor: colorScheme.outline.withOpacity(0.3),
              thumbColor: colorScheme.primary,
              overlayColor: colorScheme.primary.withOpacity(0.1),
              trackHeight: 4,
            ),
            child: Slider(
              value: _options.delay.toDouble(),
              min: 0,
              max: 10000,
              divisions: 20,
              onChanged: (value) =>
                  _updateOptions(_options.copyWith(delay: value.round())),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSwitchOption(
    BuildContext context, {
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                subtitle,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
            ],
          ),
        ),
        Switch(
          value: value,
          onChanged: onChanged,
          activeColor: colorScheme.primary,
          activeTrackColor: colorScheme.primary.withOpacity(0.3),
          inactiveThumbColor: colorScheme.outline,
          inactiveTrackColor: colorScheme.outline.withOpacity(0.3),
        ),
      ],
    );
  }

  void _updateOptions(WebPageCaptureOptions options) {
    setState(() {
      _options = options;
    });
    widget.onOptionsChanged(options);
  }

  bool _isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }
}
