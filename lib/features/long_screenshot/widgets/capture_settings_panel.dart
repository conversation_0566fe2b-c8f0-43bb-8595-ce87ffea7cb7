import 'package:flutter/material.dart';
import '../../../core/design_system/design_system.dart';
import '../../../core/widgets/widgets.dart';
import '../models/long_screenshot_models.dart';

class CaptureSettingsPanel extends StatelessWidget {
  final CaptureSettings settings;
  final ValueChanged<CaptureSettings> onSettingsChanged;

  const CaptureSettingsPanel({
    super.key,
    required this.settings,
    required this.onSettingsChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: colorScheme.outline.withOpacity(0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Stitching algorithm
          _buildSectionTitle(context, '拼接算法'),
          const SizedBox(height: 8),
          _buildAlgorithmSelector(context),
          
          const SizedBox(height: 20),
          
          // Quality settings
          _buildSectionTitle(context, '质量设置'),
          const SizedBox(height: 8),
          _buildQualitySlider(context),
          
          const SizedBox(height: 20),
          
          // Advanced options
          _buildSectionTitle(context, '高级选项'),
          const SizedBox(height: 8),
          _buildAdvancedOptions(context),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Text(
      title,
      style: theme.textTheme.titleSmall?.copyWith(
        fontWeight: FontWeight.w600,
        color: colorScheme.onSurface,
      ),
    );
  }

  Widget _buildAlgorithmSelector(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: StitchingAlgorithm.values.map((algorithm) {
          final isSelected = algorithm == settings.algorithm;
          final isFirst = algorithm == StitchingAlgorithm.values.first;
          final isLast = algorithm == StitchingAlgorithm.values.last;

          return Container(
            decoration: BoxDecoration(
              color: isSelected 
                  ? colorScheme.primary.withOpacity(0.1)
                  : Colors.transparent,
              borderRadius: BorderRadius.vertical(
                top: isFirst ? const Radius.circular(12) : Radius.zero,
                bottom: isLast ? const Radius.circular(12) : Radius.zero,
              ),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () => onSettingsChanged(
                  settings.copyWith(algorithm: algorithm),
                ),
                borderRadius: BorderRadius.vertical(
                  top: isFirst ? const Radius.circular(12) : Radius.zero,
                  bottom: isLast ? const Radius.circular(12) : Radius.zero,
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 10,
                  ),
                  child: Row(
                    children: [
                      Radio<StitchingAlgorithm>(
                        value: algorithm,
                        groupValue: settings.algorithm,
                        onChanged: (value) {
                          if (value != null) {
                            onSettingsChanged(
                              settings.copyWith(algorithm: value),
                            );
                          }
                        },
                        activeColor: colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              algorithm.displayName,
                              style: theme.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w500,
                                color: colorScheme.onSurface,
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              algorithm.description,
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: colorScheme.onSurface.withOpacity(0.7),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildQualitySlider(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '压缩质量',
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: colorScheme.onSurface,
                ),
              ),
              Text(
                '${(settings.compressionQuality * 100).round()}%',
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: colorScheme.primary,
              inactiveTrackColor: colorScheme.outline.withOpacity(0.3),
              thumbColor: colorScheme.primary,
              overlayColor: colorScheme.primary.withOpacity(0.1),
              trackHeight: 4,
            ),
            child: Slider(
              value: settings.compressionQuality,
              min: 0.1,
              max: 1.0,
              divisions: 9,
              onChanged: (value) => onSettingsChanged(
                settings.copyWith(compressionQuality: value),
              ),
            ),
          ),
          Text(
            '较高的质量会产生更大的文件',
            style: theme.textTheme.bodySmall?.copyWith(
              color: colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdvancedOptions(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          _buildSwitchOption(
            context,
            title: '移除状态栏',
            subtitle: '自动检测并移除状态栏区域',
            value: settings.removeStatusBar,
            onChanged: (value) => onSettingsChanged(
              settings.copyWith(removeStatusBar: value),
            ),
          ),
          const SizedBox(height: 12),
          _buildSwitchOption(
            context,
            title: '移除导航栏',
            subtitle: '自动检测并移除底部导航栏',
            value: settings.removeNavigationBar,
            onChanged: (value) => onSettingsChanged(
              settings.copyWith(removeNavigationBar: value),
            ),
          ),
          const SizedBox(height: 12),
          _buildSwitchOption(
            context,
            title: '文本优化',
            subtitle: '针对文本内容进行优化处理',
            value: settings.optimizeForText,
            onChanged: (value) => onSettingsChanged(
              settings.copyWith(optimizeForText: value),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSwitchOption(
    BuildContext context, {
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                subtitle,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
            ],
          ),
        ),
        Switch(
          value: value,
          onChanged: onChanged,
          activeColor: colorScheme.primary,
          activeTrackColor: colorScheme.primary.withOpacity(0.3),
          inactiveThumbColor: colorScheme.outline,
          inactiveTrackColor: colorScheme.outline.withOpacity(0.3),
        ),
      ],
    );
  }
}
