import 'package:flutter/material.dart';
import '../../../core/design_system/design_system.dart';
import '../../../core/widgets/widgets.dart';
import '../models/long_screenshot_models.dart';

class CaptureProgressOverlay extends StatelessWidget {
  final CaptureProgress? progress;
  final VoidCallback onCancel;

  const CaptureProgressOverlay({
    super.key,
    required this.progress,
    required this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    if (progress == null) return const SizedBox.shrink();

    return Container(
      color: Colors.black.withOpacity(0.7),
      child: Center(child: _buildProgressCard(context)),
    );
  }

  Widget _buildProgressCard(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      margin: const EdgeInsets.all(32),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Progress animation
          _buildProgressAnimation(context),

          const SizedBox(height: 24),

          // Progress info
          _buildProgressInfo(context),

          const SizedBox(height: 24),

          // Progress bar
          _buildProgressBar(context),

          const SizedBox(height: 24),

          // Cancel button
          SizedBox(
            width: double.infinity,
            child: TuShenButton(
              onPressed: onCancel,
              variant: TuShenButtonVariant.outline,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.close, size: 18),
                  const SizedBox(width: 8),
                  Text('取消'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressAnimation(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: colorScheme.primaryContainer.withOpacity(0.3),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Animated progress circle
          SizedBox(
            width: 60,
            height: 60,
            child: CircularProgressIndicator(
              value: progress!.progress,
              strokeWidth: 4,
              backgroundColor: colorScheme.outline.withOpacity(0.3),
              valueColor: AlwaysStoppedAnimation<Color>(colorScheme.primary),
            ),
          ),

          // Progress percentage
          Text(
            '${(progress!.progress * 100).round()}%',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressInfo(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      children: [
        // Current action
        Text(
          progress!.currentAction,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: colorScheme.onSurface,
          ),
          textAlign: TextAlign.center,
        ),

        const SizedBox(height: 8),

        // Step info
        Text(
          '步骤 ${progress!.currentStep} / ${progress!.totalSteps}',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: colorScheme.onSurface.withOpacity(0.7),
          ),
        ),

        // Captured images count
        if (progress!.capturedImages.isNotEmpty) ...[
          const SizedBox(height: 8),
          Text(
            '已捕获 ${progress!.capturedImages.length} 张图片',
            style: theme.textTheme.bodySmall?.copyWith(
              color: colorScheme.primary,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildProgressBar(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      children: [
        // Progress bar
        Container(
          height: 8,
          decoration: BoxDecoration(
            color: colorScheme.outline.withOpacity(0.3),
            borderRadius: BorderRadius.circular(4),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: progress!.progress,
            child: Container(
              decoration: BoxDecoration(
                color: colorScheme.primary,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
        ),

        const SizedBox(height: 8),

        // Step indicators
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: List.generate(progress!.totalSteps, (index) {
            final isCompleted = index < progress!.currentStep;
            final isCurrent = index == progress!.currentStep - 1;

            return Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isCompleted || isCurrent
                    ? colorScheme.primary
                    : colorScheme.outline.withOpacity(0.3),
              ),
            );
          }),
        ),
      ],
    );
  }
}
