import 'package:flutter/material.dart';
import 'dart:io';
import '../../../core/design_system/design_system.dart';
import '../../../core/widgets/widgets.dart';

class SourceImagesPanel extends StatelessWidget {
  final List<String> sourceImagePaths;
  final ValueChanged<String> onAddImage;
  final ValueChanged<int> onRemoveImage;
  final Function(int, int) onReorderImages;
  final VoidCallback onClearImages;

  const SourceImagesPanel({
    super.key,
    required this.sourceImagePaths,
    required this.onAddImage,
    required this.onRemoveImage,
    required this.onReorderImages,
    required this.onClearImages,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: colorScheme.outline.withOpacity(0.2)),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '源图片 (${sourceImagePaths.length})',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: colorScheme.onSurface,
                ),
              ),
              if (sourceImagePaths.isNotEmpty)
                TextButton(
                  onPressed: onClearImages,
                  child: Text('清空', style: TextStyle(color: colorScheme.error)),
                ),
            ],
          ),
          const SizedBox(height: 12),

          // Add image button
          SizedBox(
            width: double.infinity,
            child: TuShenButton(
              onPressed: _showImagePicker,
              variant: TuShenButtonVariant.outline,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.add_photo_alternate, size: 20),
                  const SizedBox(width: 8),
                  Text('添加图片'),
                ],
              ),
            ),
          ),

          if (sourceImagePaths.isNotEmpty) ...[
            const SizedBox(height: 16),

            // Instructions
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: colorScheme.primaryContainer.withOpacity(0.3),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    size: 16,
                    color: colorScheme.primary,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '长按拖拽可调整图片顺序',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.primary,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 12),

            // Image list
            ReorderableListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: sourceImagePaths.length,
              onReorder: onReorderImages,
              itemBuilder: (context, index) {
                final imagePath = sourceImagePaths[index];
                return _buildImageItem(context, imagePath, index);
              },
            ),
          ] else ...[
            const SizedBox(height: 16),

            // Empty state
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: colorScheme.surfaceVariant.withOpacity(0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: colorScheme.outline.withOpacity(0.3),
                  style: BorderStyle.solid,
                ),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.photo_library_outlined,
                    size: 48,
                    color: colorScheme.onSurface.withOpacity(0.4),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    '还没有添加图片',
                    style: theme.textTheme.titleSmall?.copyWith(
                      color: colorScheme.onSurface.withOpacity(0.6),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '至少需要2张图片才能进行拼接',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurface.withOpacity(0.5),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildImageItem(BuildContext context, String imagePath, int index) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      key: ValueKey(imagePath),
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorScheme.outline.withOpacity(0.2)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            // Order number
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: colorScheme.primary,
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Text(
                  '${index + 1}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.onPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),

            // Image preview
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.file(
                File(imagePath),
                width: 60,
                height: 60,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: 60,
                    height: 60,
                    color: colorScheme.errorContainer,
                    child: Icon(Icons.broken_image, color: colorScheme.error),
                  );
                },
              ),
            ),
            const SizedBox(width: 12),

            // Image info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    imagePath.split('/').last,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                      color: colorScheme.onSurface,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 2),
                  FutureBuilder<FileStat>(
                    future: File(imagePath).stat(),
                    builder: (context, snapshot) {
                      if (snapshot.hasData) {
                        final size = snapshot.data!.size;
                        final sizeText = _formatFileSize(size);
                        return Text(
                          sizeText,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: colorScheme.onSurface.withOpacity(0.6),
                          ),
                        );
                      }
                      return const SizedBox.shrink();
                    },
                  ),
                ],
              ),
            ),

            // Actions
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Drag handle
                Icon(
                  Icons.drag_handle,
                  color: colorScheme.onSurface.withOpacity(0.5),
                  size: 20,
                ),
                const SizedBox(width: 8),

                // Remove button
                IconButton(
                  onPressed: () => onRemoveImage(index),
                  icon: Icon(Icons.close, color: colorScheme.error, size: 20),
                  constraints: const BoxConstraints(
                    minWidth: 32,
                    minHeight: 32,
                  ),
                  padding: EdgeInsets.zero,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
  }

  void _showImagePicker() {
    // TODO: Implement image picker
    // For now, this is a placeholder
    print('Show image picker');
  }
}
