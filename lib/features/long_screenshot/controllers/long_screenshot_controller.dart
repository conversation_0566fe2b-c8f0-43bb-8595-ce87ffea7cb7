import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'dart:io';
import '../../../core/services/image_service.dart';
import '../../../core/services/gallery_service.dart';
import '../models/long_screenshot_models.dart';

/// Long screenshot controller
class LongScreenshotController extends ChangeNotifier {
  final ImageService _imageService;
  final GalleryService _galleryService;

  LongScreenshotController(this._imageService, this._galleryService);

  // State
  bool _isCapturing = false;
  bool _isProcessing = false;
  String? _error;
  CaptureSettings _settings = const CaptureSettings();
  CaptureProgress? _progress;
  LongScreenshotResult? _result;
  List<String> _sourceImagePaths = [];
  WebPageCaptureOptions? _webPageOptions;

  // Getters
  bool get isCapturing => _isCapturing;
  bool get isProcessing => _isProcessing;
  bool get isLoading => _isCapturing || _isProcessing;
  String? get error => _error;
  CaptureSettings get settings => _settings;
  CaptureProgress? get progress => _progress;
  LongScreenshotResult? get result => _result;
  List<String> get sourceImagePaths => _sourceImagePaths;
  WebPageCaptureOptions? get webPageOptions => _webPageOptions;
  
  bool get canCapture {
    switch (_settings.mode) {
      case CaptureMode.fromImages:
        return _sourceImagePaths.length >= 2;
      case CaptureMode.webPage:
        return _webPageOptions?.url.isNotEmpty == true;
      case CaptureMode.autoScroll:
      case CaptureMode.manual:
        return true;
    }
  }

  /// Update capture settings
  void updateSettings(CaptureSettings settings) {
    _settings = settings;
    _clearError();
    notifyListeners();
  }

  /// Add source image for fromImages mode
  Future<void> addSourceImage(String imagePath) async {
    try {
      // Validate image exists
      final file = File(imagePath);
      if (!await file.exists()) {
        throw Exception('Image file not found');
      }

      _sourceImagePaths.add(imagePath);
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('Failed to add image: $e');
    }
  }

  /// Remove source image
  void removeSourceImage(int index) {
    if (index >= 0 && index < _sourceImagePaths.length) {
      _sourceImagePaths.removeAt(index);
      notifyListeners();
    }
  }

  /// Clear all source images
  void clearSourceImages() {
    _sourceImagePaths.clear();
    notifyListeners();
  }

  /// Reorder source images
  void reorderSourceImages(int oldIndex, int newIndex) {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }
    final item = _sourceImagePaths.removeAt(oldIndex);
    _sourceImagePaths.insert(newIndex, item);
    notifyListeners();
  }

  /// Set web page capture options
  void setWebPageOptions(WebPageCaptureOptions options) {
    _webPageOptions = options;
    _clearError();
    notifyListeners();
  }

  /// Start auto scroll capture
  Future<void> startAutoScrollCapture() async {
    if (_settings.mode != CaptureMode.autoScroll) return;

    try {
      _setCapturing(true);
      _clearError();
      _clearResult();

      // Initialize progress
      _progress = const CaptureProgress(
        currentStep: 0,
        totalSteps: 10, // Estimated steps
        currentAction: '准备开始自动滚动截图...',
        progress: 0.0,
        capturedImages: [],
      );
      notifyListeners();

      // TODO: Implement actual auto scroll capture using platform channels
      // For now, simulate the process
      await _simulateAutoScrollCapture();

    } catch (e) {
      _setError('Auto scroll capture failed: $e');
    } finally {
      _setCapturing(false);
    }
  }

  /// Start manual capture
  Future<void> startManualCapture() async {
    if (_settings.mode != CaptureMode.manual) return;

    try {
      _setCapturing(true);
      _clearError();
      _clearResult();

      _progress = const CaptureProgress(
        currentStep: 0,
        totalSteps: 5,
        currentAction: '等待手动截图...',
        progress: 0.0,
        capturedImages: [],
      );
      notifyListeners();

      // TODO: Implement manual capture interface
      await _simulateManualCapture();

    } catch (e) {
      _setError('Manual capture failed: $e');
    } finally {
      _setCapturing(false);
    }
  }

  /// Process images from source paths
  Future<void> processFromImages() async {
    if (_settings.mode != CaptureMode.fromImages || _sourceImagePaths.isEmpty) {
      _setError('No source images selected');
      return;
    }

    try {
      _setProcessing(true);
      _clearError();
      _clearResult();

      _progress = CaptureProgress(
        currentStep: 0,
        totalSteps: _sourceImagePaths.length + 2,
        currentAction: '加载源图片...',
        progress: 0.0,
        capturedImages: const [],
      );
      notifyListeners();

      // Load and process images
      final images = <Uint8List>[];
      for (int i = 0; i < _sourceImagePaths.length; i++) {
        _progress = _progress!.copyWith(
          currentStep: i + 1,
          currentAction: '加载图片 ${i + 1}/${_sourceImagePaths.length}',
          progress: (i + 1) / (_sourceImagePaths.length + 2),
        );
        notifyListeners();

        final imageData = await _imageService.loadImageFromPath(_sourceImagePaths[i]);
        if (imageData != null) {
          images.add(imageData.bytes);
        }
        
        // Simulate processing delay
        await Future.delayed(const Duration(milliseconds: 500));
      }

      // Stitch images
      _progress = _progress!.copyWith(
        currentStep: _sourceImagePaths.length + 1,
        currentAction: '拼接图片...',
        progress: (_sourceImagePaths.length + 1) / (_sourceImagePaths.length + 2),
        capturedImages: images,
      );
      notifyListeners();

      final result = await _stitchImages(images);
      
      // Save to gallery
      if (result != null) {
        await _galleryService.saveLongScreenshotToGallery(
          screenshotPath: await _saveResultToFile(result),
          name: 'long_screenshot_${DateTime.now().millisecondsSinceEpoch}',
          sourceCount: _sourceImagePaths.length,
        );
      }

      _progress = _progress!.copyWith(
        currentStep: _sourceImagePaths.length + 2,
        currentAction: '完成',
        progress: 1.0,
      );
      notifyListeners();

    } catch (e) {
      _setError('Failed to process images: $e');
    } finally {
      _setProcessing(false);
    }
  }

  /// Capture web page
  Future<void> captureWebPage() async {
    if (_settings.mode != CaptureMode.webPage || _webPageOptions == null) {
      _setError('Web page options not set');
      return;
    }

    try {
      _setProcessing(true);
      _clearError();
      _clearResult();

      _progress = const CaptureProgress(
        currentStep: 0,
        totalSteps: 4,
        currentAction: '加载网页...',
        progress: 0.0,
        capturedImages: [],
      );
      notifyListeners();

      // TODO: Implement actual web page capture
      await _simulateWebPageCapture();

    } catch (e) {
      _setError('Web page capture failed: $e');
    } finally {
      _setProcessing(false);
    }
  }

  /// Cancel current operation
  void cancelOperation() {
    _setCapturing(false);
    _setProcessing(false);
    _progress = null;
    _clearError();
    notifyListeners();
  }

  // Private methods
  void _setCapturing(bool capturing) {
    _isCapturing = capturing;
    notifyListeners();
  }

  void _setProcessing(bool processing) {
    _isProcessing = processing;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  void _clearResult() {
    _result = null;
    _progress = null;
  }

  // Simulation methods (to be replaced with actual implementations)
  Future<void> _simulateAutoScrollCapture() async {
    final images = <Uint8List>[];
    
    for (int i = 0; i < 8; i++) {
      await Future.delayed(const Duration(milliseconds: 800));
      
      _progress = _progress!.copyWith(
        currentStep: i + 1,
        currentAction: '截图 ${i + 1}/8',
        progress: (i + 1) / 10,
      );
      notifyListeners();
      
      // Simulate capturing a screenshot
      // In real implementation, this would use platform channels
    }
    
    // Simulate stitching
    _progress = _progress!.copyWith(
      currentStep: 9,
      currentAction: '拼接截图...',
      progress: 0.9,
    );
    notifyListeners();
    
    await Future.delayed(const Duration(milliseconds: 1000));
    
    _progress = _progress!.copyWith(
      currentStep: 10,
      currentAction: '完成',
      progress: 1.0,
    );
    notifyListeners();
  }

  Future<void> _simulateManualCapture() async {
    // Simulate manual capture process
    await Future.delayed(const Duration(seconds: 2));
  }

  Future<void> _simulateWebPageCapture() async {
    final steps = ['加载网页...', '等待渲染...', '截图...', '完成'];
    
    for (int i = 0; i < steps.length; i++) {
      await Future.delayed(const Duration(milliseconds: 1000));
      
      _progress = _progress!.copyWith(
        currentStep: i + 1,
        currentAction: steps[i],
        progress: (i + 1) / steps.length,
      );
      notifyListeners();
    }
  }

  Future<LongScreenshotResult?> _stitchImages(List<Uint8List> images) async {
    // TODO: Implement actual image stitching using Rust backend
    // For now, return a mock result
    await Future.delayed(const Duration(milliseconds: 1000));
    
    if (images.isNotEmpty) {
      final mockResult = LongScreenshotResult(
        imageData: images.first,
        width: 1080,
        height: images.length * 1920,
        fileSize: images.fold(0, (sum, img) => sum + img.length),
        processingTime: const Duration(seconds: 2),
        sourceImageCount: images.length,
        settings: _settings,
        metadata: {
          'algorithm': _settings.algorithm.name,
          'created_at': DateTime.now().toIso8601String(),
        },
      );
      
      _result = mockResult;
      return mockResult;
    }
    
    return null;
  }

  Future<String> _saveResultToFile(LongScreenshotResult result) async {
    // TODO: Implement actual file saving
    return '/tmp/long_screenshot_${DateTime.now().millisecondsSinceEpoch}.png';
  }
}
