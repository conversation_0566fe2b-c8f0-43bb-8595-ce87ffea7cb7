import 'package:flutter/foundation.dart';

/// Long screenshot capture mode
enum CaptureMode {
  /// Capture scrolling content automatically
  autoScroll,
  
  /// Manual capture with user control
  manual,
  
  /// Capture from existing images
  fromImages,
  
  /// Capture web page
  webPage,
}

extension CaptureModeExtension on CaptureMode {
  String get displayName {
    switch (this) {
      case CaptureMode.autoScroll:
        return '自动滚动截图';
      case CaptureMode.manual:
        return '手动拼接截图';
      case CaptureMode.fromImages:
        return '图片拼接';
      case CaptureMode.webPage:
        return '网页截图';
    }
  }
  
  String get description {
    switch (this) {
      case CaptureMode.autoScroll:
        return '自动滚动页面并拼接成长截图';
      case CaptureMode.manual:
        return '手动控制截图区域和拼接';
      case CaptureMode.fromImages:
        return '从多张图片拼接成长截图';
      case CaptureMode.webPage:
        return '输入网址生成完整网页截图';
    }
  }
  
  String get icon {
    switch (this) {
      case CaptureMode.autoScroll:
        return '📱';
      case CaptureMode.manual:
        return '✋';
      case CaptureMode.fromImages:
        return '🖼️';
      case CaptureMode.webPage:
        return '🌐';
    }
  }
}

/// Stitching algorithm for combining screenshots
enum StitchingAlgorithm {
  /// Simple vertical concatenation
  simple,
  
  /// Smart overlap detection and removal
  smartOverlap,
  
  /// Feature-based alignment
  featureBased,
  
  /// Template matching
  templateMatching,
}

extension StitchingAlgorithmExtension on StitchingAlgorithm {
  String get displayName {
    switch (this) {
      case StitchingAlgorithm.simple:
        return '简单拼接';
      case StitchingAlgorithm.smartOverlap:
        return '智能重叠检测';
      case StitchingAlgorithm.featureBased:
        return '特征点对齐';
      case StitchingAlgorithm.templateMatching:
        return '模板匹配';
    }
  }
  
  String get description {
    switch (this) {
      case StitchingAlgorithm.simple:
        return '直接垂直拼接，速度最快';
      case StitchingAlgorithm.smartOverlap:
        return '自动检测重叠区域并去除';
      case StitchingAlgorithm.featureBased:
        return '基于特征点精确对齐';
      case StitchingAlgorithm.templateMatching:
        return '使用模板匹配确保连续性';
    }
  }
}

/// Long screenshot capture settings
class CaptureSettings {
  final CaptureMode mode;
  final StitchingAlgorithm algorithm;
  final double overlapThreshold;
  final int maxImages;
  final bool removeStatusBar;
  final bool removeNavigationBar;
  final bool optimizeForText;
  final double compressionQuality;
  
  const CaptureSettings({
    this.mode = CaptureMode.autoScroll,
    this.algorithm = StitchingAlgorithm.smartOverlap,
    this.overlapThreshold = 0.1,
    this.maxImages = 50,
    this.removeStatusBar = true,
    this.removeNavigationBar = true,
    this.optimizeForText = true,
    this.compressionQuality = 0.9,
  });
  
  CaptureSettings copyWith({
    CaptureMode? mode,
    StitchingAlgorithm? algorithm,
    double? overlapThreshold,
    int? maxImages,
    bool? removeStatusBar,
    bool? removeNavigationBar,
    bool? optimizeForText,
    double? compressionQuality,
  }) {
    return CaptureSettings(
      mode: mode ?? this.mode,
      algorithm: algorithm ?? this.algorithm,
      overlapThreshold: overlapThreshold ?? this.overlapThreshold,
      maxImages: maxImages ?? this.maxImages,
      removeStatusBar: removeStatusBar ?? this.removeStatusBar,
      removeNavigationBar: removeNavigationBar ?? this.removeNavigationBar,
      optimizeForText: optimizeForText ?? this.optimizeForText,
      compressionQuality: compressionQuality ?? this.compressionQuality,
    );
  }
}

/// Capture progress information
class CaptureProgress {
  final int currentStep;
  final int totalSteps;
  final String currentAction;
  final double progress;
  final List<Uint8List> capturedImages;
  
  const CaptureProgress({
    required this.currentStep,
    required this.totalSteps,
    required this.currentAction,
    required this.progress,
    required this.capturedImages,
  });
  
  bool get isComplete => currentStep >= totalSteps;
  
  CaptureProgress copyWith({
    int? currentStep,
    int? totalSteps,
    String? currentAction,
    double? progress,
    List<Uint8List>? capturedImages,
  }) {
    return CaptureProgress(
      currentStep: currentStep ?? this.currentStep,
      totalSteps: totalSteps ?? this.totalSteps,
      currentAction: currentAction ?? this.currentAction,
      progress: progress ?? this.progress,
      capturedImages: capturedImages ?? this.capturedImages,
    );
  }
}

/// Long screenshot result
class LongScreenshotResult {
  final Uint8List imageData;
  final int width;
  final int height;
  final int fileSize;
  final Duration processingTime;
  final int sourceImageCount;
  final CaptureSettings settings;
  final Map<String, dynamic> metadata;
  
  const LongScreenshotResult({
    required this.imageData,
    required this.width,
    required this.height,
    required this.fileSize,
    required this.processingTime,
    required this.sourceImageCount,
    required this.settings,
    required this.metadata,
  });
  
  double get aspectRatio => height / width;
  
  String get fileSizeFormatted {
    if (fileSize < 1024) return '${fileSize}B';
    if (fileSize < 1024 * 1024) return '${(fileSize / 1024).toStringAsFixed(1)}KB';
    return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)}MB';
  }
}

/// Web page capture options
class WebPageCaptureOptions {
  final String url;
  final int viewportWidth;
  final int viewportHeight;
  final bool fullPage;
  final bool removeAds;
  final bool darkMode;
  final int delay;
  final Map<String, String> headers;
  
  const WebPageCaptureOptions({
    required this.url,
    this.viewportWidth = 1200,
    this.viewportHeight = 800,
    this.fullPage = true,
    this.removeAds = false,
    this.darkMode = false,
    this.delay = 2000,
    this.headers = const {},
  });
  
  WebPageCaptureOptions copyWith({
    String? url,
    int? viewportWidth,
    int? viewportHeight,
    bool? fullPage,
    bool? removeAds,
    bool? darkMode,
    int? delay,
    Map<String, String>? headers,
  }) {
    return WebPageCaptureOptions(
      url: url ?? this.url,
      viewportWidth: viewportWidth ?? this.viewportWidth,
      viewportHeight: viewportHeight ?? this.viewportHeight,
      fullPage: fullPage ?? this.fullPage,
      removeAds: removeAds ?? this.removeAds,
      darkMode: darkMode ?? this.darkMode,
      delay: delay ?? this.delay,
      headers: headers ?? this.headers,
    );
  }
}
