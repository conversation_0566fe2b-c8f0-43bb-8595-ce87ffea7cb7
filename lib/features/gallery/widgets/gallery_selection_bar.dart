import 'package:flutter/material.dart';
import '../controllers/gallery_controller.dart';
import '../../../core/design_system/colors.dart';

/// Gallery selection bar widget
class GallerySelectionBar extends StatelessWidget {
  final GalleryController controller;
  final VoidCallback onSelectAll;
  final VoidCallback onClearSelection;
  final VoidCallback onDelete;

  const GallerySelectionBar({
    super.key,
    required this.controller,
    required this.onSelectAll,
    required this.onClearSelection,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: TuShenColors.primaryGradient,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: TuShenColors.primary.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Selection count
          Expanded(
            child: Text(
              '${controller.selectedCount} selected',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
          ),
          
          // Action buttons
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Select all button
              _ActionButton(
                icon: Icons.select_all,
                onPressed: onSelectAll,
                tooltip: 'Select All',
              ),
              
              const SizedBox(width: 8),
              
              // Share button
              _ActionButton(
                icon: Icons.share,
                onPressed: controller.selectedCount > 0 
                    ? () => _handleShare(context)
                    : null,
                tooltip: 'Share',
              ),
              
              const SizedBox(width: 8),
              
              // Delete button
              _ActionButton(
                icon: Icons.delete,
                onPressed: controller.selectedCount > 0 ? onDelete : null,
                tooltip: 'Delete',
              ),
              
              const SizedBox(width: 8),
              
              // Clear selection button
              _ActionButton(
                icon: Icons.close,
                onPressed: onClearSelection,
                tooltip: 'Clear Selection',
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _handleShare(BuildContext context) {
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Share functionality coming soon'),
        duration: Duration(seconds: 2),
      ),
    );
  }
}

/// Action button widget for selection bar
class _ActionButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback? onPressed;
  final String tooltip;

  const _ActionButton({
    required this.icon,
    required this.onPressed,
    required this.tooltip,
  });

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: tooltip,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: onPressed != null 
                  ? Colors.white.withValues(alpha: 0.2)
                  : Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: onPressed != null 
                  ? Colors.white
                  : Colors.white.withValues(alpha: 0.5),
              size: 20,
            ),
          ),
        ),
      ),
    );
  }
}
