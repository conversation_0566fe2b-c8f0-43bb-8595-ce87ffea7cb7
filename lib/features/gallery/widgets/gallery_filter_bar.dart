import 'package:flutter/material.dart';
import '../controllers/gallery_controller.dart';
import '../models/gallery_models.dart';
import '../../../core/design_system/colors.dart';

/// Gallery filter bar widget
class GalleryFilterBar extends StatelessWidget {
  final GalleryController controller;
  final Function(GalleryFilter) onFilterChanged;

  const GalleryFilterBar({
    super.key,
    required this.controller,
    required this.onFilterChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: GalleryFilter.values.length,
        separatorBuilder: (context, index) => const SizedBox(width: 8),
        itemBuilder: (context, index) {
          final filter = GalleryFilter.values[index];
          final isSelected = controller.currentFilter == filter;
          
          return _FilterChip(
            filter: filter,
            isSelected: isSelected,
            onTap: () => onFilterChanged(filter),
          );
        },
      ),
    );
  }
}

/// Individual filter chip widget
class _FilterChip extends StatelessWidget {
  final GalleryFilter filter;
  final bool isSelected;
  final VoidCallback onTap;

  const _FilterChip({
    required this.filter,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          gradient: isSelected
              ? LinearGradient(
                  colors: TuShenColors.primaryGradient,
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                )
              : null,
          color: isSelected ? null : Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(20),
          border: isSelected
              ? null
              : Border.all(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: TuShenColors.primary.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              _getFilterIcon(filter),
              size: 16,
              color: isSelected
                  ? Colors.white
                  : Theme.of(context).colorScheme.onSurface,
            ),
            const SizedBox(width: 6),
            Text(
              _getFilterName(filter),
              style: TextStyle(
                color: isSelected
                    ? Colors.white
                    : Theme.of(context).colorScheme.onSurface,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getFilterIcon(GalleryFilter filter) {
    switch (filter) {
      case GalleryFilter.all:
        return Icons.grid_view;
      case GalleryFilter.images:
        return Icons.image;
      case GalleryFilter.collages:
        return Icons.dashboard;
      case GalleryFilter.livePhotos:
        return Icons.motion_photos_on;
      case GalleryFilter.videos:
        return Icons.videocam;
      case GalleryFilter.recent:
        return Icons.schedule;
      case GalleryFilter.favorites:
        return Icons.favorite;
    }
  }

  String _getFilterName(GalleryFilter filter) {
    switch (filter) {
      case GalleryFilter.all:
        return 'All';
      case GalleryFilter.images:
        return 'Images';
      case GalleryFilter.collages:
        return 'Collages';
      case GalleryFilter.livePhotos:
        return 'Live Photos';
      case GalleryFilter.videos:
        return 'Videos';
      case GalleryFilter.recent:
        return 'Recent';
      case GalleryFilter.favorites:
        return 'Favorites';
    }
  }
}
