import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import '../controllers/gallery_controller.dart';
import '../models/gallery_models.dart';
import 'gallery_item_card.dart';

/// Gallery grid widget for displaying items
class GalleryGrid extends StatelessWidget {
  final GalleryController controller;
  final List<GalleryItem> items;
  final Function(GalleryItem) onItemTap;
  final Function(GalleryItem) onItemLongPress;

  const GalleryGrid({
    super.key,
    required this.controller,
    required this.items,
    required this.onItemTap,
    required this.onItemLongPress,
  });

  @override
  Widget build(BuildContext context) {
    if (items.isEmpty) {
      return _buildEmptyState(context);
    }

    return Padding(
      padding: const EdgeInsets.all(16),
      child: _buildGrid(context),
    );
  }

  Widget _buildGrid(BuildContext context) {
    switch (controller.viewMode) {
      case GalleryViewMode.grid:
        return _buildStandardGrid(context);
      case GalleryViewMode.list:
        return _buildListView(context);
      case GalleryViewMode.masonry:
        return _buildMasonryGrid(context);
    }
  }

  Widget _buildStandardGrid(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1.0,
      ),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        return GalleryItemCard(
          item: item,
          isSelected: controller.selection.isSelected(item.id),
          isSelectionMode: controller.isSelectionMode,
          onTap: () => onItemTap(item),
          onLongPress: () => onItemLongPress(item),
          onFavoriteToggle: () => controller.toggleFavorite(item.id),
        );
      },
    );
  }

  Widget _buildListView(BuildContext context) {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: items.length,
      separatorBuilder: (context, index) => const SizedBox(height: 8),
      itemBuilder: (context, index) {
        final item = items[index];
        return GalleryItemCard(
          item: item,
          isSelected: controller.selection.isSelected(item.id),
          isSelectionMode: controller.isSelectionMode,
          onTap: () => onItemTap(item),
          onLongPress: () => onItemLongPress(item),
          onFavoriteToggle: () => controller.toggleFavorite(item.id),
          isListMode: true,
        );
      },
    );
  }

  Widget _buildMasonryGrid(BuildContext context) {
    return MasonryGridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 12,
      mainAxisSpacing: 12,
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        return GalleryItemCard(
          item: item,
          isSelected: controller.selection.isSelected(item.id),
          isSelectionMode: controller.isSelectionMode,
          onTap: () => onItemTap(item),
          onLongPress: () => onItemLongPress(item),
          onFavoriteToggle: () => controller.toggleFavorite(item.id),
          isMasonryMode: true,
        );
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    String message;
    IconData icon;
    
    if (controller.searchState.isActive) {
      message = 'No results found for "${controller.searchState.query}"';
      icon = Icons.search_off;
    } else {
      switch (controller.currentFilter) {
        case GalleryFilter.all:
          message = 'No items in gallery';
          icon = Icons.photo_library_outlined;
          break;
        case GalleryFilter.images:
          message = 'No images found';
          icon = Icons.image_not_supported;
          break;
        case GalleryFilter.collages:
          message = 'No collages created yet';
          icon = Icons.dashboard_outlined;
          break;
        case GalleryFilter.livePhotos:
          message = 'No live photos found';
          icon = Icons.motion_photos_off;
          break;
        case GalleryFilter.videos:
          message = 'No videos found';
          icon = Icons.videocam_off;
          break;
        case GalleryFilter.recent:
          message = 'No recent items';
          icon = Icons.schedule;
          break;
        case GalleryFilter.favorites:
          message = 'No favorites yet';
          icon = Icons.favorite_border;
          break;
      }
    }

    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.4),
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            textAlign: TextAlign.center,
          ),
          if (!controller.searchState.isActive && 
              controller.currentFilter == GalleryFilter.all) ...[
            const SizedBox(height: 8),
            Text(
              'Start by creating a collage or importing photos',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}
