import 'image_data_compression.dart';

/// 压缩设置
class CompressionSettings {
  final int quality; // 1-100
  final String format; // JPEG, PNG, WebP
  final double? targetSizeKB; // 目标大小（KB）
  final int? resizeWidth; // 调整宽度
  final int? resizeHeight; // 调整高度
  final bool maintainAspectRatio; // 保持宽高比
  final bool preserveMetadata; // 保留元数据
  final bool progressive; // 渐进式JPEG

  const CompressionSettings({
    this.quality = 85,
    this.format = 'JPEG',
    this.targetSizeKB,
    this.resizeWidth,
    this.resizeHeight,
    this.maintainAspectRatio = true,
    this.preserveMetadata = false,
    this.progressive = false,
  });

  CompressionSettings copyWith({
    int? quality,
    String? format,
    double? targetSizeKB,
    int? resizeWidth,
    int? resizeHeight,
    bool? maintainAspectRatio,
    bool? preserveMetadata,
    bool? progressive,
  }) {
    return CompressionSettings(
      quality: quality ?? this.quality,
      format: format ?? this.format,
      targetSizeKB: targetSizeKB ?? this.targetSizeKB,
      resizeWidth: resizeWidth ?? this.resizeWidth,
      resizeHeight: resizeHeight ?? this.resizeHeight,
      maintainAspectRatio: maintainAspectRatio ?? this.maintainAspectRatio,
      preserveMetadata: preserveMetadata ?? this.preserveMetadata,
      progressive: progressive ?? this.progressive,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CompressionSettings &&
        other.quality == quality &&
        other.format == format &&
        other.targetSizeKB == targetSizeKB &&
        other.resizeWidth == resizeWidth &&
        other.resizeHeight == resizeHeight &&
        other.maintainAspectRatio == maintainAspectRatio &&
        other.preserveMetadata == preserveMetadata &&
        other.progressive == progressive;
  }

  @override
  int get hashCode {
    return Object.hash(
      quality,
      format,
      targetSizeKB,
      resizeWidth,
      resizeHeight,
      maintainAspectRatio,
      preserveMetadata,
      progressive,
    );
  }

  @override
  String toString() {
    return 'CompressionSettings('
        'quality: $quality, '
        'format: $format, '
        'targetSizeKB: $targetSizeKB, '
        'resizeWidth: $resizeWidth, '
        'resizeHeight: $resizeHeight, '
        'maintainAspectRatio: $maintainAspectRatio, '
        'preserveMetadata: $preserveMetadata, '
        'progressive: $progressive'
        ')';
  }
}

/// 压缩结果
class CompressionResult {
  final ImageDataCompression compressedImage;
  final int originalSize; // 原始大小（字节）
  final int compressedSize; // 压缩后大小（字节）
  final double compressionRatio; // 压缩比例
  final int spaceSaved; // 节省空间（字节）
  final double spaceSavedPercentage; // 节省空间百分比
  final int finalQuality; // 最终质量
  final int iterations; // 迭代次数
  final bool targetAchieved; // 是否达到目标大小
  final Duration processingTime; // 处理时间

  const CompressionResult({
    required this.compressedImage,
    required this.originalSize,
    required this.compressedSize,
    required this.compressionRatio,
    required this.spaceSaved,
    required this.spaceSavedPercentage,
    required this.finalQuality,
    required this.iterations,
    required this.targetAchieved,
    required this.processingTime,
  });

  @override
  String toString() {
    return 'CompressionResult('
        'originalSize: $originalSize, '
        'compressedSize: $compressedSize, '
        'compressionRatio: $compressionRatio, '
        'spaceSaved: $spaceSaved, '
        'spaceSavedPercentage: $spaceSavedPercentage, '
        'finalQuality: $finalQuality, '
        'iterations: $iterations, '
        'targetAchieved: $targetAchieved, '
        'processingTime: $processingTime'
        ')';
  }
}

/// 压缩预估结果
class CompressionEstimate {
  final int estimatedSize; // 预估大小（字节）
  final double estimatedCompressionRatio; // 预估压缩比例
  final int estimatedSpaceSaved; // 预估节省空间（字节）

  const CompressionEstimate({
    required this.estimatedSize,
    required this.estimatedCompressionRatio,
    required this.estimatedSpaceSaved,
  });

  @override
  String toString() {
    return 'CompressionEstimate('
        'estimatedSize: $estimatedSize, '
        'estimatedCompressionRatio: $estimatedCompressionRatio, '
        'estimatedSpaceSaved: $estimatedSpaceSaved'
        ')';
  }
}

/// 压缩质量预设
enum CompressionQuality {
  maximum(95, '最高质量'),
  high(85, '高质量'),
  medium(70, '中等质量'),
  low(50, '低质量'),
  custom(0, '自定义');

  const CompressionQuality(this.value, this.label);

  final int value;
  final String label;

  static CompressionQuality fromValue(int value) {
    for (final quality in CompressionQuality.values) {
      if (quality.value == value) return quality;
    }
    return CompressionQuality.custom;
  }
}

/// 目标大小预设
enum TargetSizePreset {
  none(null, '无限制'),
  small(100, '100 KB'),
  medium(500, '500 KB'),
  large(1000, '1 MB'),
  custom(0, '自定义');

  const TargetSizePreset(this.sizeKB, this.label);

  final double? sizeKB;
  final String label;

  static TargetSizePreset fromSize(double? sizeKB) {
    if (sizeKB == null) return TargetSizePreset.none;
    for (final preset in TargetSizePreset.values) {
      if (preset.sizeKB == sizeKB) return preset;
    }
    return TargetSizePreset.custom;
  }
}

/// 输出格式选项
enum OutputFormat {
  jpeg('JPEG', 'jpg', '适合照片，文件较小'),
  png('PNG', 'png', '支持透明度，无损压缩'),
  webp('WebP', 'webp', '现代格式，压缩效果最佳');

  const OutputFormat(this.displayName, this.extension, this.description);

  final String displayName;
  final String extension;
  final String description;

  static OutputFormat fromString(String format) {
    switch (format.toUpperCase()) {
      case 'JPEG':
      case 'JPG':
        return OutputFormat.jpeg;
      case 'PNG':
        return OutputFormat.png;
      case 'WEBP':
        return OutputFormat.webp;
      default:
        return OutputFormat.jpeg;
    }
  }
}
