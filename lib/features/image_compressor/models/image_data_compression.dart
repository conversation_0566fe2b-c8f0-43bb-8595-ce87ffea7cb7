import 'dart:typed_data';

/// Image data model specifically for compression operations
class ImageDataCompression {
  final String id;
  final Uint8List data;
  final int width;
  final int height;
  final String format;
  final DateTime createdAt;

  const ImageDataCompression({
    required this.id,
    required this.data,
    required this.width,
    required this.height,
    required this.format,
    required this.createdAt,
  });

  /// Create from existing image data
  factory ImageDataCompression.fromBytes({
    required String id,
    required Uint8List data,
    required int width,
    required int height,
    required String format,
  }) {
    return ImageDataCompression(
      id: id,
      data: data,
      width: width,
      height: height,
      format: format,
      createdAt: DateTime.now(),
    );
  }

  /// Copy with new properties
  ImageDataCompression copyWith({
    String? id,
    Uint8List? data,
    int? width,
    int? height,
    String? format,
    DateTime? createdAt,
  }) {
    return ImageDataCompression(
      id: id ?? this.id,
      data: data ?? this.data,
      width: width ?? this.width,
      height: height ?? this.height,
      format: format ?? this.format,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// Get file size in bytes
  int get fileSize => data.length;

  /// Get aspect ratio
  double get aspectRatio => width / height;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ImageDataCompression && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ImageDataCompression(id: $id, size: ${width}x$height, format: $format, fileSize: ${fileSize} bytes)';
  }
}
