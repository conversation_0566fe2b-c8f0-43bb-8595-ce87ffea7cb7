import 'package:flutter/foundation.dart';

import '../../../core/services/image_processor_service.dart';
import '../models/compression_models.dart';
import '../models/image_data_compression.dart';

/// 图片压缩控制器
class ImageCompressorController extends ChangeNotifier {
  ImageDataCompression? _originalImage;
  ImageDataCompression? _compressedImage;
  CompressionSettings _settings = CompressionSettings();
  CompressionResult? _lastResult;
  bool _isCompressing = false;

  // Getters
  ImageDataCompression? get originalImage => _originalImage;
  ImageDataCompression? get compressedImage => _compressedImage;
  CompressionSettings get settings => _settings;
  CompressionResult? get lastResult => _lastResult;
  bool get isCompressing => _isCompressing;
  bool get hasImage => _originalImage != null;
  bool get hasCompressedImage => _compressedImage != null;

  /// 加载图片
  Future<void> loadImage(ImageDataCompression imageData) async {
    _originalImage = imageData;
    _compressedImage = null;
    _lastResult = null;
    notifyListeners();

    // 自动进行一次预估
    await _estimateCompression();
  }

  /// 更新压缩设置
  void updateSettings(CompressionSettings newSettings) {
    _settings = newSettings;
    notifyListeners();

    // 自动重新预估
    _estimateCompression();
  }

  /// 更新质量
  void updateQuality(double quality) {
    _settings = _settings.copyWith(quality: quality.round());
    notifyListeners();
    _estimateCompression();
  }

  /// 更新目标大小
  void updateTargetSize(double? targetSizeKB) {
    _settings = _settings.copyWith(targetSizeKB: targetSizeKB);
    notifyListeners();
    _estimateCompression();
  }

  /// 更新输出格式
  void updateFormat(String format) {
    _settings = _settings.copyWith(format: format);
    notifyListeners();
    _estimateCompression();
  }

  /// 更新是否保持宽高比
  void updateMaintainAspectRatio(bool maintain) {
    _settings = _settings.copyWith(maintainAspectRatio: maintain);
    notifyListeners();
  }

  /// 更新调整大小尺寸
  void updateResizeDimensions(int? width, int? height) {
    _settings = _settings.copyWith(resizeWidth: width, resizeHeight: height);
    notifyListeners();
    _estimateCompression();
  }

  /// 预估压缩结果
  Future<void> _estimateCompression() async {
    if (_originalImage == null) return;

    try {
      final imageProcessor = ImageProcessorService.instance;
      final estimate = await imageProcessor.estimateCompression(
        _originalImage!,
        _settings,
      );

      // 更新预估结果（这里可以显示给用户）
      // 注意：这是模拟的预估，实际实现需要调用Rust后端
      notifyListeners();
    } catch (e) {
      debugPrint('压缩预估失败: $e');
    }
  }

  /// 执行图片压缩
  Future<void> compressImage() async {
    if (_originalImage == null || _isCompressing) return;

    _isCompressing = true;
    notifyListeners();

    try {
      final imageProcessor = ImageProcessorService.instance;
      final result = await imageProcessor.compressImage(
        _originalImage!,
        _settings,
      );

      _compressedImage = result.compressedImage;
      _lastResult = result;

      notifyListeners();
    } catch (e) {
      debugPrint('图片压缩失败: $e');
      rethrow;
    } finally {
      _isCompressing = false;
      notifyListeners();
    }
  }

  /// 保存压缩后的图片
  Future<void> saveCompressedImage() async {
    if (_compressedImage == null) return;

    try {
      // TODO: Implement saving to storage service
      // For now, just simulate saving
      await Future.delayed(const Duration(milliseconds: 500));

      // 可以添加到最近工作或图库
      notifyListeners();
    } catch (e) {
      debugPrint('保存压缩图片失败: $e');
      rethrow;
    }
  }

  /// 重置压缩结果
  void resetCompression() {
    _compressedImage = null;
    _lastResult = null;
    notifyListeners();
  }

  /// 获取压缩比例
  double get compressionRatio {
    if (_lastResult == null) return 1.0;
    return _lastResult!.compressionRatio;
  }

  /// 获取节省的空间
  int get spaceSaved {
    if (_lastResult == null) return 0;
    return _lastResult!.spaceSaved;
  }

  /// 获取节省的空间百分比
  double get spaceSavedPercentage {
    if (_lastResult == null) return 0.0;
    return _lastResult!.spaceSavedPercentage;
  }

  /// 获取原始文件大小（字节）
  int get originalSize {
    return _originalImage?.data.length ?? 0;
  }

  /// 获取压缩后文件大小（字节）
  int get compressedSize {
    return _compressedImage?.data.length ?? 0;
  }

  /// 格式化文件大小
  String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }

  /// 获取支持的格式列表
  List<String> get supportedFormats => ['JPEG', 'PNG', 'WebP'];
}
