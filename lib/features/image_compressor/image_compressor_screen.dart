import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';

import 'controllers/image_compressor_controller.dart';
import 'models/image_data_compression.dart';
import 'widgets/compression_controls.dart';
import 'widgets/compression_preview.dart';
import 'widgets/compression_stats.dart';

/// 图片压缩页面
class ImageCompressorScreen extends StatefulWidget {
  final String? imageId;

  const ImageCompressorScreen({super.key, this.imageId});

  @override
  State<ImageCompressorScreen> createState() => _ImageCompressorScreenState();
}

class _ImageCompressorScreenState extends State<ImageCompressorScreen> {
  late ImageCompressorController _controller;
  final ImagePicker _picker = ImagePicker();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _controller = ImageCompressorController();
    _initializeFromArguments();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _initializeFromArguments() {
    if (widget.imageId != null) {
      _loadImageFromId(widget.imageId!);
    }
  }

  Future<void> _loadImageFromId(String imageId) async {
    setState(() => _isLoading = true);
    try {
      // TODO: Implement loading from storage service
      // For now, just show a message that this feature is not implemented
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('从存储加载图片功能待实现')));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('加载图片失败: $e')));
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  String _getImageFormat(String path) {
    final extension = path.split('.').last.toLowerCase();
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'JPEG';
      case 'png':
        return 'PNG';
      case 'webp':
        return 'WebP';
      default:
        return 'JPEG';
    }
  }

  Future<void> _pickImage() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 100,
      );

      if (image != null) {
        setState(() => _isLoading = true);

        final bytes = await image.readAsBytes();
        final imageData = ImageDataCompression(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          data: bytes,
          width: 0, // Will be determined by the processor
          height: 0,
          format: _getImageFormat(image.path),
          createdAt: DateTime.now(),
        );

        await _controller.loadImage(imageData);
        setState(() => _isLoading = false);
      }
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('选择图片失败: $e')));
      }
    }
  }

  Future<void> _compressImage() async {
    if (!_controller.hasImage) return;

    setState(() => _isLoading = true);
    try {
      await _controller.compressImage();
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('压缩完成')));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('压缩失败: $e')));
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  Future<void> _saveCompressedImage() async {
    if (!_controller.hasCompressedImage) return;

    setState(() => _isLoading = true);
    try {
      await _controller.saveCompressedImage();
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('保存成功')));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('保存失败: $e')));
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('图片压缩'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          if (_controller.hasImage)
            IconButton(
              icon: const Icon(Icons.compress),
              onPressed: _isLoading ? null : _compressImage,
              tooltip: '开始压缩',
            ),
          if (_controller.hasCompressedImage)
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _isLoading ? null : _saveCompressedImage,
              tooltip: '保存压缩后的图片',
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _controller.hasImage
          ? _buildCompressionInterface()
          : _buildEmptyState(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.compress,
            size: 80,
            color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
          ),
          const SizedBox(height: 24),
          Text(
            '选择图片开始压缩',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            '支持专业的图片压缩，可自定义目标大小',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: _pickImage,
            icon: const Icon(Icons.photo_library),
            label: const Text('选择图片'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompressionInterface() {
    return Column(
      children: [
        // 压缩预览区域
        Expanded(flex: 3, child: CompressionPreview(controller: _controller)),

        // 压缩统计信息
        if (_controller.hasCompressedImage)
          CompressionStats(controller: _controller),

        // 压缩控制面板
        CompressionControls(
          controller: _controller,
          onCompress: _compressImage,
          onSave: _saveCompressedImage,
          isLoading: _isLoading,
        ),
      ],
    );
  }
}
