import 'package:flutter/material.dart';
import 'dart:typed_data';

import '../controllers/image_compressor_controller.dart';

/// 压缩预览组件
class CompressionPreview extends StatefulWidget {
  final ImageCompressorController controller;

  const CompressionPreview({
    super.key,
    required this.controller,
  });

  @override
  State<CompressionPreview> createState() => _CompressionPreviewState();
}

class _CompressionPreviewState extends State<CompressionPreview>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _showComparison = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    widget.controller.addListener(_onControllerChanged);
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onControllerChanged);
    _tabController.dispose();
    super.dispose();
  }

  void _onControllerChanged() {
    if (mounted) {
      setState(() {
        _showComparison = widget.controller.hasCompressedImage;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.controller.hasImage) {
      return const Center(
        child: Text('请先选择图片'),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // 预览模式切换
          if (_showComparison) _buildViewModeToggle(),
          
          // 图片预览区域
          Expanded(
            child: _showComparison
                ? _buildComparisonView()
                : _buildSingleImageView(),
          ),
        ],
      ),
    );
  }

  Widget _buildViewModeToggle() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: TabBar(
        controller: _tabController,
        tabs: const [
          Tab(text: '对比视图'),
          Tab(text: '单独预览'),
        ],
        onTap: (index) {
          setState(() {
            _showComparison = index == 0;
          });
        },
      ),
    );
  }

  Widget _buildComparisonView() {
    return Row(
      children: [
        // 原始图片
        Expanded(
          child: _buildImageCard(
            title: '原始图片',
            imageData: widget.controller.originalImage!.data,
            size: widget.controller.originalSize,
            subtitle: '${widget.controller.originalImage!.width} × ${widget.controller.originalImage!.height}',
          ),
        ),
        
        const SizedBox(width: 16),
        
        // 压缩后图片
        Expanded(
          child: widget.controller.hasCompressedImage
              ? _buildImageCard(
                  title: '压缩后',
                  imageData: widget.controller.compressedImage!.data,
                  size: widget.controller.compressedSize,
                  subtitle: '${widget.controller.compressedImage!.width} × ${widget.controller.compressedImage!.height}',
                  isCompressed: true,
                )
              : _buildPlaceholderCard(),
        ),
      ],
    );
  }

  Widget _buildSingleImageView() {
    final imageData = widget.controller.hasCompressedImage
        ? widget.controller.compressedImage!.data
        : widget.controller.originalImage!.data;
    
    final size = widget.controller.hasCompressedImage
        ? widget.controller.compressedSize
        : widget.controller.originalSize;
    
    final title = widget.controller.hasCompressedImage ? '压缩后图片' : '原始图片';
    
    final image = widget.controller.hasCompressedImage
        ? widget.controller.compressedImage!
        : widget.controller.originalImage!;

    return _buildImageCard(
      title: title,
      imageData: imageData,
      size: size,
      subtitle: '${image.width} × ${image.height}',
      isCompressed: widget.controller.hasCompressedImage,
      fullWidth: true,
    );
  }

  Widget _buildImageCard({
    required String title,
    required Uint8List imageData,
    required int size,
    required String subtitle,
    bool isCompressed = false,
    bool fullWidth = false,
  }) {
    return Card(
      elevation: 2,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题栏
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: isCompressed
                  ? Theme.of(context).colorScheme.primaryContainer
                  : Theme.of(context).colorScheme.surfaceVariant,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${widget.controller.formatFileSize(size)} • $subtitle',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ),
          
          // 图片预览
          Expanded(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(8),
              child: ClipRRect(
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(12),
                  bottomRight: Radius.circular(12),
                ),
                child: Image.memory(
                  imageData,
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: Theme.of(context).colorScheme.errorContainer,
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.error_outline,
                              color: Theme.of(context).colorScheme.error,
                              size: 48,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              '图片加载失败',
                              style: TextStyle(
                                color: Theme.of(context).colorScheme.error,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlaceholderCard() {
    return Card(
      elevation: 2,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.compress,
              size: 64,
              color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              '压缩后的图片',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '点击"开始压缩"查看结果',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
