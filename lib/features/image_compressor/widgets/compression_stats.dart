import 'package:flutter/material.dart';

import '../controllers/image_compressor_controller.dart';

/// 压缩统计信息组件
class CompressionStats extends StatelessWidget {
  final ImageCompressorController controller;

  const CompressionStats({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    if (!controller.hasCompressedImage || controller.lastResult == null) {
      return const SizedBox.shrink();
    }

    final result = controller.lastResult!;
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            children: [
              Icon(
                Icons.analytics_outlined,
                color: Theme.of(context).colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                '压缩统计',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // 统计信息网格
          _buildStatsGrid(context, result),
          
          const SizedBox(height: 16),
          
          // 压缩效果指示器
          _buildCompressionIndicator(context, result),
        ],
      ),
    );
  }

  Widget _buildStatsGrid(BuildContext context, dynamic result) {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 2.5,
      mainAxisSpacing: 12,
      crossAxisSpacing: 12,
      children: [
        _buildStatCard(
          context,
          icon: Icons.file_download_outlined,
          label: '原始大小',
          value: controller.formatFileSize(result.originalSize),
          color: Theme.of(context).colorScheme.secondary,
        ),
        _buildStatCard(
          context,
          icon: Icons.compress,
          label: '压缩后',
          value: controller.formatFileSize(result.compressedSize),
          color: Theme.of(context).colorScheme.primary,
        ),
        _buildStatCard(
          context,
          icon: Icons.trending_down,
          label: '节省空间',
          value: controller.formatFileSize(result.spaceSaved),
          color: Theme.of(context).colorScheme.tertiary,
        ),
        _buildStatCard(
          context,
          icon: Icons.percent,
          label: '压缩比例',
          value: '${result.spaceSavedPercentage.toStringAsFixed(1)}%',
          color: Theme.of(context).colorScheme.error,
        ),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 16,
                color: color,
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  label,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: color,
                    fontWeight: FontWeight.w500,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompressionIndicator(BuildContext context, dynamic result) {
    final compressionRatio = result.compressionRatio;
    final percentage = (1 - compressionRatio) * 100;
    
    Color indicatorColor;
    String description;
    
    if (percentage >= 70) {
      indicatorColor = Colors.green;
      description = '优秀压缩效果';
    } else if (percentage >= 50) {
      indicatorColor = Colors.orange;
      description = '良好压缩效果';
    } else if (percentage >= 30) {
      indicatorColor = Colors.blue;
      description = '一般压缩效果';
    } else {
      indicatorColor = Colors.red;
      description = '压缩效果有限';
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '压缩效果',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              description,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: indicatorColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 8),
        
        // 进度条
        ClipRRect(
          borderRadius: BorderRadius.circular(4),
          child: LinearProgressIndicator(
            value: percentage / 100,
            backgroundColor: Theme.of(context).colorScheme.outline.withOpacity(0.2),
            valueColor: AlwaysStoppedAnimation<Color>(indicatorColor),
            minHeight: 8,
          ),
        ),
        
        const SizedBox(height: 8),
        
        // 详细信息
        if (result.targetAchieved != null) ...[
          Row(
            children: [
              Icon(
                result.targetAchieved ? Icons.check_circle : Icons.info,
                size: 16,
                color: result.targetAchieved ? Colors.green : Colors.orange,
              ),
              const SizedBox(width: 4),
              Text(
                result.targetAchieved ? '已达到目标大小' : '未完全达到目标大小',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: result.targetAchieved ? Colors.green : Colors.orange,
                ),
              ),
            ],
          ),
        ],
        
        if (result.iterations != null && result.iterations > 1) ...[
          const SizedBox(height: 4),
          Text(
            '经过 ${result.iterations} 次迭代优化',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
        ],
        
        if (result.processingTime != null) ...[
          const SizedBox(height: 4),
          Text(
            '处理时间: ${result.processingTime.inMilliseconds}ms',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
        ],
      ],
    );
  }
}
