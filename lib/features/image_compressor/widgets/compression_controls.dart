import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../controllers/image_compressor_controller.dart';
import '../models/compression_models.dart';

/// 压缩控制面板
class CompressionControls extends StatefulWidget {
  final ImageCompressorController controller;
  final VoidCallback onCompress;
  final VoidCallback onSave;
  final bool isLoading;

  const CompressionControls({
    super.key,
    required this.controller,
    required this.onCompress,
    required this.onSave,
    required this.isLoading,
  });

  @override
  State<CompressionControls> createState() => _CompressionControlsState();
}

class _CompressionControlsState extends State<CompressionControls> {
  final TextEditingController _targetSizeController = TextEditingController();
  final TextEditingController _widthController = TextEditingController();
  final TextEditingController _heightController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _updateControllers();
  }

  @override
  void dispose() {
    _targetSizeController.dispose();
    _widthController.dispose();
    _heightController.dispose();
    super.dispose();
  }

  void _updateControllers() {
    final settings = widget.controller.settings;
    _targetSizeController.text = settings.targetSizeKB?.toString() ?? '';
    _widthController.text = settings.resizeWidth?.toString() ?? '';
    _heightController.text = settings.resizeHeight?.toString() ?? '';
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 质量控制
          _buildQualityControl(),
          const SizedBox(height: 16),
          
          // 格式选择
          _buildFormatSelection(),
          const SizedBox(height: 16),
          
          // 目标大小控制
          _buildTargetSizeControl(),
          const SizedBox(height: 16),
          
          // 尺寸调整
          _buildResizeControls(),
          const SizedBox(height: 16),
          
          // 高级选项
          _buildAdvancedOptions(),
          const SizedBox(height: 20),
          
          // 操作按钮
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildQualityControl() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '压缩质量',
              style: Theme.of(context).textTheme.titleSmall,
            ),
            Text(
              '${widget.controller.settings.quality}%',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Slider(
          value: widget.controller.settings.quality.toDouble(),
          min: 1,
          max: 100,
          divisions: 99,
          onChanged: widget.isLoading ? null : (value) {
            widget.controller.updateQuality(value);
          },
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('低质量', style: Theme.of(context).textTheme.bodySmall),
            Text('高质量', style: Theme.of(context).textTheme.bodySmall),
          ],
        ),
      ],
    );
  }

  Widget _buildFormatSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '输出格式',
          style: Theme.of(context).textTheme.titleSmall,
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: OutputFormat.values.map((format) {
            final isSelected = widget.controller.settings.format == format.displayName;
            return ChoiceChip(
              label: Text(format.displayName),
              selected: isSelected,
              onSelected: widget.isLoading ? null : (selected) {
                if (selected) {
                  widget.controller.updateFormat(format.displayName);
                }
              },
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildTargetSizeControl() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '目标大小 (KB)',
          style: Theme.of(context).textTheme.titleSmall,
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _targetSizeController,
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                ],
                decoration: const InputDecoration(
                  hintText: '留空表示无限制',
                  border: OutlineInputBorder(),
                  isDense: true,
                ),
                onChanged: (value) {
                  final size = double.tryParse(value);
                  widget.controller.updateTargetSize(size);
                },
              ),
            ),
            const SizedBox(width: 8),
            IconButton(
              icon: const Icon(Icons.clear),
              onPressed: () {
                _targetSizeController.clear();
                widget.controller.updateTargetSize(null);
              },
              tooltip: '清除目标大小',
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildResizeControls() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '调整尺寸',
          style: Theme.of(context).textTheme.titleSmall,
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _widthController,
                keyboardType: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                decoration: const InputDecoration(
                  labelText: '宽度',
                  border: OutlineInputBorder(),
                  isDense: true,
                ),
                onChanged: (value) {
                  final width = int.tryParse(value);
                  final height = int.tryParse(_heightController.text);
                  widget.controller.updateResizeDimensions(width, height);
                },
              ),
            ),
            const SizedBox(width: 8),
            const Text('×'),
            const SizedBox(width: 8),
            Expanded(
              child: TextField(
                controller: _heightController,
                keyboardType: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                decoration: const InputDecoration(
                  labelText: '高度',
                  border: OutlineInputBorder(),
                  isDense: true,
                ),
                onChanged: (value) {
                  final width = int.tryParse(_widthController.text);
                  final height = int.tryParse(value);
                  widget.controller.updateResizeDimensions(width, height);
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAdvancedOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '高级选项',
          style: Theme.of(context).textTheme.titleSmall,
        ),
        const SizedBox(height: 8),
        CheckboxListTile(
          title: const Text('保持宽高比'),
          value: widget.controller.settings.maintainAspectRatio,
          onChanged: widget.isLoading ? null : (value) {
            widget.controller.updateMaintainAspectRatio(value ?? true);
          },
          dense: true,
          contentPadding: EdgeInsets.zero,
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: widget.isLoading ? null : widget.onCompress,
            icon: widget.isLoading
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.compress),
            label: Text(widget.isLoading ? '压缩中...' : '开始压缩'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        if (widget.controller.hasCompressedImage) ...[
          const SizedBox(width: 12),
          Expanded(
            child: OutlinedButton.icon(
              onPressed: widget.isLoading ? null : widget.onSave,
              icon: const Icon(Icons.save),
              label: const Text('保存结果'),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ],
    );
  }
}
