import 'package:flutter/material.dart';
import '../../core/services/ffi_test_service.dart';

/// Debug page for testing FFI integration
class FFITestPage extends StatefulWidget {
  const FFITestPage({super.key});

  @override
  State<FFITestPage> createState() => _FFITestPageState();
}

class _FFITestPageState extends State<FFITestPage> {
  final FFITestService _testService = FFITestService.instance;
  Map<String, bool>? _testResults;
  bool _isRunningTests = false;
  String _statusMessage = 'Ready to run FFI tests';

  @override
  void initState() {
    super.initState();
    _checkServiceStatus();
  }

  void _checkServiceStatus() {
    setState(() {
      if (_testService.allServicesReady) {
        _statusMessage = '✅ All FFI services are ready';
      } else {
        _statusMessage = '⚠️ FFI services are not ready';
      }
    });
  }

  Future<void> _runTests() async {
    setState(() {
      _isRunningTests = true;
      _statusMessage = 'Running FFI tests...';
      _testResults = null;
    });

    try {
      final results = await _testService.runAllTests();
      setState(() {
        _testResults = results;
        _statusMessage = _testService.getTestSummary(results);
      });
    } catch (e) {
      setState(() {
        _statusMessage = '❌ Test execution failed: $e';
      });
    } finally {
      setState(() {
        _isRunningTests = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('FFI Test'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Status Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'FFI Status',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _statusMessage,
                      style: Theme.of(context).textTheme.bodyLarge,
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Test Button
            ElevatedButton(
              onPressed: _isRunningTests ? null : _runTests,
              child: _isRunningTests
                  ? const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                        SizedBox(width: 8),
                        Text('Running Tests...'),
                      ],
                    )
                  : const Text('Run FFI Tests'),
            ),
            
            const SizedBox(height: 16),
            
            // Test Results
            if (_testResults != null) ...[
              Text(
                'Test Results',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 8),
              Expanded(
                child: Card(
                  child: ListView(
                    padding: const EdgeInsets.all(16.0),
                    children: _testResults!.entries.map((entry) {
                      return ListTile(
                        leading: Icon(
                          entry.value ? Icons.check_circle : Icons.error,
                          color: entry.value ? Colors.green : Colors.red,
                        ),
                        title: Text(_formatTestName(entry.key)),
                        subtitle: Text(entry.value ? 'Passed' : 'Failed'),
                        trailing: entry.value
                            ? const Icon(Icons.thumb_up, color: Colors.green)
                            : const Icon(Icons.thumb_down, color: Colors.red),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ] else if (!_isRunningTests) ...[
              Expanded(
                child: Card(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.science,
                          size: 64,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'No tests run yet',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Tap the button above to run FFI tests',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ] else ...[
              Expanded(
                child: Card(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const CircularProgressIndicator(),
                        const SizedBox(height: 16),
                        Text(
                          'Running FFI Tests...',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'This may take a few seconds',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _checkServiceStatus,
        tooltip: 'Refresh Status',
        child: const Icon(Icons.refresh),
      ),
    );
  }

  String _formatTestName(String testName) {
    switch (testName) {
      case 'connectivity':
        return 'FFI Connectivity';
      case 'imageProcessing':
        return 'Image Processing';
      case 'collageCreation':
        return 'Collage Creation';
      case 'filterSystem':
        return 'Filter System';
      default:
        return testName;
    }
  }
}
