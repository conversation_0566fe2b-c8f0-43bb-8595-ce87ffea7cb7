import 'package:flutter/material.dart';

/// Theme mode options
enum AppThemeMode {
  light,
  dark,
  system,
}

/// Language options
enum AppLanguage {
  english,
  chinese,
  japanese,
  korean,
}

/// Image quality options
enum ImageQuality {
  low,
  medium,
  high,
  ultra,
}

/// Processing speed options
enum ProcessingSpeed {
  fast,
  balanced,
  quality,
}

/// Memory usage options
enum MemoryUsage {
  low,
  balanced,
  high,
}

/// App settings model
class AppSettings {
  final AppThemeMode themeMode;
  final AppLanguage language;
  final ImageQuality imageQuality;
  final ProcessingSpeed processingSpeed;
  final MemoryUsage memoryUsage;
  final bool enableHapticFeedback;
  final bool enableSoundEffects;
  final bool enableAutoSave;
  final bool enableCloudSync;
  final bool enableAnalytics;
  final double maxCacheSize; // in MB
  final int maxRecentItems;

  const AppSettings({
    this.themeMode = AppThemeMode.system,
    this.language = AppLanguage.english,
    this.imageQuality = ImageQuality.high,
    this.processingSpeed = ProcessingSpeed.balanced,
    this.memoryUsage = MemoryUsage.balanced,
    this.enableHapticFeedback = true,
    this.enableSoundEffects = true,
    this.enableAutoSave = true,
    this.enableCloudSync = false,
    this.enableAnalytics = true,
    this.maxCacheSize = 500.0,
    this.maxRecentItems = 50,
  });

  /// Create a copy with updated properties
  AppSettings copyWith({
    AppThemeMode? themeMode,
    AppLanguage? language,
    ImageQuality? imageQuality,
    ProcessingSpeed? processingSpeed,
    MemoryUsage? memoryUsage,
    bool? enableHapticFeedback,
    bool? enableSoundEffects,
    bool? enableAutoSave,
    bool? enableCloudSync,
    bool? enableAnalytics,
    double? maxCacheSize,
    int? maxRecentItems,
  }) {
    return AppSettings(
      themeMode: themeMode ?? this.themeMode,
      language: language ?? this.language,
      imageQuality: imageQuality ?? this.imageQuality,
      processingSpeed: processingSpeed ?? this.processingSpeed,
      memoryUsage: memoryUsage ?? this.memoryUsage,
      enableHapticFeedback: enableHapticFeedback ?? this.enableHapticFeedback,
      enableSoundEffects: enableSoundEffects ?? this.enableSoundEffects,
      enableAutoSave: enableAutoSave ?? this.enableAutoSave,
      enableCloudSync: enableCloudSync ?? this.enableCloudSync,
      enableAnalytics: enableAnalytics ?? this.enableAnalytics,
      maxCacheSize: maxCacheSize ?? this.maxCacheSize,
      maxRecentItems: maxRecentItems ?? this.maxRecentItems,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'themeMode': themeMode.name,
      'language': language.name,
      'imageQuality': imageQuality.name,
      'processingSpeed': processingSpeed.name,
      'memoryUsage': memoryUsage.name,
      'enableHapticFeedback': enableHapticFeedback,
      'enableSoundEffects': enableSoundEffects,
      'enableAutoSave': enableAutoSave,
      'enableCloudSync': enableCloudSync,
      'enableAnalytics': enableAnalytics,
      'maxCacheSize': maxCacheSize,
      'maxRecentItems': maxRecentItems,
    };
  }

  /// Create from JSON
  factory AppSettings.fromJson(Map<String, dynamic> json) {
    return AppSettings(
      themeMode: AppThemeMode.values.firstWhere(
        (e) => e.name == json['themeMode'],
        orElse: () => AppThemeMode.system,
      ),
      language: AppLanguage.values.firstWhere(
        (e) => e.name == json['language'],
        orElse: () => AppLanguage.english,
      ),
      imageQuality: ImageQuality.values.firstWhere(
        (e) => e.name == json['imageQuality'],
        orElse: () => ImageQuality.high,
      ),
      processingSpeed: ProcessingSpeed.values.firstWhere(
        (e) => e.name == json['processingSpeed'],
        orElse: () => ProcessingSpeed.balanced,
      ),
      memoryUsage: MemoryUsage.values.firstWhere(
        (e) => e.name == json['memoryUsage'],
        orElse: () => MemoryUsage.balanced,
      ),
      enableHapticFeedback: json['enableHapticFeedback'] ?? true,
      enableSoundEffects: json['enableSoundEffects'] ?? true,
      enableAutoSave: json['enableAutoSave'] ?? true,
      enableCloudSync: json['enableCloudSync'] ?? false,
      enableAnalytics: json['enableAnalytics'] ?? true,
      maxCacheSize: (json['maxCacheSize'] ?? 500.0).toDouble(),
      maxRecentItems: json['maxRecentItems'] ?? 50,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppSettings &&
        other.themeMode == themeMode &&
        other.language == language &&
        other.imageQuality == imageQuality &&
        other.processingSpeed == processingSpeed &&
        other.memoryUsage == memoryUsage &&
        other.enableHapticFeedback == enableHapticFeedback &&
        other.enableSoundEffects == enableSoundEffects &&
        other.enableAutoSave == enableAutoSave &&
        other.enableCloudSync == enableCloudSync &&
        other.enableAnalytics == enableAnalytics &&
        other.maxCacheSize == maxCacheSize &&
        other.maxRecentItems == maxRecentItems;
  }

  @override
  int get hashCode {
    return Object.hash(
      themeMode,
      language,
      imageQuality,
      processingSpeed,
      memoryUsage,
      enableHapticFeedback,
      enableSoundEffects,
      enableAutoSave,
      enableCloudSync,
      enableAnalytics,
      maxCacheSize,
      maxRecentItems,
    );
  }
}

/// Extension methods for enum display names
extension AppThemeModeExtension on AppThemeMode {
  String get displayName {
    switch (this) {
      case AppThemeMode.light:
        return 'Light';
      case AppThemeMode.dark:
        return 'Dark';
      case AppThemeMode.system:
        return 'System';
    }
  }

  IconData get icon {
    switch (this) {
      case AppThemeMode.light:
        return Icons.light_mode;
      case AppThemeMode.dark:
        return Icons.dark_mode;
      case AppThemeMode.system:
        return Icons.brightness_auto;
    }
  }
}

extension AppLanguageExtension on AppLanguage {
  String get displayName {
    switch (this) {
      case AppLanguage.english:
        return 'English';
      case AppLanguage.chinese:
        return '中文';
      case AppLanguage.japanese:
        return '日本語';
      case AppLanguage.korean:
        return '한국어';
    }
  }

  String get code {
    switch (this) {
      case AppLanguage.english:
        return 'en';
      case AppLanguage.chinese:
        return 'zh';
      case AppLanguage.japanese:
        return 'ja';
      case AppLanguage.korean:
        return 'ko';
    }
  }
}

extension ImageQualityExtension on ImageQuality {
  String get displayName {
    switch (this) {
      case ImageQuality.low:
        return 'Low';
      case ImageQuality.medium:
        return 'Medium';
      case ImageQuality.high:
        return 'High';
      case ImageQuality.ultra:
        return 'Ultra';
    }
  }

  String get description {
    switch (this) {
      case ImageQuality.low:
        return 'Faster processing, smaller files';
      case ImageQuality.medium:
        return 'Balanced quality and speed';
      case ImageQuality.high:
        return 'Better quality, larger files';
      case ImageQuality.ultra:
        return 'Best quality, largest files';
    }
  }
}

extension ProcessingSpeedExtension on ProcessingSpeed {
  String get displayName {
    switch (this) {
      case ProcessingSpeed.fast:
        return 'Fast';
      case ProcessingSpeed.balanced:
        return 'Balanced';
      case ProcessingSpeed.quality:
        return 'Quality';
    }
  }

  String get description {
    switch (this) {
      case ProcessingSpeed.fast:
        return 'Prioritize speed over quality';
      case ProcessingSpeed.balanced:
        return 'Balance between speed and quality';
      case ProcessingSpeed.quality:
        return 'Prioritize quality over speed';
    }
  }
}

extension MemoryUsageExtension on MemoryUsage {
  String get displayName {
    switch (this) {
      case MemoryUsage.low:
        return 'Low';
      case MemoryUsage.balanced:
        return 'Balanced';
      case MemoryUsage.high:
        return 'High';
    }
  }

  String get description {
    switch (this) {
      case MemoryUsage.low:
        return 'Use less memory, may be slower';
      case MemoryUsage.balanced:
        return 'Balance memory usage and performance';
      case MemoryUsage.high:
        return 'Use more memory for better performance';
    }
  }
}
