import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/settings_models.dart';

/// Settings controller for managing app settings
class SettingsController extends ChangeNotifier {
  static const String _settingsKey = 'app_settings';
  
  AppSettings _settings = const AppSettings();
  bool _isLoading = false;
  String? _error;

  // Getters
  AppSettings get settings => _settings;
  bool get isLoading => _isLoading;
  String? get error => _error;

  /// Initialize settings controller
  Future<void> initialize() async {
    await loadSettings();
  }

  /// Load settings from storage
  Future<void> loadSettings() async {
    _setLoading(true);
    _clearError();

    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_settingsKey);
      
      if (settingsJson != null) {
        final settingsMap = jsonDecode(settingsJson) as Map<String, dynamic>;
        _settings = AppSettings.fromJson(settingsMap);
      }
    } catch (e) {
      _setError('Failed to load settings: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Save settings to storage
  Future<void> saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = jsonEncode(_settings.toJson());
      await prefs.setString(_settingsKey, settingsJson);
    } catch (e) {
      _setError('Failed to save settings: $e');
    }
  }

  /// Update theme mode
  Future<void> updateThemeMode(AppThemeMode themeMode) async {
    _settings = _settings.copyWith(themeMode: themeMode);
    notifyListeners();
    await saveSettings();
  }

  /// Update language
  Future<void> updateLanguage(AppLanguage language) async {
    _settings = _settings.copyWith(language: language);
    notifyListeners();
    await saveSettings();
  }

  /// Update image quality
  Future<void> updateImageQuality(ImageQuality quality) async {
    _settings = _settings.copyWith(imageQuality: quality);
    notifyListeners();
    await saveSettings();
  }

  /// Update processing speed
  Future<void> updateProcessingSpeed(ProcessingSpeed speed) async {
    _settings = _settings.copyWith(processingSpeed: speed);
    notifyListeners();
    await saveSettings();
  }

  /// Update memory usage
  Future<void> updateMemoryUsage(MemoryUsage usage) async {
    _settings = _settings.copyWith(memoryUsage: usage);
    notifyListeners();
    await saveSettings();
  }

  /// Update haptic feedback setting
  Future<void> updateHapticFeedback(bool enabled) async {
    _settings = _settings.copyWith(enableHapticFeedback: enabled);
    notifyListeners();
    await saveSettings();
  }

  /// Update sound effects setting
  Future<void> updateSoundEffects(bool enabled) async {
    _settings = _settings.copyWith(enableSoundEffects: enabled);
    notifyListeners();
    await saveSettings();
  }

  /// Update auto save setting
  Future<void> updateAutoSave(bool enabled) async {
    _settings = _settings.copyWith(enableAutoSave: enabled);
    notifyListeners();
    await saveSettings();
  }

  /// Update cloud sync setting
  Future<void> updateCloudSync(bool enabled) async {
    _settings = _settings.copyWith(enableCloudSync: enabled);
    notifyListeners();
    await saveSettings();
  }

  /// Update analytics setting
  Future<void> updateAnalytics(bool enabled) async {
    _settings = _settings.copyWith(enableAnalytics: enabled);
    notifyListeners();
    await saveSettings();
  }

  /// Update max cache size
  Future<void> updateMaxCacheSize(double size) async {
    _settings = _settings.copyWith(maxCacheSize: size);
    notifyListeners();
    await saveSettings();
  }

  /// Update max recent items
  Future<void> updateMaxRecentItems(int count) async {
    _settings = _settings.copyWith(maxRecentItems: count);
    notifyListeners();
    await saveSettings();
  }

  /// Reset settings to defaults
  Future<void> resetToDefaults() async {
    _settings = const AppSettings();
    notifyListeners();
    await saveSettings();
  }

  /// Clear cache
  Future<void> clearCache() async {
    try {
      // TODO: Implement cache clearing logic
      // This would involve clearing image cache, temporary files, etc.
    } catch (e) {
      _setError('Failed to clear cache: $e');
    }
  }

  /// Export settings
  Map<String, dynamic> exportSettings() {
    return _settings.toJson();
  }

  /// Import settings
  Future<void> importSettings(Map<String, dynamic> settingsMap) async {
    try {
      _settings = AppSettings.fromJson(settingsMap);
      notifyListeners();
      await saveSettings();
    } catch (e) {
      _setError('Failed to import settings: $e');
    }
  }

  /// Get cache size (mock implementation)
  Future<double> getCacheSize() async {
    // TODO: Implement actual cache size calculation
    return 125.5; // Mock value in MB
  }

  /// Get app version
  String getAppVersion() {
    return '1.0.0'; // TODO: Get from package info
  }

  /// Get build number
  String getBuildNumber() {
    return '1'; // TODO: Get from package info
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Set error state
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// Clear error state
  void _clearError() {
    _error = null;
  }
}
