// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Japanese (`ja`).
class AppLocalizationsJa extends AppLocalizations {
  AppLocalizationsJa([String locale = 'ja']) : super(locale);

  @override
  String get appTitle => 'TuShen';

  @override
  String get welcome => 'TuShenへようこそ';

  @override
  String get welcomeSubtitle => 'プロフェッショナルツールで美しいコラージュを作成し、写真を編集しましょう';

  @override
  String get createCollage => 'コラージュ作成';

  @override
  String get createCollageSubtitle => '複数の写真を組み合わせる';

  @override
  String get editPhoto => '写真編集';

  @override
  String get editPhotoSubtitle => 'フィルターとエフェクトを適用';

  @override
  String get gallery => 'ギャラリー';

  @override
  String get settings => '設定';

  @override
  String get home => 'ホーム';

  @override
  String get quickActions => 'クイックアクション';

  @override
  String get recentWork => '最近の作品';

  @override
  String get viewAll => 'すべて表示';

  @override
  String get noRecentImages => '最近の画像がありません';

  @override
  String get selectImage => '画像を選択';

  @override
  String get selectImageDescription => '画像を追加する方法を選択してください';

  @override
  String get takePhoto => '写真を撮る';

  @override
  String get takePhotoDescription => 'カメラで新しい写真を撮影';

  @override
  String get chooseFromGallery => 'ギャラリーから選択';

  @override
  String get chooseFromGalleryDescription => 'ギャラリーから既存の写真を選択';

  @override
  String get selectMultiple => '複数選択';

  @override
  String get selectMultipleDescription => 'コラージュ用に複数の写真を選択';

  @override
  String get cancel => 'キャンセル';

  @override
  String get loading => '読み込み中...';

  @override
  String get error => 'エラー';

  @override
  String get permissionDenied => '権限が拒否されました';

  @override
  String get cameraPermissionDenied => 'カメラの権限が拒否されました';

  @override
  String get photoLibraryPermissionDenied => 'フォトライブラリの権限が拒否されました';

  @override
  String get failedToLoadImages => '画像の読み込みに失敗しました';

  @override
  String get failedToPickImage => '画像の選択に失敗しました';

  @override
  String get features => '機能';

  @override
  String get professionalTools => 'プロフェッショナルツール';

  @override
  String get professionalToolsDescription => 'プロ級の画像処理ツールを使用';

  @override
  String get highPerformance => '高性能処理';

  @override
  String get highPerformanceDescription => 'Rustベースの高速画像処理エンジン';

  @override
  String get crossPlatform => 'クロスプラットフォーム';

  @override
  String get crossPlatformDescription => 'iOS、Android、macOS、Windowsをサポート';
}
