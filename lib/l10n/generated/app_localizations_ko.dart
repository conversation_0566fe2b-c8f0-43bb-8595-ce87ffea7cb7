// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Korean (`ko`).
class AppLocalizationsKo extends AppLocalizations {
  AppLocalizationsKo([String locale = 'ko']) : super(locale);

  @override
  String get appTitle => 'TuShen';

  @override
  String get welcome => 'TuShen에 오신 것을 환영합니다';

  @override
  String get welcomeSubtitle => '전문 도구로 멋진 콜라주를 만들고 사진을 편집하세요';

  @override
  String get createCollage => '콜라주 만들기';

  @override
  String get createCollageSubtitle => '여러 사진 결합';

  @override
  String get editPhoto => '사진 편집';

  @override
  String get editPhotoSubtitle => '필터 및 효과 적용';

  @override
  String get gallery => '갤러리';

  @override
  String get settings => '설정';

  @override
  String get home => '홈';

  @override
  String get quickActions => '빠른 작업';

  @override
  String get recentWork => '최근 작업';

  @override
  String get viewAll => '모두 보기';

  @override
  String get noRecentImages => '최근 이미지가 없습니다';

  @override
  String get selectImage => '이미지 선택';

  @override
  String get selectImageDescription => '이미지를 추가할 방법을 선택하세요';

  @override
  String get takePhoto => '사진 촬영';

  @override
  String get takePhotoDescription => '카메라로 새 사진 촬영';

  @override
  String get chooseFromGallery => '갤러리에서 선택';

  @override
  String get chooseFromGalleryDescription => '갤러리에서 기존 사진 선택';

  @override
  String get selectMultiple => '여러 개 선택';

  @override
  String get selectMultipleDescription => '콜라주용 여러 사진 선택';

  @override
  String get cancel => '취소';

  @override
  String get loading => '로딩 중...';

  @override
  String get error => '오류';

  @override
  String get permissionDenied => '권한이 거부되었습니다';

  @override
  String get cameraPermissionDenied => '카메라 권한이 거부되었습니다';

  @override
  String get photoLibraryPermissionDenied => '사진 라이브러리 권한이 거부되었습니다';

  @override
  String get failedToLoadImages => '이미지 로드에 실패했습니다';

  @override
  String get failedToPickImage => '이미지 선택에 실패했습니다';

  @override
  String get features => '기능';

  @override
  String get professionalTools => '전문 도구';

  @override
  String get professionalToolsDescription => '전문가급 이미지 처리 도구 사용';

  @override
  String get highPerformance => '고성능 처리';

  @override
  String get highPerformanceDescription => 'Rust 기반 고속 이미지 처리 엔진';

  @override
  String get crossPlatform => '크로스 플랫폼';

  @override
  String get crossPlatformDescription => 'iOS, Android, macOS, Windows 지원';
}
