// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get appTitle => '图神';

  @override
  String get welcome => '欢迎使用图神';

  @override
  String get welcomeSubtitle => '使用专业工具创建精美拼贴和编辑照片';

  @override
  String get createCollage => '创建拼贴';

  @override
  String get createCollageSubtitle => '合并多张照片';

  @override
  String get editPhoto => '编辑照片';

  @override
  String get editPhotoSubtitle => '应用滤镜和特效';

  @override
  String get gallery => '图库';

  @override
  String get settings => '设置';

  @override
  String get home => '首页';

  @override
  String get quickActions => '快速操作';

  @override
  String get recentWork => '最近作品';

  @override
  String get viewAll => '查看全部';

  @override
  String get noRecentImages => '暂无最近图片';

  @override
  String get selectImage => '选择图片';

  @override
  String get selectImageDescription => '选择添加图片的方式';

  @override
  String get takePhoto => '拍照';

  @override
  String get takePhotoDescription => '使用相机拍摄新照片';

  @override
  String get chooseFromGallery => '从相册选择';

  @override
  String get chooseFromGalleryDescription => '从相册中选择现有照片';

  @override
  String get selectMultiple => '选择多张';

  @override
  String get selectMultipleDescription => '选择多张照片制作拼贴';

  @override
  String get cancel => '取消';

  @override
  String get loading => '加载中...';

  @override
  String get error => '错误';

  @override
  String get permissionDenied => '权限被拒绝';

  @override
  String get cameraPermissionDenied => '相机权限被拒绝';

  @override
  String get photoLibraryPermissionDenied => '照片库权限被拒绝';

  @override
  String get failedToLoadImages => '加载图片失败';

  @override
  String get failedToPickImage => '选择图片失败';

  @override
  String get features => '功能特色';

  @override
  String get professionalTools => '专业工具';

  @override
  String get professionalToolsDescription => '使用专业级图像处理工具';

  @override
  String get highPerformance => '高性能处理';

  @override
  String get highPerformanceDescription => '基于Rust的高速图像处理引擎';

  @override
  String get crossPlatform => '跨平台支持';

  @override
  String get crossPlatformDescription => '支持iOS、Android、macOS、Windows';
}

/// The translations for Chinese, as used in Taiwan (`zh_TW`).
class AppLocalizationsZhTw extends AppLocalizationsZh {
  AppLocalizationsZhTw() : super('zh_TW');

  @override
  String get appTitle => '圖神';

  @override
  String get welcome => '歡迎使用圖神';

  @override
  String get welcomeSubtitle => '使用專業工具創建精美拼貼和編輯照片';

  @override
  String get createCollage => '創建拼貼';

  @override
  String get createCollageSubtitle => '合併多張照片';

  @override
  String get editPhoto => '編輯照片';

  @override
  String get editPhotoSubtitle => '應用濾鏡和特效';

  @override
  String get gallery => '圖庫';

  @override
  String get settings => '設定';

  @override
  String get home => '首頁';

  @override
  String get quickActions => '快速操作';

  @override
  String get recentWork => '最近作品';

  @override
  String get viewAll => '查看全部';

  @override
  String get noRecentImages => '暫無最近圖片';

  @override
  String get selectImage => '選擇圖片';

  @override
  String get selectImageDescription => '選擇添加圖片的方式';

  @override
  String get takePhoto => '拍照';

  @override
  String get takePhotoDescription => '使用相機拍攝新照片';

  @override
  String get chooseFromGallery => '從相簿選擇';

  @override
  String get chooseFromGalleryDescription => '從相簿中選擇現有照片';

  @override
  String get selectMultiple => '選擇多張';

  @override
  String get selectMultipleDescription => '選擇多張照片製作拼貼';

  @override
  String get cancel => '取消';

  @override
  String get loading => '載入中...';

  @override
  String get error => '錯誤';

  @override
  String get permissionDenied => '權限被拒絕';

  @override
  String get cameraPermissionDenied => '相機權限被拒絕';

  @override
  String get photoLibraryPermissionDenied => '照片庫權限被拒絕';

  @override
  String get failedToLoadImages => '載入圖片失敗';

  @override
  String get failedToPickImage => '選擇圖片失敗';

  @override
  String get features => '功能特色';

  @override
  String get professionalTools => '專業工具';

  @override
  String get professionalToolsDescription => '使用專業級圖像處理工具';

  @override
  String get highPerformance => '高性能處理';

  @override
  String get highPerformanceDescription => '基於Rust的高速圖像處理引擎';

  @override
  String get crossPlatform => '跨平台支援';

  @override
  String get crossPlatformDescription => '支援iOS、Android、macOS、Windows';
}
