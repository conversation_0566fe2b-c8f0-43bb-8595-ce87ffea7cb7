import 'dart:ffi';
import 'dart:io';
import 'dart:typed_data';
import 'package:ffi/ffi.dart';
import 'generated/bridge.dart';

/// FFI service for communicating with Rust backend
class RustBridgeService {
  static RustBridgeService? _instance;
  static RustBridgeService get instance => _instance ??= RustBridgeService._();

  RustBridgeService._();

  late final DynamicLibrary _lib;
  bool _isInitialized = false;

  /// Initialize the Rust library
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _lib = _loadLibrary();
      _isInitialized = true;
    } catch (e) {
      throw Exception('Failed to initialize Rust bridge: $e');
    }
  }

  /// Load the appropriate dynamic library for the current platform
  DynamicLibrary _loadLibrary() {
    if (Platform.isAndroid) {
      return DynamicLibrary.open('libtushen_core.so');
    } else if (Platform.isIOS) {
      return DynamicLibrary.process();
    } else if (Platform.isMacOS) {
      // Try different paths for macOS
      final possiblePaths = [
        'libtushen_core.dylib',
        './libtushen_core.dylib',
        '../MacOS/libtushen_core.dylib',
        '../../MacOS/libtushen_core.dylib',
      ];

      for (final path in possiblePaths) {
        try {
          return DynamicLibrary.open(path);
        } catch (e) {
          // Continue to next path
          continue;
        }
      }

      // If all paths fail, try the default
      return DynamicLibrary.open('libtushen_core.dylib');
    } else if (Platform.isWindows) {
      return DynamicLibrary.open('tushen_core.dll');
    } else if (Platform.isLinux) {
      return DynamicLibrary.open('libtushen_core.so');
    } else {
      throw UnsupportedError(
        'Unsupported platform: ${Platform.operatingSystem}',
      );
    }
  }

  /// Check if the service is initialized
  bool get isInitialized => _isInitialized;

  /// Get the dynamic library instance
  DynamicLibrary get library {
    if (!_isInitialized) {
      throw StateError(
        'RustBridgeService not initialized. Call initialize() first.',
      );
    }
    return _lib;
  }

  /// Generate analysis visualization images (ELA, etc.)
  Future<Map<String, dynamic>> authenticateImage(
    ImageDataBridge imageData,
    Map<String, dynamic> config,
  ) async {
    // Simulate processing time for generating analysis images
    await Future.delayed(const Duration(seconds: 2));

    // Generate ELA (Error Level Analysis) visualization
    final elaImageData = await _generateELAVisualization(imageData);

    return {
      'processing_time_ms': 2000,
      'analysis_images': [
        {
          'analysis_type': 'ela',
          'image_data': elaImageData,
          'description': 'Error Level Analysis - 显示压缩差异的染色图',
        },
      ],
    };
  }

  /// Generate ELA (Error Level Analysis) visualization
  Future<List<int>> _generateELAVisualization(ImageDataBridge imageData) async {
    // For now, generate a mock ELA-style visualization
    // This will be replaced with actual Rust FFI call to generate real ELA

    // Create a simple colored overlay to simulate ELA effect
    // In real implementation, this would call Rust ELA algorithm
    final mockELAData = List<int>.from(imageData.data);

    // Apply a blue tint to simulate ELA coloring
    for (int i = 0; i < mockELAData.length; i += 3) {
      if (i + 2 < mockELAData.length) {
        // Enhance blue channel, reduce red/green for ELA effect
        mockELAData[i] = (mockELAData[i] * 0.3).round(); // Red
        mockELAData[i + 1] = (mockELAData[i + 1] * 0.5).round(); // Green
        mockELAData[i + 2] = (mockELAData[i + 2] * 1.5)
            .clamp(0, 255)
            .round(); // Blue
      }
    }

    return mockELAData;
  }
}

/// Data structures for FFI communication



/// Filter types supported by the Rust backend
enum FilterType {
  // Basic filters
  blur,
  sharpen,
  emboss,
  edgeDetect,

  // Adjustment filters
  brightness,
  contrast,
  saturation,
  hue,
  gamma,
  exposure,
  highlights,
  shadows,

  // Artistic filters
  oilPainting,
  watercolor,
  pencilSketch,
  cartoon,
  vintage,
  sepia,
  blackAndWhite,

  // Portrait filters
  skinSmooth,
  eyeBrighten,
  teethWhiten,

  // Landscape filters
  skyEnhance,
  foliageBoost,

  // Architecture filters
  structureEnhance,
  perspectiveCorrect,

  // Food filters
  foodVibrant,
  warmTone,

  // Custom filter
  custom,
}

/// Image formats supported by the system
enum ImageFormat { jpeg, png, webp, bmp, gif, tiff, heic, avif, ico, raw }

/// Color spaces supported by the system
enum ColorSpace { srgb, displayP3, rec2020, adobeRgb }

/// Extension methods for enum conversions
extension FilterTypeExtension on FilterType {
  String get rustName {
    switch (this) {
      case FilterType.blur:
        return 'Blur';
      case FilterType.sharpen:
        return 'Sharpen';
      case FilterType.emboss:
        return 'Emboss';
      case FilterType.edgeDetect:
        return 'EdgeDetect';
      case FilterType.brightness:
        return 'Brightness';
      case FilterType.contrast:
        return 'Contrast';
      case FilterType.saturation:
        return 'Saturation';
      case FilterType.hue:
        return 'Hue';
      case FilterType.gamma:
        return 'Gamma';
      case FilterType.exposure:
        return 'Exposure';
      case FilterType.highlights:
        return 'Highlights';
      case FilterType.shadows:
        return 'Shadows';
      case FilterType.oilPainting:
        return 'OilPainting';
      case FilterType.watercolor:
        return 'Watercolor';
      case FilterType.pencilSketch:
        return 'PencilSketch';
      case FilterType.cartoon:
        return 'Cartoon';
      case FilterType.vintage:
        return 'Vintage';
      case FilterType.sepia:
        return 'Sepia';
      case FilterType.blackAndWhite:
        return 'BlackAndWhite';
      case FilterType.skinSmooth:
        return 'SkinSmooth';
      case FilterType.eyeBrighten:
        return 'EyeBrighten';
      case FilterType.teethWhiten:
        return 'TeethWhiten';
      case FilterType.skyEnhance:
        return 'SkyEnhance';
      case FilterType.foliageBoost:
        return 'FoliageBoost';
      case FilterType.structureEnhance:
        return 'StructureEnhance';
      case FilterType.perspectiveCorrect:
        return 'PerspectiveCorrect';
      case FilterType.foodVibrant:
        return 'FoodVibrant';
      case FilterType.warmTone:
        return 'WarmTone';
      case FilterType.custom:
        return 'Custom';
    }
  }
}

extension ImageFormatExtension on ImageFormat {
  String get rustName {
    switch (this) {
      case ImageFormat.jpeg:
        return 'Jpeg';
      case ImageFormat.png:
        return 'Png';
      case ImageFormat.webp:
        return 'Webp';
      case ImageFormat.bmp:
        return 'Bmp';
      case ImageFormat.gif:
        return 'Gif';
      case ImageFormat.tiff:
        return 'Tiff';
      case ImageFormat.heic:
        return 'Heic';
      case ImageFormat.avif:
        return 'Avif';
      case ImageFormat.ico:
        return 'Ico';
      case ImageFormat.raw:
        return 'Raw';
    }
  }
}

extension ColorSpaceExtension on ColorSpace {
  String get rustName {
    switch (this) {
      case ColorSpace.srgb:
        return 'Srgb';
      case ColorSpace.displayP3:
        return 'DisplayP3';
      case ColorSpace.rec2020:
        return 'Rec2020';
      case ColorSpace.adobeRgb:
        return 'AdobeRgb';
    }
  }
}
