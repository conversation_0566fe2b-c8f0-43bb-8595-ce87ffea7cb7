// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.9.0.

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../frb_generated.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';

/// Configuration for image authentication algorithms
class AuthenticationConfig {
  /// ELA compression quality for analysis (1-100)
  final int elaQuality;

  /// Threshold for copy-move detection
  final double copyMoveThreshold;

  /// Block size for analysis (8, 16, 32)
  final int blockSize;

  /// Noise analysis sensitivity (0.0-1.0)
  final double noiseSensitivity;

  /// Enable metadata analysis
  final bool analyzeMetadata;

  const AuthenticationConfig({
    required this.elaQuality,
    required this.copyMoveThreshold,
    required this.blockSize,
    required this.noiseSensitivity,
    required this.analyzeMetadata,
  });

  @override
  int get hashCode =>
      elaQuality.hashCode ^
      copyMoveThreshold.hashCode ^
      blockSize.hashCode ^
      noiseSensitivity.hashCode ^
      analyzeMetadata.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AuthenticationConfig &&
          runtimeType == other.runtimeType &&
          elaQuality == other.elaQuality &&
          copyMoveThreshold == other.copyMoveThreshold &&
          blockSize == other.blockSize &&
          noiseSensitivity == other.noiseSensitivity &&
          analyzeMetadata == other.analyzeMetadata;
}

/// Image authentication engine for detecting tampering and manipulation
class ImageAuthenticator {
  /// Configuration for authentication algorithms
  final AuthenticationConfig config;

  const ImageAuthenticator({required this.config});

  @override
  int get hashCode => config.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ImageAuthenticator && runtimeType == other.runtimeType && config == other.config;
}
