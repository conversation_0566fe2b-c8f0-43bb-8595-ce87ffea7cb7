// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.9.0.

// ignore_for_file: unused_import, unused_element, unnecessary_import, duplicate_ignore, invalid_use_of_internal_member, annotate_overrides, non_constant_identifier_names, curly_braces_in_flow_control_structures, prefer_const_literals_to_create_immutables, unused_field

// Static analysis wrongly picks the IO variant, thus ignore this
// ignore_for_file: argument_type_not_assignable

import 'bridge.dart';
import 'core/image_authenticator.dart';
import 'dart:async';
import 'dart:convert';
import 'frb_generated.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated_web.dart';

abstract class TushenCoreApiImplPlatform extends BaseApiImpl<TushenCoreWire> {
  TushenCoreApiImplPlatform({
    required super.handler,
    required super.wire,
    required super.generalizedFrbRustBinding,
    required super.portManager,
  });

  CrossPlatformFinalizerArg get rust_arc_decrement_strong_count_FilterEngineBridgePtr => wire
      .rust_arc_decrement_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFilterEngineBridge;

  @protected
  FilterEngineBridge
  dco_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFilterEngineBridge(dynamic raw);

  @protected
  FilterEngineBridge
  dco_decode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFilterEngineBridge(dynamic raw);

  @protected
  FilterEngineBridge dco_decode_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFilterEngineBridge(
    dynamic raw,
  );

  @protected
  String dco_decode_String(dynamic raw);

  @protected
  AnalysisImageBridge dco_decode_analysis_image_bridge(dynamic raw);

  @protected
  AnalysisResultBridge dco_decode_analysis_result_bridge(dynamic raw);

  @protected
  AuthenticationConfig dco_decode_authentication_config(dynamic raw);

  @protected
  AuthenticationConfigBridge dco_decode_authentication_config_bridge(dynamic raw);

  @protected
  AuthenticationResultBridge dco_decode_authentication_result_bridge(dynamic raw);

  @protected
  bool dco_decode_bool(dynamic raw);

  @protected
  AuthenticationConfigBridge dco_decode_box_autoadd_authentication_config_bridge(dynamic raw);

  @protected
  CollageEngineBridge dco_decode_box_autoadd_collage_engine_bridge(dynamic raw);

  @protected
  ImageAuthenticatorBridge dco_decode_box_autoadd_image_authenticator_bridge(dynamic raw);

  @protected
  ImageCompressorBridge dco_decode_box_autoadd_image_compressor_bridge(dynamic raw);

  @protected
  ImageDataBridge dco_decode_box_autoadd_image_data_bridge(dynamic raw);

  @protected
  ImageProcessorBridge dco_decode_box_autoadd_image_processor_bridge(dynamic raw);

  @protected
  int dco_decode_box_autoadd_u_32(dynamic raw);

  @protected
  int dco_decode_box_autoadd_u_8(dynamic raw);

  @protected
  CollageEngineBridge dco_decode_collage_engine_bridge(dynamic raw);

  @protected
  CompressionEstimateBridge dco_decode_compression_estimate_bridge(dynamic raw);

  @protected
  CompressionResultBridge dco_decode_compression_result_bridge(dynamic raw);

  @protected
  double dco_decode_f_32(dynamic raw);

  @protected
  ImageAuthenticator dco_decode_image_authenticator(dynamic raw);

  @protected
  ImageAuthenticatorBridge dco_decode_image_authenticator_bridge(dynamic raw);

  @protected
  ImageCompressorBridge dco_decode_image_compressor_bridge(dynamic raw);

  @protected
  ImageDataBridge dco_decode_image_data_bridge(dynamic raw);

  @protected
  ImageProcessorBridge dco_decode_image_processor_bridge(dynamic raw);

  @protected
  List<String> dco_decode_list_String(dynamic raw);

  @protected
  List<AnalysisImageBridge> dco_decode_list_analysis_image_bridge(dynamic raw);

  @protected
  List<AnalysisResultBridge> dco_decode_list_analysis_result_bridge(dynamic raw);

  @protected
  List<ImageDataBridge> dco_decode_list_image_data_bridge(dynamic raw);

  @protected
  List<ManipulationRegionBridge> dco_decode_list_manipulation_region_bridge(dynamic raw);

  @protected
  List<MetadataPairBridge> dco_decode_list_metadata_pair_bridge(dynamic raw);

  @protected
  List<int> dco_decode_list_prim_u_8_loose(dynamic raw);

  @protected
  Uint8List dco_decode_list_prim_u_8_strict(dynamic raw);

  @protected
  List<RectBridge> dco_decode_list_rect_bridge(dynamic raw);

  @protected
  ManipulationRegionBridge dco_decode_manipulation_region_bridge(dynamic raw);

  @protected
  MetadataPairBridge dco_decode_metadata_pair_bridge(dynamic raw);

  @protected
  String? dco_decode_opt_String(dynamic raw);

  @protected
  int? dco_decode_opt_box_autoadd_u_32(dynamic raw);

  @protected
  int? dco_decode_opt_box_autoadd_u_8(dynamic raw);

  @protected
  RectBridge dco_decode_rect_bridge(dynamic raw);

  @protected
  int dco_decode_u_32(dynamic raw);

  @protected
  BigInt dco_decode_u_64(dynamic raw);

  @protected
  int dco_decode_u_8(dynamic raw);

  @protected
  void dco_decode_unit(dynamic raw);

  @protected
  BigInt dco_decode_usize(dynamic raw);

  @protected
  FilterEngineBridge
  sse_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFilterEngineBridge(
    SseDeserializer deserializer,
  );

  @protected
  FilterEngineBridge
  sse_decode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFilterEngineBridge(
    SseDeserializer deserializer,
  );

  @protected
  FilterEngineBridge sse_decode_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFilterEngineBridge(
    SseDeserializer deserializer,
  );

  @protected
  String sse_decode_String(SseDeserializer deserializer);

  @protected
  AnalysisImageBridge sse_decode_analysis_image_bridge(SseDeserializer deserializer);

  @protected
  AnalysisResultBridge sse_decode_analysis_result_bridge(SseDeserializer deserializer);

  @protected
  AuthenticationConfig sse_decode_authentication_config(SseDeserializer deserializer);

  @protected
  AuthenticationConfigBridge sse_decode_authentication_config_bridge(SseDeserializer deserializer);

  @protected
  AuthenticationResultBridge sse_decode_authentication_result_bridge(SseDeserializer deserializer);

  @protected
  bool sse_decode_bool(SseDeserializer deserializer);

  @protected
  AuthenticationConfigBridge sse_decode_box_autoadd_authentication_config_bridge(SseDeserializer deserializer);

  @protected
  CollageEngineBridge sse_decode_box_autoadd_collage_engine_bridge(SseDeserializer deserializer);

  @protected
  ImageAuthenticatorBridge sse_decode_box_autoadd_image_authenticator_bridge(SseDeserializer deserializer);

  @protected
  ImageCompressorBridge sse_decode_box_autoadd_image_compressor_bridge(SseDeserializer deserializer);

  @protected
  ImageDataBridge sse_decode_box_autoadd_image_data_bridge(SseDeserializer deserializer);

  @protected
  ImageProcessorBridge sse_decode_box_autoadd_image_processor_bridge(SseDeserializer deserializer);

  @protected
  int sse_decode_box_autoadd_u_32(SseDeserializer deserializer);

  @protected
  int sse_decode_box_autoadd_u_8(SseDeserializer deserializer);

  @protected
  CollageEngineBridge sse_decode_collage_engine_bridge(SseDeserializer deserializer);

  @protected
  CompressionEstimateBridge sse_decode_compression_estimate_bridge(SseDeserializer deserializer);

  @protected
  CompressionResultBridge sse_decode_compression_result_bridge(SseDeserializer deserializer);

  @protected
  double sse_decode_f_32(SseDeserializer deserializer);

  @protected
  ImageAuthenticator sse_decode_image_authenticator(SseDeserializer deserializer);

  @protected
  ImageAuthenticatorBridge sse_decode_image_authenticator_bridge(SseDeserializer deserializer);

  @protected
  ImageCompressorBridge sse_decode_image_compressor_bridge(SseDeserializer deserializer);

  @protected
  ImageDataBridge sse_decode_image_data_bridge(SseDeserializer deserializer);

  @protected
  ImageProcessorBridge sse_decode_image_processor_bridge(SseDeserializer deserializer);

  @protected
  List<String> sse_decode_list_String(SseDeserializer deserializer);

  @protected
  List<AnalysisImageBridge> sse_decode_list_analysis_image_bridge(SseDeserializer deserializer);

  @protected
  List<AnalysisResultBridge> sse_decode_list_analysis_result_bridge(SseDeserializer deserializer);

  @protected
  List<ImageDataBridge> sse_decode_list_image_data_bridge(SseDeserializer deserializer);

  @protected
  List<ManipulationRegionBridge> sse_decode_list_manipulation_region_bridge(SseDeserializer deserializer);

  @protected
  List<MetadataPairBridge> sse_decode_list_metadata_pair_bridge(SseDeserializer deserializer);

  @protected
  List<int> sse_decode_list_prim_u_8_loose(SseDeserializer deserializer);

  @protected
  Uint8List sse_decode_list_prim_u_8_strict(SseDeserializer deserializer);

  @protected
  List<RectBridge> sse_decode_list_rect_bridge(SseDeserializer deserializer);

  @protected
  ManipulationRegionBridge sse_decode_manipulation_region_bridge(SseDeserializer deserializer);

  @protected
  MetadataPairBridge sse_decode_metadata_pair_bridge(SseDeserializer deserializer);

  @protected
  String? sse_decode_opt_String(SseDeserializer deserializer);

  @protected
  int? sse_decode_opt_box_autoadd_u_32(SseDeserializer deserializer);

  @protected
  int? sse_decode_opt_box_autoadd_u_8(SseDeserializer deserializer);

  @protected
  RectBridge sse_decode_rect_bridge(SseDeserializer deserializer);

  @protected
  int sse_decode_u_32(SseDeserializer deserializer);

  @protected
  BigInt sse_decode_u_64(SseDeserializer deserializer);

  @protected
  int sse_decode_u_8(SseDeserializer deserializer);

  @protected
  void sse_decode_unit(SseDeserializer deserializer);

  @protected
  BigInt sse_decode_usize(SseDeserializer deserializer);

  @protected
  int sse_decode_i_32(SseDeserializer deserializer);

  @protected
  void sse_encode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFilterEngineBridge(
    FilterEngineBridge self,
    SseSerializer serializer,
  );

  @protected
  void sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFilterEngineBridge(
    FilterEngineBridge self,
    SseSerializer serializer,
  );

  @protected
  void sse_encode_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFilterEngineBridge(
    FilterEngineBridge self,
    SseSerializer serializer,
  );

  @protected
  void sse_encode_String(String self, SseSerializer serializer);

  @protected
  void sse_encode_analysis_image_bridge(AnalysisImageBridge self, SseSerializer serializer);

  @protected
  void sse_encode_analysis_result_bridge(AnalysisResultBridge self, SseSerializer serializer);

  @protected
  void sse_encode_authentication_config(AuthenticationConfig self, SseSerializer serializer);

  @protected
  void sse_encode_authentication_config_bridge(AuthenticationConfigBridge self, SseSerializer serializer);

  @protected
  void sse_encode_authentication_result_bridge(AuthenticationResultBridge self, SseSerializer serializer);

  @protected
  void sse_encode_bool(bool self, SseSerializer serializer);

  @protected
  void sse_encode_box_autoadd_authentication_config_bridge(AuthenticationConfigBridge self, SseSerializer serializer);

  @protected
  void sse_encode_box_autoadd_collage_engine_bridge(CollageEngineBridge self, SseSerializer serializer);

  @protected
  void sse_encode_box_autoadd_image_authenticator_bridge(ImageAuthenticatorBridge self, SseSerializer serializer);

  @protected
  void sse_encode_box_autoadd_image_compressor_bridge(ImageCompressorBridge self, SseSerializer serializer);

  @protected
  void sse_encode_box_autoadd_image_data_bridge(ImageDataBridge self, SseSerializer serializer);

  @protected
  void sse_encode_box_autoadd_image_processor_bridge(ImageProcessorBridge self, SseSerializer serializer);

  @protected
  void sse_encode_box_autoadd_u_32(int self, SseSerializer serializer);

  @protected
  void sse_encode_box_autoadd_u_8(int self, SseSerializer serializer);

  @protected
  void sse_encode_collage_engine_bridge(CollageEngineBridge self, SseSerializer serializer);

  @protected
  void sse_encode_compression_estimate_bridge(CompressionEstimateBridge self, SseSerializer serializer);

  @protected
  void sse_encode_compression_result_bridge(CompressionResultBridge self, SseSerializer serializer);

  @protected
  void sse_encode_f_32(double self, SseSerializer serializer);

  @protected
  void sse_encode_image_authenticator(ImageAuthenticator self, SseSerializer serializer);

  @protected
  void sse_encode_image_authenticator_bridge(ImageAuthenticatorBridge self, SseSerializer serializer);

  @protected
  void sse_encode_image_compressor_bridge(ImageCompressorBridge self, SseSerializer serializer);

  @protected
  void sse_encode_image_data_bridge(ImageDataBridge self, SseSerializer serializer);

  @protected
  void sse_encode_image_processor_bridge(ImageProcessorBridge self, SseSerializer serializer);

  @protected
  void sse_encode_list_String(List<String> self, SseSerializer serializer);

  @protected
  void sse_encode_list_analysis_image_bridge(List<AnalysisImageBridge> self, SseSerializer serializer);

  @protected
  void sse_encode_list_analysis_result_bridge(List<AnalysisResultBridge> self, SseSerializer serializer);

  @protected
  void sse_encode_list_image_data_bridge(List<ImageDataBridge> self, SseSerializer serializer);

  @protected
  void sse_encode_list_manipulation_region_bridge(List<ManipulationRegionBridge> self, SseSerializer serializer);

  @protected
  void sse_encode_list_metadata_pair_bridge(List<MetadataPairBridge> self, SseSerializer serializer);

  @protected
  void sse_encode_list_prim_u_8_loose(List<int> self, SseSerializer serializer);

  @protected
  void sse_encode_list_prim_u_8_strict(Uint8List self, SseSerializer serializer);

  @protected
  void sse_encode_list_rect_bridge(List<RectBridge> self, SseSerializer serializer);

  @protected
  void sse_encode_manipulation_region_bridge(ManipulationRegionBridge self, SseSerializer serializer);

  @protected
  void sse_encode_metadata_pair_bridge(MetadataPairBridge self, SseSerializer serializer);

  @protected
  void sse_encode_opt_String(String? self, SseSerializer serializer);

  @protected
  void sse_encode_opt_box_autoadd_u_32(int? self, SseSerializer serializer);

  @protected
  void sse_encode_opt_box_autoadd_u_8(int? self, SseSerializer serializer);

  @protected
  void sse_encode_rect_bridge(RectBridge self, SseSerializer serializer);

  @protected
  void sse_encode_u_32(int self, SseSerializer serializer);

  @protected
  void sse_encode_u_64(BigInt self, SseSerializer serializer);

  @protected
  void sse_encode_u_8(int self, SseSerializer serializer);

  @protected
  void sse_encode_unit(void self, SseSerializer serializer);

  @protected
  void sse_encode_usize(BigInt self, SseSerializer serializer);

  @protected
  void sse_encode_i_32(int self, SseSerializer serializer);
}

// Section: wire_class

class TushenCoreWire implements BaseWire {
  TushenCoreWire.fromExternalLibrary(ExternalLibrary lib);

  void rust_arc_increment_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFilterEngineBridge(
    int ptr,
  ) => wasmModule
      .rust_arc_increment_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFilterEngineBridge(
        ptr,
      );

  void rust_arc_decrement_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFilterEngineBridge(
    int ptr,
  ) => wasmModule
      .rust_arc_decrement_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFilterEngineBridge(
        ptr,
      );
}

@JS('wasm_bindgen')
external TushenCoreWasmModule get wasmModule;

@JS()
@anonymous
extension type TushenCoreWasmModule._(JSObject _) implements JSObject {
  external void
  rust_arc_increment_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFilterEngineBridge(
    int ptr,
  );

  external void
  rust_arc_decrement_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFilterEngineBridge(
    int ptr,
  );
}
