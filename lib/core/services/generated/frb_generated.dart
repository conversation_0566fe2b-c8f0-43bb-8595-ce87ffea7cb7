// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.9.0.

// ignore_for_file: unused_import, unused_element, unnecessary_import, duplicate_ignore, invalid_use_of_internal_member, annotate_overrides, non_constant_identifier_names, curly_braces_in_flow_control_structures, prefer_const_literals_to_create_immutables, unused_field

import 'bridge.dart';
import 'core/image_authenticator.dart';
import 'dart:async';
import 'dart:convert';
import 'frb_generated.dart';
import 'frb_generated.io.dart' if (dart.library.js_interop) 'frb_generated.web.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';

/// Main entrypoint of the Rust API
class TushenCore extends BaseEntrypoint<TushenCoreApi, TushenCoreApiImpl, TushenCoreWire> {
  @internal
  static final instance = TushenCore._();

  TushenCore._();

  /// Initialize flutter_rust_bridge
  static Future<void> init({TushenCoreApi? api, BaseHandler? handler, ExternalLibrary? externalLibrary}) async {
    await instance.initImpl(api: api, handler: handler, externalLibrary: externalLibrary);
  }

  /// Initialize flutter_rust_bridge in mock mode.
  /// No libraries for FFI are loaded.
  static void initMock({required TushenCoreApi api}) {
    instance.initMockImpl(api: api);
  }

  /// Dispose flutter_rust_bridge
  ///
  /// The call to this function is optional, since flutter_rust_bridge (and everything else)
  /// is automatically disposed when the app stops.
  static void dispose() => instance.disposeImpl();

  @override
  ApiImplConstructor<TushenCoreApiImpl, TushenCoreWire> get apiImplConstructor => TushenCoreApiImpl.new;

  @override
  WireConstructor<TushenCoreWire> get wireConstructor => TushenCoreWire.fromExternalLibrary;

  @override
  Future<void> executeRustInitializers() async {}

  @override
  ExternalLibraryLoaderConfig get defaultExternalLibraryLoaderConfig => kDefaultExternalLibraryLoaderConfig;

  @override
  String get codegenVersion => '2.9.0';

  @override
  int get rustContentHash => 1768860821;

  static const kDefaultExternalLibraryLoaderConfig = ExternalLibraryLoaderConfig(
    stem: 'tushen_core',
    ioDirectory: 'rust/target/release/',
    webPrefix: 'pkg/',
  );
}

abstract class TushenCoreApi extends BaseApi {
  Future<ImageDataBridge> crateBridgeFilterEngineBridgeApplyFilter({
    required FilterEngineBridge that,
    required ImageDataBridge image,
    required String filterType,
    required double intensity,
  });

  List<String> crateBridgeFilterEngineBridgeGetAvailableFilters({required FilterEngineBridge that});

  Future<ImageDataBridge> crateBridgeFilterEngineBridgeGetFilterPreview({
    required FilterEngineBridge that,
    required ImageDataBridge image,
    required String filterType,
    required int previewSize,
  });

  FilterEngineBridge crateBridgeFilterEngineBridgeNew();

  Future<AuthenticationConfigBridge> crateBridgeAuthenticationConfigBridgeDefault();

  Future<ImageDataBridge> crateBridgeCollageEngineBridgeCreateCollage({
    required CollageEngineBridge that,
    required List<ImageDataBridge> images,
    required String layout,
    required int canvasWidth,
    required int canvasHeight,
    required int spacing,
    String? backgroundColor,
  });

  List<String> crateBridgeCollageEngineBridgeGetAvailableLayouts({required CollageEngineBridge that});

  Future<List<RectBridge>> crateBridgeCollageEngineBridgeGetLayoutPreview({
    required CollageEngineBridge that,
    required String layout,
    required int canvasWidth,
    required int canvasHeight,
  });

  CollageEngineBridge crateBridgeCollageEngineBridgeNew();

  String crateBridgeGetLibraryInfo();

  Future<AuthenticationResultBridge> crateBridgeImageAuthenticatorBridgeAuthenticateImage({
    required ImageAuthenticatorBridge that,
    required ImageDataBridge imageData,
  });

  ImageAuthenticatorBridge crateBridgeImageAuthenticatorBridgeNew();

  ImageAuthenticatorBridge crateBridgeImageAuthenticatorBridgeWithConfig({required AuthenticationConfigBridge config});

  Future<CompressionResultBridge> crateBridgeImageCompressorBridgeCompressImage({
    required ImageCompressorBridge that,
    required ImageDataBridge image,
    required String format,
    required int quality,
    int? targetSizeKb,
    int? resizeWidth,
    int? resizeHeight,
    required bool maintainAspectRatio,
  });

  Future<CompressionEstimateBridge> crateBridgeImageCompressorBridgeEstimateCompression({
    required ImageCompressorBridge that,
    required ImageDataBridge image,
    required String format,
    required int quality,
  });

  List<String> crateBridgeImageCompressorBridgeGetSupportedFormats({required ImageCompressorBridge that});

  ImageCompressorBridge crateBridgeImageCompressorBridgeNew();

  Future<ImageDataBridge> crateBridgeImageProcessorBridgeCropImage({
    required ImageProcessorBridge that,
    required ImageDataBridge image,
    required int x,
    required int y,
    required int width,
    required int height,
  });

  Future<ImageDataBridge> crateBridgeImageProcessorBridgeLoadImageFromBytes({
    required ImageProcessorBridge that,
    required List<int> bytes,
  });

  Future<ImageDataBridge> crateBridgeImageProcessorBridgeLoadImageFromPath({
    required ImageProcessorBridge that,
    required String path,
  });

  ImageProcessorBridge crateBridgeImageProcessorBridgeNew();

  Future<ImageDataBridge> crateBridgeImageProcessorBridgeResizeImage({
    required ImageProcessorBridge that,
    required ImageDataBridge image,
    required int width,
    required int height,
    required bool maintainAspectRatio,
  });

  Future<ImageDataBridge> crateBridgeImageProcessorBridgeRotateImage({
    required ImageProcessorBridge that,
    required ImageDataBridge image,
    required double angle,
  });

  Future<Uint8List> crateBridgeImageProcessorBridgeSaveImageToBytes({
    required ImageProcessorBridge that,
    required ImageDataBridge image,
    required String format,
    int? quality,
  });

  String crateBridgeInitTushenCore();

  RustArcIncrementStrongCountFnType get rust_arc_increment_strong_count_FilterEngineBridge;

  RustArcDecrementStrongCountFnType get rust_arc_decrement_strong_count_FilterEngineBridge;

  CrossPlatformFinalizerArg get rust_arc_decrement_strong_count_FilterEngineBridgePtr;
}

class TushenCoreApiImpl extends TushenCoreApiImplPlatform implements TushenCoreApi {
  TushenCoreApiImpl({
    required super.handler,
    required super.wire,
    required super.generalizedFrbRustBinding,
    required super.portManager,
  });

  @override
  Future<ImageDataBridge> crateBridgeFilterEngineBridgeApplyFilter({
    required FilterEngineBridge that,
    required ImageDataBridge image,
    required String filterType,
    required double intensity,
  }) {
    return handler.executeNormal(
      NormalTask(
        callFfi: (port_) {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFilterEngineBridge(
            that,
            serializer,
          );
          sse_encode_box_autoadd_image_data_bridge(image, serializer);
          sse_encode_String(filterType, serializer);
          sse_encode_f_32(intensity, serializer);
          pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 1, port: port_);
        },
        codec: SseCodec(decodeSuccessData: sse_decode_image_data_bridge, decodeErrorData: sse_decode_String),
        constMeta: kCrateBridgeFilterEngineBridgeApplyFilterConstMeta,
        argValues: [that, image, filterType, intensity],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateBridgeFilterEngineBridgeApplyFilterConstMeta => const TaskConstMeta(
    debugName: "FilterEngineBridge_apply_filter",
    argNames: ["that", "image", "filterType", "intensity"],
  );

  @override
  List<String> crateBridgeFilterEngineBridgeGetAvailableFilters({required FilterEngineBridge that}) {
    return handler.executeSync(
      SyncTask(
        callFfi: () {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFilterEngineBridge(
            that,
            serializer,
          );
          return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 2)!;
        },
        codec: SseCodec(decodeSuccessData: sse_decode_list_String, decodeErrorData: null),
        constMeta: kCrateBridgeFilterEngineBridgeGetAvailableFiltersConstMeta,
        argValues: [that],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateBridgeFilterEngineBridgeGetAvailableFiltersConstMeta =>
      const TaskConstMeta(debugName: "FilterEngineBridge_get_available_filters", argNames: ["that"]);

  @override
  Future<ImageDataBridge> crateBridgeFilterEngineBridgeGetFilterPreview({
    required FilterEngineBridge that,
    required ImageDataBridge image,
    required String filterType,
    required int previewSize,
  }) {
    return handler.executeNormal(
      NormalTask(
        callFfi: (port_) {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFilterEngineBridge(
            that,
            serializer,
          );
          sse_encode_box_autoadd_image_data_bridge(image, serializer);
          sse_encode_String(filterType, serializer);
          sse_encode_u_32(previewSize, serializer);
          pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 3, port: port_);
        },
        codec: SseCodec(decodeSuccessData: sse_decode_image_data_bridge, decodeErrorData: sse_decode_String),
        constMeta: kCrateBridgeFilterEngineBridgeGetFilterPreviewConstMeta,
        argValues: [that, image, filterType, previewSize],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateBridgeFilterEngineBridgeGetFilterPreviewConstMeta => const TaskConstMeta(
    debugName: "FilterEngineBridge_get_filter_preview",
    argNames: ["that", "image", "filterType", "previewSize"],
  );

  @override
  FilterEngineBridge crateBridgeFilterEngineBridgeNew() {
    return handler.executeSync(
      SyncTask(
        callFfi: () {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 4)!;
        },
        codec: SseCodec(
          decodeSuccessData:
              sse_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFilterEngineBridge,
          decodeErrorData: null,
        ),
        constMeta: kCrateBridgeFilterEngineBridgeNewConstMeta,
        argValues: [],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateBridgeFilterEngineBridgeNewConstMeta =>
      const TaskConstMeta(debugName: "FilterEngineBridge_new", argNames: []);

  @override
  Future<AuthenticationConfigBridge> crateBridgeAuthenticationConfigBridgeDefault() {
    return handler.executeNormal(
      NormalTask(
        callFfi: (port_) {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 5, port: port_);
        },
        codec: SseCodec(decodeSuccessData: sse_decode_authentication_config_bridge, decodeErrorData: null),
        constMeta: kCrateBridgeAuthenticationConfigBridgeDefaultConstMeta,
        argValues: [],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateBridgeAuthenticationConfigBridgeDefaultConstMeta =>
      const TaskConstMeta(debugName: "authentication_config_bridge_default", argNames: []);

  @override
  Future<ImageDataBridge> crateBridgeCollageEngineBridgeCreateCollage({
    required CollageEngineBridge that,
    required List<ImageDataBridge> images,
    required String layout,
    required int canvasWidth,
    required int canvasHeight,
    required int spacing,
    String? backgroundColor,
  }) {
    return handler.executeNormal(
      NormalTask(
        callFfi: (port_) {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_box_autoadd_collage_engine_bridge(that, serializer);
          sse_encode_list_image_data_bridge(images, serializer);
          sse_encode_String(layout, serializer);
          sse_encode_u_32(canvasWidth, serializer);
          sse_encode_u_32(canvasHeight, serializer);
          sse_encode_u_32(spacing, serializer);
          sse_encode_opt_String(backgroundColor, serializer);
          pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 6, port: port_);
        },
        codec: SseCodec(decodeSuccessData: sse_decode_image_data_bridge, decodeErrorData: sse_decode_String),
        constMeta: kCrateBridgeCollageEngineBridgeCreateCollageConstMeta,
        argValues: [that, images, layout, canvasWidth, canvasHeight, spacing, backgroundColor],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateBridgeCollageEngineBridgeCreateCollageConstMeta => const TaskConstMeta(
    debugName: "collage_engine_bridge_create_collage",
    argNames: ["that", "images", "layout", "canvasWidth", "canvasHeight", "spacing", "backgroundColor"],
  );

  @override
  List<String> crateBridgeCollageEngineBridgeGetAvailableLayouts({required CollageEngineBridge that}) {
    return handler.executeSync(
      SyncTask(
        callFfi: () {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_box_autoadd_collage_engine_bridge(that, serializer);
          return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 7)!;
        },
        codec: SseCodec(decodeSuccessData: sse_decode_list_String, decodeErrorData: null),
        constMeta: kCrateBridgeCollageEngineBridgeGetAvailableLayoutsConstMeta,
        argValues: [that],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateBridgeCollageEngineBridgeGetAvailableLayoutsConstMeta =>
      const TaskConstMeta(debugName: "collage_engine_bridge_get_available_layouts", argNames: ["that"]);

  @override
  Future<List<RectBridge>> crateBridgeCollageEngineBridgeGetLayoutPreview({
    required CollageEngineBridge that,
    required String layout,
    required int canvasWidth,
    required int canvasHeight,
  }) {
    return handler.executeNormal(
      NormalTask(
        callFfi: (port_) {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_box_autoadd_collage_engine_bridge(that, serializer);
          sse_encode_String(layout, serializer);
          sse_encode_u_32(canvasWidth, serializer);
          sse_encode_u_32(canvasHeight, serializer);
          pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 8, port: port_);
        },
        codec: SseCodec(decodeSuccessData: sse_decode_list_rect_bridge, decodeErrorData: sse_decode_String),
        constMeta: kCrateBridgeCollageEngineBridgeGetLayoutPreviewConstMeta,
        argValues: [that, layout, canvasWidth, canvasHeight],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateBridgeCollageEngineBridgeGetLayoutPreviewConstMeta => const TaskConstMeta(
    debugName: "collage_engine_bridge_get_layout_preview",
    argNames: ["that", "layout", "canvasWidth", "canvasHeight"],
  );

  @override
  CollageEngineBridge crateBridgeCollageEngineBridgeNew() {
    return handler.executeSync(
      SyncTask(
        callFfi: () {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 9)!;
        },
        codec: SseCodec(decodeSuccessData: sse_decode_collage_engine_bridge, decodeErrorData: null),
        constMeta: kCrateBridgeCollageEngineBridgeNewConstMeta,
        argValues: [],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateBridgeCollageEngineBridgeNewConstMeta =>
      const TaskConstMeta(debugName: "collage_engine_bridge_new", argNames: []);

  @override
  String crateBridgeGetLibraryInfo() {
    return handler.executeSync(
      SyncTask(
        callFfi: () {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 10)!;
        },
        codec: SseCodec(decodeSuccessData: sse_decode_String, decodeErrorData: null),
        constMeta: kCrateBridgeGetLibraryInfoConstMeta,
        argValues: [],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateBridgeGetLibraryInfoConstMeta =>
      const TaskConstMeta(debugName: "get_library_info", argNames: []);

  @override
  Future<AuthenticationResultBridge> crateBridgeImageAuthenticatorBridgeAuthenticateImage({
    required ImageAuthenticatorBridge that,
    required ImageDataBridge imageData,
  }) {
    return handler.executeNormal(
      NormalTask(
        callFfi: (port_) {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_box_autoadd_image_authenticator_bridge(that, serializer);
          sse_encode_box_autoadd_image_data_bridge(imageData, serializer);
          pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 11, port: port_);
        },
        codec: SseCodec(decodeSuccessData: sse_decode_authentication_result_bridge, decodeErrorData: sse_decode_String),
        constMeta: kCrateBridgeImageAuthenticatorBridgeAuthenticateImageConstMeta,
        argValues: [that, imageData],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateBridgeImageAuthenticatorBridgeAuthenticateImageConstMeta =>
      const TaskConstMeta(debugName: "image_authenticator_bridge_authenticate_image", argNames: ["that", "imageData"]);

  @override
  ImageAuthenticatorBridge crateBridgeImageAuthenticatorBridgeNew() {
    return handler.executeSync(
      SyncTask(
        callFfi: () {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 12)!;
        },
        codec: SseCodec(decodeSuccessData: sse_decode_image_authenticator_bridge, decodeErrorData: null),
        constMeta: kCrateBridgeImageAuthenticatorBridgeNewConstMeta,
        argValues: [],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateBridgeImageAuthenticatorBridgeNewConstMeta =>
      const TaskConstMeta(debugName: "image_authenticator_bridge_new", argNames: []);

  @override
  ImageAuthenticatorBridge crateBridgeImageAuthenticatorBridgeWithConfig({required AuthenticationConfigBridge config}) {
    return handler.executeSync(
      SyncTask(
        callFfi: () {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_box_autoadd_authentication_config_bridge(config, serializer);
          return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 13)!;
        },
        codec: SseCodec(decodeSuccessData: sse_decode_image_authenticator_bridge, decodeErrorData: null),
        constMeta: kCrateBridgeImageAuthenticatorBridgeWithConfigConstMeta,
        argValues: [config],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateBridgeImageAuthenticatorBridgeWithConfigConstMeta =>
      const TaskConstMeta(debugName: "image_authenticator_bridge_with_config", argNames: ["config"]);

  @override
  Future<CompressionResultBridge> crateBridgeImageCompressorBridgeCompressImage({
    required ImageCompressorBridge that,
    required ImageDataBridge image,
    required String format,
    required int quality,
    int? targetSizeKb,
    int? resizeWidth,
    int? resizeHeight,
    required bool maintainAspectRatio,
  }) {
    return handler.executeNormal(
      NormalTask(
        callFfi: (port_) {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_box_autoadd_image_compressor_bridge(that, serializer);
          sse_encode_box_autoadd_image_data_bridge(image, serializer);
          sse_encode_String(format, serializer);
          sse_encode_u_8(quality, serializer);
          sse_encode_opt_box_autoadd_u_32(targetSizeKb, serializer);
          sse_encode_opt_box_autoadd_u_32(resizeWidth, serializer);
          sse_encode_opt_box_autoadd_u_32(resizeHeight, serializer);
          sse_encode_bool(maintainAspectRatio, serializer);
          pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 14, port: port_);
        },
        codec: SseCodec(decodeSuccessData: sse_decode_compression_result_bridge, decodeErrorData: sse_decode_String),
        constMeta: kCrateBridgeImageCompressorBridgeCompressImageConstMeta,
        argValues: [that, image, format, quality, targetSizeKb, resizeWidth, resizeHeight, maintainAspectRatio],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateBridgeImageCompressorBridgeCompressImageConstMeta => const TaskConstMeta(
    debugName: "image_compressor_bridge_compress_image",
    argNames: [
      "that",
      "image",
      "format",
      "quality",
      "targetSizeKb",
      "resizeWidth",
      "resizeHeight",
      "maintainAspectRatio",
    ],
  );

  @override
  Future<CompressionEstimateBridge> crateBridgeImageCompressorBridgeEstimateCompression({
    required ImageCompressorBridge that,
    required ImageDataBridge image,
    required String format,
    required int quality,
  }) {
    return handler.executeNormal(
      NormalTask(
        callFfi: (port_) {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_box_autoadd_image_compressor_bridge(that, serializer);
          sse_encode_box_autoadd_image_data_bridge(image, serializer);
          sse_encode_String(format, serializer);
          sse_encode_u_8(quality, serializer);
          pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 15, port: port_);
        },
        codec: SseCodec(decodeSuccessData: sse_decode_compression_estimate_bridge, decodeErrorData: sse_decode_String),
        constMeta: kCrateBridgeImageCompressorBridgeEstimateCompressionConstMeta,
        argValues: [that, image, format, quality],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateBridgeImageCompressorBridgeEstimateCompressionConstMeta => const TaskConstMeta(
    debugName: "image_compressor_bridge_estimate_compression",
    argNames: ["that", "image", "format", "quality"],
  );

  @override
  List<String> crateBridgeImageCompressorBridgeGetSupportedFormats({required ImageCompressorBridge that}) {
    return handler.executeSync(
      SyncTask(
        callFfi: () {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_box_autoadd_image_compressor_bridge(that, serializer);
          return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 16)!;
        },
        codec: SseCodec(decodeSuccessData: sse_decode_list_String, decodeErrorData: null),
        constMeta: kCrateBridgeImageCompressorBridgeGetSupportedFormatsConstMeta,
        argValues: [that],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateBridgeImageCompressorBridgeGetSupportedFormatsConstMeta =>
      const TaskConstMeta(debugName: "image_compressor_bridge_get_supported_formats", argNames: ["that"]);

  @override
  ImageCompressorBridge crateBridgeImageCompressorBridgeNew() {
    return handler.executeSync(
      SyncTask(
        callFfi: () {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 17)!;
        },
        codec: SseCodec(decodeSuccessData: sse_decode_image_compressor_bridge, decodeErrorData: null),
        constMeta: kCrateBridgeImageCompressorBridgeNewConstMeta,
        argValues: [],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateBridgeImageCompressorBridgeNewConstMeta =>
      const TaskConstMeta(debugName: "image_compressor_bridge_new", argNames: []);

  @override
  Future<ImageDataBridge> crateBridgeImageProcessorBridgeCropImage({
    required ImageProcessorBridge that,
    required ImageDataBridge image,
    required int x,
    required int y,
    required int width,
    required int height,
  }) {
    return handler.executeNormal(
      NormalTask(
        callFfi: (port_) {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_box_autoadd_image_processor_bridge(that, serializer);
          sse_encode_box_autoadd_image_data_bridge(image, serializer);
          sse_encode_u_32(x, serializer);
          sse_encode_u_32(y, serializer);
          sse_encode_u_32(width, serializer);
          sse_encode_u_32(height, serializer);
          pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 18, port: port_);
        },
        codec: SseCodec(decodeSuccessData: sse_decode_image_data_bridge, decodeErrorData: sse_decode_String),
        constMeta: kCrateBridgeImageProcessorBridgeCropImageConstMeta,
        argValues: [that, image, x, y, width, height],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateBridgeImageProcessorBridgeCropImageConstMeta => const TaskConstMeta(
    debugName: "image_processor_bridge_crop_image",
    argNames: ["that", "image", "x", "y", "width", "height"],
  );

  @override
  Future<ImageDataBridge> crateBridgeImageProcessorBridgeLoadImageFromBytes({
    required ImageProcessorBridge that,
    required List<int> bytes,
  }) {
    return handler.executeNormal(
      NormalTask(
        callFfi: (port_) {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_box_autoadd_image_processor_bridge(that, serializer);
          sse_encode_list_prim_u_8_loose(bytes, serializer);
          pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 19, port: port_);
        },
        codec: SseCodec(decodeSuccessData: sse_decode_image_data_bridge, decodeErrorData: sse_decode_String),
        constMeta: kCrateBridgeImageProcessorBridgeLoadImageFromBytesConstMeta,
        argValues: [that, bytes],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateBridgeImageProcessorBridgeLoadImageFromBytesConstMeta =>
      const TaskConstMeta(debugName: "image_processor_bridge_load_image_from_bytes", argNames: ["that", "bytes"]);

  @override
  Future<ImageDataBridge> crateBridgeImageProcessorBridgeLoadImageFromPath({
    required ImageProcessorBridge that,
    required String path,
  }) {
    return handler.executeNormal(
      NormalTask(
        callFfi: (port_) {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_box_autoadd_image_processor_bridge(that, serializer);
          sse_encode_String(path, serializer);
          pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 20, port: port_);
        },
        codec: SseCodec(decodeSuccessData: sse_decode_image_data_bridge, decodeErrorData: sse_decode_String),
        constMeta: kCrateBridgeImageProcessorBridgeLoadImageFromPathConstMeta,
        argValues: [that, path],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateBridgeImageProcessorBridgeLoadImageFromPathConstMeta =>
      const TaskConstMeta(debugName: "image_processor_bridge_load_image_from_path", argNames: ["that", "path"]);

  @override
  ImageProcessorBridge crateBridgeImageProcessorBridgeNew() {
    return handler.executeSync(
      SyncTask(
        callFfi: () {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 21)!;
        },
        codec: SseCodec(decodeSuccessData: sse_decode_image_processor_bridge, decodeErrorData: null),
        constMeta: kCrateBridgeImageProcessorBridgeNewConstMeta,
        argValues: [],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateBridgeImageProcessorBridgeNewConstMeta =>
      const TaskConstMeta(debugName: "image_processor_bridge_new", argNames: []);

  @override
  Future<ImageDataBridge> crateBridgeImageProcessorBridgeResizeImage({
    required ImageProcessorBridge that,
    required ImageDataBridge image,
    required int width,
    required int height,
    required bool maintainAspectRatio,
  }) {
    return handler.executeNormal(
      NormalTask(
        callFfi: (port_) {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_box_autoadd_image_processor_bridge(that, serializer);
          sse_encode_box_autoadd_image_data_bridge(image, serializer);
          sse_encode_u_32(width, serializer);
          sse_encode_u_32(height, serializer);
          sse_encode_bool(maintainAspectRatio, serializer);
          pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 22, port: port_);
        },
        codec: SseCodec(decodeSuccessData: sse_decode_image_data_bridge, decodeErrorData: sse_decode_String),
        constMeta: kCrateBridgeImageProcessorBridgeResizeImageConstMeta,
        argValues: [that, image, width, height, maintainAspectRatio],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateBridgeImageProcessorBridgeResizeImageConstMeta => const TaskConstMeta(
    debugName: "image_processor_bridge_resize_image",
    argNames: ["that", "image", "width", "height", "maintainAspectRatio"],
  );

  @override
  Future<ImageDataBridge> crateBridgeImageProcessorBridgeRotateImage({
    required ImageProcessorBridge that,
    required ImageDataBridge image,
    required double angle,
  }) {
    return handler.executeNormal(
      NormalTask(
        callFfi: (port_) {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_box_autoadd_image_processor_bridge(that, serializer);
          sse_encode_box_autoadd_image_data_bridge(image, serializer);
          sse_encode_f_32(angle, serializer);
          pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 23, port: port_);
        },
        codec: SseCodec(decodeSuccessData: sse_decode_image_data_bridge, decodeErrorData: sse_decode_String),
        constMeta: kCrateBridgeImageProcessorBridgeRotateImageConstMeta,
        argValues: [that, image, angle],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateBridgeImageProcessorBridgeRotateImageConstMeta =>
      const TaskConstMeta(debugName: "image_processor_bridge_rotate_image", argNames: ["that", "image", "angle"]);

  @override
  Future<Uint8List> crateBridgeImageProcessorBridgeSaveImageToBytes({
    required ImageProcessorBridge that,
    required ImageDataBridge image,
    required String format,
    int? quality,
  }) {
    return handler.executeNormal(
      NormalTask(
        callFfi: (port_) {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_box_autoadd_image_processor_bridge(that, serializer);
          sse_encode_box_autoadd_image_data_bridge(image, serializer);
          sse_encode_String(format, serializer);
          sse_encode_opt_box_autoadd_u_8(quality, serializer);
          pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 24, port: port_);
        },
        codec: SseCodec(decodeSuccessData: sse_decode_list_prim_u_8_strict, decodeErrorData: sse_decode_String),
        constMeta: kCrateBridgeImageProcessorBridgeSaveImageToBytesConstMeta,
        argValues: [that, image, format, quality],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateBridgeImageProcessorBridgeSaveImageToBytesConstMeta => const TaskConstMeta(
    debugName: "image_processor_bridge_save_image_to_bytes",
    argNames: ["that", "image", "format", "quality"],
  );

  @override
  String crateBridgeInitTushenCore() {
    return handler.executeSync(
      SyncTask(
        callFfi: () {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 25)!;
        },
        codec: SseCodec(decodeSuccessData: sse_decode_String, decodeErrorData: sse_decode_String),
        constMeta: kCrateBridgeInitTushenCoreConstMeta,
        argValues: [],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateBridgeInitTushenCoreConstMeta =>
      const TaskConstMeta(debugName: "init_tushen_core", argNames: []);

  RustArcIncrementStrongCountFnType get rust_arc_increment_strong_count_FilterEngineBridge => wire
      .rust_arc_increment_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFilterEngineBridge;

  RustArcDecrementStrongCountFnType get rust_arc_decrement_strong_count_FilterEngineBridge => wire
      .rust_arc_decrement_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFilterEngineBridge;

  @protected
  FilterEngineBridge
  dco_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFilterEngineBridge(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return FilterEngineBridgeImpl.frbInternalDcoDecode(raw as List<dynamic>);
  }

  @protected
  FilterEngineBridge
  dco_decode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFilterEngineBridge(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return FilterEngineBridgeImpl.frbInternalDcoDecode(raw as List<dynamic>);
  }

  @protected
  FilterEngineBridge dco_decode_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFilterEngineBridge(
    dynamic raw,
  ) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return FilterEngineBridgeImpl.frbInternalDcoDecode(raw as List<dynamic>);
  }

  @protected
  String dco_decode_String(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as String;
  }

  @protected
  AnalysisImageBridge dco_decode_analysis_image_bridge(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.length != 2) throw Exception('unexpected arr length: expect 2 but see ${arr.length}');
    return AnalysisImageBridge(name: dco_decode_String(arr[0]), imageData: dco_decode_image_data_bridge(arr[1]));
  }

  @protected
  AnalysisResultBridge dco_decode_analysis_result_bridge(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.length != 4) throw Exception('unexpected arr length: expect 4 but see ${arr.length}');
    return AnalysisResultBridge(
      analysisType: dco_decode_String(arr[0]),
      confidence: dco_decode_f_32(arr[1]),
      anomalies: dco_decode_list_String(arr[2]),
      metadata: dco_decode_list_metadata_pair_bridge(arr[3]),
    );
  }

  @protected
  AuthenticationConfig dco_decode_authentication_config(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.length != 5) throw Exception('unexpected arr length: expect 5 but see ${arr.length}');
    return AuthenticationConfig(
      elaQuality: dco_decode_u_8(arr[0]),
      copyMoveThreshold: dco_decode_f_32(arr[1]),
      blockSize: dco_decode_u_32(arr[2]),
      noiseSensitivity: dco_decode_f_32(arr[3]),
      analyzeMetadata: dco_decode_bool(arr[4]),
    );
  }

  @protected
  AuthenticationConfigBridge dco_decode_authentication_config_bridge(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.length != 5) throw Exception('unexpected arr length: expect 5 but see ${arr.length}');
    return AuthenticationConfigBridge(
      elaQuality: dco_decode_u_8(arr[0]),
      copyMoveThreshold: dco_decode_f_32(arr[1]),
      blockSize: dco_decode_u_32(arr[2]),
      noiseSensitivity: dco_decode_f_32(arr[3]),
      analyzeMetadata: dco_decode_bool(arr[4]),
    );
  }

  @protected
  AuthenticationResultBridge dco_decode_authentication_result_bridge(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.length != 5) throw Exception('unexpected arr length: expect 5 but see ${arr.length}');
    return AuthenticationResultBridge(
      authenticityScore: dco_decode_f_32(arr[0]),
      analyses: dco_decode_list_analysis_result_bridge(arr[1]),
      manipulationRegions: dco_decode_list_manipulation_region_bridge(arr[2]),
      processingTimeMs: dco_decode_u_64(arr[3]),
      analysisImages: dco_decode_list_analysis_image_bridge(arr[4]),
    );
  }

  @protected
  bool dco_decode_bool(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as bool;
  }

  @protected
  AuthenticationConfigBridge dco_decode_box_autoadd_authentication_config_bridge(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return dco_decode_authentication_config_bridge(raw);
  }

  @protected
  CollageEngineBridge dco_decode_box_autoadd_collage_engine_bridge(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return dco_decode_collage_engine_bridge(raw);
  }

  @protected
  ImageAuthenticatorBridge dco_decode_box_autoadd_image_authenticator_bridge(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return dco_decode_image_authenticator_bridge(raw);
  }

  @protected
  ImageCompressorBridge dco_decode_box_autoadd_image_compressor_bridge(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return dco_decode_image_compressor_bridge(raw);
  }

  @protected
  ImageDataBridge dco_decode_box_autoadd_image_data_bridge(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return dco_decode_image_data_bridge(raw);
  }

  @protected
  ImageProcessorBridge dco_decode_box_autoadd_image_processor_bridge(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return dco_decode_image_processor_bridge(raw);
  }

  @protected
  int dco_decode_box_autoadd_u_32(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as int;
  }

  @protected
  int dco_decode_box_autoadd_u_8(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as int;
  }

  @protected
  CollageEngineBridge dco_decode_collage_engine_bridge(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.isNotEmpty) throw Exception('unexpected arr length: expect 0 but see ${arr.length}');
    return CollageEngineBridge.raw();
  }

  @protected
  CompressionEstimateBridge dco_decode_compression_estimate_bridge(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.length != 3) throw Exception('unexpected arr length: expect 3 but see ${arr.length}');
    return CompressionEstimateBridge(
      estimatedSize: dco_decode_u_64(arr[0]),
      compressionRatio: dco_decode_f_32(arr[1]),
      spaceSaved: dco_decode_u_64(arr[2]),
    );
  }

  @protected
  CompressionResultBridge dco_decode_compression_result_bridge(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.length != 9) throw Exception('unexpected arr length: expect 9 but see ${arr.length}');
    return CompressionResultBridge(
      imageData: dco_decode_image_data_bridge(arr[0]),
      originalSize: dco_decode_u_64(arr[1]),
      compressedSize: dco_decode_u_64(arr[2]),
      compressionRatio: dco_decode_f_32(arr[3]),
      spaceSaved: dco_decode_u_64(arr[4]),
      spaceSavedPercentage: dco_decode_f_32(arr[5]),
      finalQuality: dco_decode_u_8(arr[6]),
      iterations: dco_decode_u_32(arr[7]),
      targetAchieved: dco_decode_bool(arr[8]),
    );
  }

  @protected
  double dco_decode_f_32(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as double;
  }

  @protected
  ImageAuthenticator dco_decode_image_authenticator(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.length != 1) throw Exception('unexpected arr length: expect 1 but see ${arr.length}');
    return ImageAuthenticator(config: dco_decode_authentication_config(arr[0]));
  }

  @protected
  ImageAuthenticatorBridge dco_decode_image_authenticator_bridge(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.length != 1) throw Exception('unexpected arr length: expect 1 but see ${arr.length}');
    return ImageAuthenticatorBridge.raw(authenticator: dco_decode_image_authenticator(arr[0]));
  }

  @protected
  ImageCompressorBridge dco_decode_image_compressor_bridge(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.isNotEmpty) throw Exception('unexpected arr length: expect 0 but see ${arr.length}');
    return ImageCompressorBridge.raw();
  }

  @protected
  ImageDataBridge dco_decode_image_data_bridge(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.length != 5) throw Exception('unexpected arr length: expect 5 but see ${arr.length}');
    return ImageDataBridge(
      width: dco_decode_u_32(arr[0]),
      height: dco_decode_u_32(arr[1]),
      format: dco_decode_String(arr[2]),
      colorSpace: dco_decode_String(arr[3]),
      data: dco_decode_list_prim_u_8_strict(arr[4]),
    );
  }

  @protected
  ImageProcessorBridge dco_decode_image_processor_bridge(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.isNotEmpty) throw Exception('unexpected arr length: expect 0 but see ${arr.length}');
    return ImageProcessorBridge.raw();
  }

  @protected
  List<String> dco_decode_list_String(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return (raw as List<dynamic>).map(dco_decode_String).toList();
  }

  @protected
  List<AnalysisImageBridge> dco_decode_list_analysis_image_bridge(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return (raw as List<dynamic>).map(dco_decode_analysis_image_bridge).toList();
  }

  @protected
  List<AnalysisResultBridge> dco_decode_list_analysis_result_bridge(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return (raw as List<dynamic>).map(dco_decode_analysis_result_bridge).toList();
  }

  @protected
  List<ImageDataBridge> dco_decode_list_image_data_bridge(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return (raw as List<dynamic>).map(dco_decode_image_data_bridge).toList();
  }

  @protected
  List<ManipulationRegionBridge> dco_decode_list_manipulation_region_bridge(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return (raw as List<dynamic>).map(dco_decode_manipulation_region_bridge).toList();
  }

  @protected
  List<MetadataPairBridge> dco_decode_list_metadata_pair_bridge(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return (raw as List<dynamic>).map(dco_decode_metadata_pair_bridge).toList();
  }

  @protected
  List<int> dco_decode_list_prim_u_8_loose(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as List<int>;
  }

  @protected
  Uint8List dco_decode_list_prim_u_8_strict(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as Uint8List;
  }

  @protected
  List<RectBridge> dco_decode_list_rect_bridge(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return (raw as List<dynamic>).map(dco_decode_rect_bridge).toList();
  }

  @protected
  ManipulationRegionBridge dco_decode_manipulation_region_bridge(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.length != 7) throw Exception('unexpected arr length: expect 7 but see ${arr.length}');
    return ManipulationRegionBridge(
      x: dco_decode_u_32(arr[0]),
      y: dco_decode_u_32(arr[1]),
      width: dco_decode_u_32(arr[2]),
      height: dco_decode_u_32(arr[3]),
      confidence: dco_decode_f_32(arr[4]),
      manipulationType: dco_decode_String(arr[5]),
      details: dco_decode_String(arr[6]),
    );
  }

  @protected
  MetadataPairBridge dco_decode_metadata_pair_bridge(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.length != 2) throw Exception('unexpected arr length: expect 2 but see ${arr.length}');
    return MetadataPairBridge(key: dco_decode_String(arr[0]), value: dco_decode_String(arr[1]));
  }

  @protected
  String? dco_decode_opt_String(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw == null ? null : dco_decode_String(raw);
  }

  @protected
  int? dco_decode_opt_box_autoadd_u_32(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw == null ? null : dco_decode_box_autoadd_u_32(raw);
  }

  @protected
  int? dco_decode_opt_box_autoadd_u_8(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw == null ? null : dco_decode_box_autoadd_u_8(raw);
  }

  @protected
  RectBridge dco_decode_rect_bridge(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.length != 4) throw Exception('unexpected arr length: expect 4 but see ${arr.length}');
    return RectBridge(
      x: dco_decode_u_32(arr[0]),
      y: dco_decode_u_32(arr[1]),
      width: dco_decode_u_32(arr[2]),
      height: dco_decode_u_32(arr[3]),
    );
  }

  @protected
  int dco_decode_u_32(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as int;
  }

  @protected
  BigInt dco_decode_u_64(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return dcoDecodeU64(raw);
  }

  @protected
  int dco_decode_u_8(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as int;
  }

  @protected
  void dco_decode_unit(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return;
  }

  @protected
  BigInt dco_decode_usize(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return dcoDecodeU64(raw);
  }

  @protected
  FilterEngineBridge
  sse_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFilterEngineBridge(
    SseDeserializer deserializer,
  ) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return FilterEngineBridgeImpl.frbInternalSseDecode(sse_decode_usize(deserializer), sse_decode_i_32(deserializer));
  }

  @protected
  FilterEngineBridge
  sse_decode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFilterEngineBridge(
    SseDeserializer deserializer,
  ) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return FilterEngineBridgeImpl.frbInternalSseDecode(sse_decode_usize(deserializer), sse_decode_i_32(deserializer));
  }

  @protected
  FilterEngineBridge sse_decode_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFilterEngineBridge(
    SseDeserializer deserializer,
  ) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return FilterEngineBridgeImpl.frbInternalSseDecode(sse_decode_usize(deserializer), sse_decode_i_32(deserializer));
  }

  @protected
  String sse_decode_String(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var inner = sse_decode_list_prim_u_8_strict(deserializer);
    return utf8.decoder.convert(inner);
  }

  @protected
  AnalysisImageBridge sse_decode_analysis_image_bridge(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var var_name = sse_decode_String(deserializer);
    var var_imageData = sse_decode_image_data_bridge(deserializer);
    return AnalysisImageBridge(name: var_name, imageData: var_imageData);
  }

  @protected
  AnalysisResultBridge sse_decode_analysis_result_bridge(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var var_analysisType = sse_decode_String(deserializer);
    var var_confidence = sse_decode_f_32(deserializer);
    var var_anomalies = sse_decode_list_String(deserializer);
    var var_metadata = sse_decode_list_metadata_pair_bridge(deserializer);
    return AnalysisResultBridge(
      analysisType: var_analysisType,
      confidence: var_confidence,
      anomalies: var_anomalies,
      metadata: var_metadata,
    );
  }

  @protected
  AuthenticationConfig sse_decode_authentication_config(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var var_elaQuality = sse_decode_u_8(deserializer);
    var var_copyMoveThreshold = sse_decode_f_32(deserializer);
    var var_blockSize = sse_decode_u_32(deserializer);
    var var_noiseSensitivity = sse_decode_f_32(deserializer);
    var var_analyzeMetadata = sse_decode_bool(deserializer);
    return AuthenticationConfig(
      elaQuality: var_elaQuality,
      copyMoveThreshold: var_copyMoveThreshold,
      blockSize: var_blockSize,
      noiseSensitivity: var_noiseSensitivity,
      analyzeMetadata: var_analyzeMetadata,
    );
  }

  @protected
  AuthenticationConfigBridge sse_decode_authentication_config_bridge(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var var_elaQuality = sse_decode_u_8(deserializer);
    var var_copyMoveThreshold = sse_decode_f_32(deserializer);
    var var_blockSize = sse_decode_u_32(deserializer);
    var var_noiseSensitivity = sse_decode_f_32(deserializer);
    var var_analyzeMetadata = sse_decode_bool(deserializer);
    return AuthenticationConfigBridge(
      elaQuality: var_elaQuality,
      copyMoveThreshold: var_copyMoveThreshold,
      blockSize: var_blockSize,
      noiseSensitivity: var_noiseSensitivity,
      analyzeMetadata: var_analyzeMetadata,
    );
  }

  @protected
  AuthenticationResultBridge sse_decode_authentication_result_bridge(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var var_authenticityScore = sse_decode_f_32(deserializer);
    var var_analyses = sse_decode_list_analysis_result_bridge(deserializer);
    var var_manipulationRegions = sse_decode_list_manipulation_region_bridge(deserializer);
    var var_processingTimeMs = sse_decode_u_64(deserializer);
    var var_analysisImages = sse_decode_list_analysis_image_bridge(deserializer);
    return AuthenticationResultBridge(
      authenticityScore: var_authenticityScore,
      analyses: var_analyses,
      manipulationRegions: var_manipulationRegions,
      processingTimeMs: var_processingTimeMs,
      analysisImages: var_analysisImages,
    );
  }

  @protected
  bool sse_decode_bool(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return deserializer.buffer.getUint8() != 0;
  }

  @protected
  AuthenticationConfigBridge sse_decode_box_autoadd_authentication_config_bridge(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return (sse_decode_authentication_config_bridge(deserializer));
  }

  @protected
  CollageEngineBridge sse_decode_box_autoadd_collage_engine_bridge(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return (sse_decode_collage_engine_bridge(deserializer));
  }

  @protected
  ImageAuthenticatorBridge sse_decode_box_autoadd_image_authenticator_bridge(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return (sse_decode_image_authenticator_bridge(deserializer));
  }

  @protected
  ImageCompressorBridge sse_decode_box_autoadd_image_compressor_bridge(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return (sse_decode_image_compressor_bridge(deserializer));
  }

  @protected
  ImageDataBridge sse_decode_box_autoadd_image_data_bridge(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return (sse_decode_image_data_bridge(deserializer));
  }

  @protected
  ImageProcessorBridge sse_decode_box_autoadd_image_processor_bridge(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return (sse_decode_image_processor_bridge(deserializer));
  }

  @protected
  int sse_decode_box_autoadd_u_32(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return (sse_decode_u_32(deserializer));
  }

  @protected
  int sse_decode_box_autoadd_u_8(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return (sse_decode_u_8(deserializer));
  }

  @protected
  CollageEngineBridge sse_decode_collage_engine_bridge(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return CollageEngineBridge.raw();
  }

  @protected
  CompressionEstimateBridge sse_decode_compression_estimate_bridge(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var var_estimatedSize = sse_decode_u_64(deserializer);
    var var_compressionRatio = sse_decode_f_32(deserializer);
    var var_spaceSaved = sse_decode_u_64(deserializer);
    return CompressionEstimateBridge(
      estimatedSize: var_estimatedSize,
      compressionRatio: var_compressionRatio,
      spaceSaved: var_spaceSaved,
    );
  }

  @protected
  CompressionResultBridge sse_decode_compression_result_bridge(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var var_imageData = sse_decode_image_data_bridge(deserializer);
    var var_originalSize = sse_decode_u_64(deserializer);
    var var_compressedSize = sse_decode_u_64(deserializer);
    var var_compressionRatio = sse_decode_f_32(deserializer);
    var var_spaceSaved = sse_decode_u_64(deserializer);
    var var_spaceSavedPercentage = sse_decode_f_32(deserializer);
    var var_finalQuality = sse_decode_u_8(deserializer);
    var var_iterations = sse_decode_u_32(deserializer);
    var var_targetAchieved = sse_decode_bool(deserializer);
    return CompressionResultBridge(
      imageData: var_imageData,
      originalSize: var_originalSize,
      compressedSize: var_compressedSize,
      compressionRatio: var_compressionRatio,
      spaceSaved: var_spaceSaved,
      spaceSavedPercentage: var_spaceSavedPercentage,
      finalQuality: var_finalQuality,
      iterations: var_iterations,
      targetAchieved: var_targetAchieved,
    );
  }

  @protected
  double sse_decode_f_32(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return deserializer.buffer.getFloat32();
  }

  @protected
  ImageAuthenticator sse_decode_image_authenticator(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var var_config = sse_decode_authentication_config(deserializer);
    return ImageAuthenticator(config: var_config);
  }

  @protected
  ImageAuthenticatorBridge sse_decode_image_authenticator_bridge(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var var_authenticator = sse_decode_image_authenticator(deserializer);
    return ImageAuthenticatorBridge.raw(authenticator: var_authenticator);
  }

  @protected
  ImageCompressorBridge sse_decode_image_compressor_bridge(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return ImageCompressorBridge.raw();
  }

  @protected
  ImageDataBridge sse_decode_image_data_bridge(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var var_width = sse_decode_u_32(deserializer);
    var var_height = sse_decode_u_32(deserializer);
    var var_format = sse_decode_String(deserializer);
    var var_colorSpace = sse_decode_String(deserializer);
    var var_data = sse_decode_list_prim_u_8_strict(deserializer);
    return ImageDataBridge(
      width: var_width,
      height: var_height,
      format: var_format,
      colorSpace: var_colorSpace,
      data: var_data,
    );
  }

  @protected
  ImageProcessorBridge sse_decode_image_processor_bridge(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return ImageProcessorBridge.raw();
  }

  @protected
  List<String> sse_decode_list_String(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    var len_ = sse_decode_i_32(deserializer);
    var ans_ = <String>[];
    for (var idx_ = 0; idx_ < len_; ++idx_) {
      ans_.add(sse_decode_String(deserializer));
    }
    return ans_;
  }

  @protected
  List<AnalysisImageBridge> sse_decode_list_analysis_image_bridge(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    var len_ = sse_decode_i_32(deserializer);
    var ans_ = <AnalysisImageBridge>[];
    for (var idx_ = 0; idx_ < len_; ++idx_) {
      ans_.add(sse_decode_analysis_image_bridge(deserializer));
    }
    return ans_;
  }

  @protected
  List<AnalysisResultBridge> sse_decode_list_analysis_result_bridge(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    var len_ = sse_decode_i_32(deserializer);
    var ans_ = <AnalysisResultBridge>[];
    for (var idx_ = 0; idx_ < len_; ++idx_) {
      ans_.add(sse_decode_analysis_result_bridge(deserializer));
    }
    return ans_;
  }

  @protected
  List<ImageDataBridge> sse_decode_list_image_data_bridge(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    var len_ = sse_decode_i_32(deserializer);
    var ans_ = <ImageDataBridge>[];
    for (var idx_ = 0; idx_ < len_; ++idx_) {
      ans_.add(sse_decode_image_data_bridge(deserializer));
    }
    return ans_;
  }

  @protected
  List<ManipulationRegionBridge> sse_decode_list_manipulation_region_bridge(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    var len_ = sse_decode_i_32(deserializer);
    var ans_ = <ManipulationRegionBridge>[];
    for (var idx_ = 0; idx_ < len_; ++idx_) {
      ans_.add(sse_decode_manipulation_region_bridge(deserializer));
    }
    return ans_;
  }

  @protected
  List<MetadataPairBridge> sse_decode_list_metadata_pair_bridge(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    var len_ = sse_decode_i_32(deserializer);
    var ans_ = <MetadataPairBridge>[];
    for (var idx_ = 0; idx_ < len_; ++idx_) {
      ans_.add(sse_decode_metadata_pair_bridge(deserializer));
    }
    return ans_;
  }

  @protected
  List<int> sse_decode_list_prim_u_8_loose(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var len_ = sse_decode_i_32(deserializer);
    return deserializer.buffer.getUint8List(len_);
  }

  @protected
  Uint8List sse_decode_list_prim_u_8_strict(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var len_ = sse_decode_i_32(deserializer);
    return deserializer.buffer.getUint8List(len_);
  }

  @protected
  List<RectBridge> sse_decode_list_rect_bridge(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    var len_ = sse_decode_i_32(deserializer);
    var ans_ = <RectBridge>[];
    for (var idx_ = 0; idx_ < len_; ++idx_) {
      ans_.add(sse_decode_rect_bridge(deserializer));
    }
    return ans_;
  }

  @protected
  ManipulationRegionBridge sse_decode_manipulation_region_bridge(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var var_x = sse_decode_u_32(deserializer);
    var var_y = sse_decode_u_32(deserializer);
    var var_width = sse_decode_u_32(deserializer);
    var var_height = sse_decode_u_32(deserializer);
    var var_confidence = sse_decode_f_32(deserializer);
    var var_manipulationType = sse_decode_String(deserializer);
    var var_details = sse_decode_String(deserializer);
    return ManipulationRegionBridge(
      x: var_x,
      y: var_y,
      width: var_width,
      height: var_height,
      confidence: var_confidence,
      manipulationType: var_manipulationType,
      details: var_details,
    );
  }

  @protected
  MetadataPairBridge sse_decode_metadata_pair_bridge(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var var_key = sse_decode_String(deserializer);
    var var_value = sse_decode_String(deserializer);
    return MetadataPairBridge(key: var_key, value: var_value);
  }

  @protected
  String? sse_decode_opt_String(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    if (sse_decode_bool(deserializer)) {
      return (sse_decode_String(deserializer));
    } else {
      return null;
    }
  }

  @protected
  int? sse_decode_opt_box_autoadd_u_32(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    if (sse_decode_bool(deserializer)) {
      return (sse_decode_box_autoadd_u_32(deserializer));
    } else {
      return null;
    }
  }

  @protected
  int? sse_decode_opt_box_autoadd_u_8(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    if (sse_decode_bool(deserializer)) {
      return (sse_decode_box_autoadd_u_8(deserializer));
    } else {
      return null;
    }
  }

  @protected
  RectBridge sse_decode_rect_bridge(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var var_x = sse_decode_u_32(deserializer);
    var var_y = sse_decode_u_32(deserializer);
    var var_width = sse_decode_u_32(deserializer);
    var var_height = sse_decode_u_32(deserializer);
    return RectBridge(x: var_x, y: var_y, width: var_width, height: var_height);
  }

  @protected
  int sse_decode_u_32(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return deserializer.buffer.getUint32();
  }

  @protected
  BigInt sse_decode_u_64(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return deserializer.buffer.getBigUint64();
  }

  @protected
  int sse_decode_u_8(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return deserializer.buffer.getUint8();
  }

  @protected
  void sse_decode_unit(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
  }

  @protected
  BigInt sse_decode_usize(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return deserializer.buffer.getBigUint64();
  }

  @protected
  int sse_decode_i_32(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return deserializer.buffer.getInt32();
  }

  @protected
  void sse_encode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFilterEngineBridge(
    FilterEngineBridge self,
    SseSerializer serializer,
  ) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_usize((self as FilterEngineBridgeImpl).frbInternalSseEncode(move: true), serializer);
  }

  @protected
  void sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFilterEngineBridge(
    FilterEngineBridge self,
    SseSerializer serializer,
  ) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_usize((self as FilterEngineBridgeImpl).frbInternalSseEncode(move: false), serializer);
  }

  @protected
  void sse_encode_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFilterEngineBridge(
    FilterEngineBridge self,
    SseSerializer serializer,
  ) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_usize((self as FilterEngineBridgeImpl).frbInternalSseEncode(move: null), serializer);
  }

  @protected
  void sse_encode_String(String self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_list_prim_u_8_strict(utf8.encoder.convert(self), serializer);
  }

  @protected
  void sse_encode_analysis_image_bridge(AnalysisImageBridge self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_String(self.name, serializer);
    sse_encode_image_data_bridge(self.imageData, serializer);
  }

  @protected
  void sse_encode_analysis_result_bridge(AnalysisResultBridge self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_String(self.analysisType, serializer);
    sse_encode_f_32(self.confidence, serializer);
    sse_encode_list_String(self.anomalies, serializer);
    sse_encode_list_metadata_pair_bridge(self.metadata, serializer);
  }

  @protected
  void sse_encode_authentication_config(AuthenticationConfig self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_u_8(self.elaQuality, serializer);
    sse_encode_f_32(self.copyMoveThreshold, serializer);
    sse_encode_u_32(self.blockSize, serializer);
    sse_encode_f_32(self.noiseSensitivity, serializer);
    sse_encode_bool(self.analyzeMetadata, serializer);
  }

  @protected
  void sse_encode_authentication_config_bridge(AuthenticationConfigBridge self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_u_8(self.elaQuality, serializer);
    sse_encode_f_32(self.copyMoveThreshold, serializer);
    sse_encode_u_32(self.blockSize, serializer);
    sse_encode_f_32(self.noiseSensitivity, serializer);
    sse_encode_bool(self.analyzeMetadata, serializer);
  }

  @protected
  void sse_encode_authentication_result_bridge(AuthenticationResultBridge self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_f_32(self.authenticityScore, serializer);
    sse_encode_list_analysis_result_bridge(self.analyses, serializer);
    sse_encode_list_manipulation_region_bridge(self.manipulationRegions, serializer);
    sse_encode_u_64(self.processingTimeMs, serializer);
    sse_encode_list_analysis_image_bridge(self.analysisImages, serializer);
  }

  @protected
  void sse_encode_bool(bool self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    serializer.buffer.putUint8(self ? 1 : 0);
  }

  @protected
  void sse_encode_box_autoadd_authentication_config_bridge(AuthenticationConfigBridge self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_authentication_config_bridge(self, serializer);
  }

  @protected
  void sse_encode_box_autoadd_collage_engine_bridge(CollageEngineBridge self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_collage_engine_bridge(self, serializer);
  }

  @protected
  void sse_encode_box_autoadd_image_authenticator_bridge(ImageAuthenticatorBridge self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_image_authenticator_bridge(self, serializer);
  }

  @protected
  void sse_encode_box_autoadd_image_compressor_bridge(ImageCompressorBridge self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_image_compressor_bridge(self, serializer);
  }

  @protected
  void sse_encode_box_autoadd_image_data_bridge(ImageDataBridge self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_image_data_bridge(self, serializer);
  }

  @protected
  void sse_encode_box_autoadd_image_processor_bridge(ImageProcessorBridge self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_image_processor_bridge(self, serializer);
  }

  @protected
  void sse_encode_box_autoadd_u_32(int self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_u_32(self, serializer);
  }

  @protected
  void sse_encode_box_autoadd_u_8(int self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_u_8(self, serializer);
  }

  @protected
  void sse_encode_collage_engine_bridge(CollageEngineBridge self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
  }

  @protected
  void sse_encode_compression_estimate_bridge(CompressionEstimateBridge self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_u_64(self.estimatedSize, serializer);
    sse_encode_f_32(self.compressionRatio, serializer);
    sse_encode_u_64(self.spaceSaved, serializer);
  }

  @protected
  void sse_encode_compression_result_bridge(CompressionResultBridge self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_image_data_bridge(self.imageData, serializer);
    sse_encode_u_64(self.originalSize, serializer);
    sse_encode_u_64(self.compressedSize, serializer);
    sse_encode_f_32(self.compressionRatio, serializer);
    sse_encode_u_64(self.spaceSaved, serializer);
    sse_encode_f_32(self.spaceSavedPercentage, serializer);
    sse_encode_u_8(self.finalQuality, serializer);
    sse_encode_u_32(self.iterations, serializer);
    sse_encode_bool(self.targetAchieved, serializer);
  }

  @protected
  void sse_encode_f_32(double self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    serializer.buffer.putFloat32(self);
  }

  @protected
  void sse_encode_image_authenticator(ImageAuthenticator self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_authentication_config(self.config, serializer);
  }

  @protected
  void sse_encode_image_authenticator_bridge(ImageAuthenticatorBridge self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_image_authenticator(self.authenticator, serializer);
  }

  @protected
  void sse_encode_image_compressor_bridge(ImageCompressorBridge self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
  }

  @protected
  void sse_encode_image_data_bridge(ImageDataBridge self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_u_32(self.width, serializer);
    sse_encode_u_32(self.height, serializer);
    sse_encode_String(self.format, serializer);
    sse_encode_String(self.colorSpace, serializer);
    sse_encode_list_prim_u_8_strict(self.data, serializer);
  }

  @protected
  void sse_encode_image_processor_bridge(ImageProcessorBridge self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
  }

  @protected
  void sse_encode_list_String(List<String> self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_i_32(self.length, serializer);
    for (final item in self) {
      sse_encode_String(item, serializer);
    }
  }

  @protected
  void sse_encode_list_analysis_image_bridge(List<AnalysisImageBridge> self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_i_32(self.length, serializer);
    for (final item in self) {
      sse_encode_analysis_image_bridge(item, serializer);
    }
  }

  @protected
  void sse_encode_list_analysis_result_bridge(List<AnalysisResultBridge> self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_i_32(self.length, serializer);
    for (final item in self) {
      sse_encode_analysis_result_bridge(item, serializer);
    }
  }

  @protected
  void sse_encode_list_image_data_bridge(List<ImageDataBridge> self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_i_32(self.length, serializer);
    for (final item in self) {
      sse_encode_image_data_bridge(item, serializer);
    }
  }

  @protected
  void sse_encode_list_manipulation_region_bridge(List<ManipulationRegionBridge> self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_i_32(self.length, serializer);
    for (final item in self) {
      sse_encode_manipulation_region_bridge(item, serializer);
    }
  }

  @protected
  void sse_encode_list_metadata_pair_bridge(List<MetadataPairBridge> self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_i_32(self.length, serializer);
    for (final item in self) {
      sse_encode_metadata_pair_bridge(item, serializer);
    }
  }

  @protected
  void sse_encode_list_prim_u_8_loose(List<int> self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_i_32(self.length, serializer);
    serializer.buffer.putUint8List(self is Uint8List ? self : Uint8List.fromList(self));
  }

  @protected
  void sse_encode_list_prim_u_8_strict(Uint8List self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_i_32(self.length, serializer);
    serializer.buffer.putUint8List(self);
  }

  @protected
  void sse_encode_list_rect_bridge(List<RectBridge> self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_i_32(self.length, serializer);
    for (final item in self) {
      sse_encode_rect_bridge(item, serializer);
    }
  }

  @protected
  void sse_encode_manipulation_region_bridge(ManipulationRegionBridge self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_u_32(self.x, serializer);
    sse_encode_u_32(self.y, serializer);
    sse_encode_u_32(self.width, serializer);
    sse_encode_u_32(self.height, serializer);
    sse_encode_f_32(self.confidence, serializer);
    sse_encode_String(self.manipulationType, serializer);
    sse_encode_String(self.details, serializer);
  }

  @protected
  void sse_encode_metadata_pair_bridge(MetadataPairBridge self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_String(self.key, serializer);
    sse_encode_String(self.value, serializer);
  }

  @protected
  void sse_encode_opt_String(String? self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    sse_encode_bool(self != null, serializer);
    if (self != null) {
      sse_encode_String(self, serializer);
    }
  }

  @protected
  void sse_encode_opt_box_autoadd_u_32(int? self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    sse_encode_bool(self != null, serializer);
    if (self != null) {
      sse_encode_box_autoadd_u_32(self, serializer);
    }
  }

  @protected
  void sse_encode_opt_box_autoadd_u_8(int? self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    sse_encode_bool(self != null, serializer);
    if (self != null) {
      sse_encode_box_autoadd_u_8(self, serializer);
    }
  }

  @protected
  void sse_encode_rect_bridge(RectBridge self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_u_32(self.x, serializer);
    sse_encode_u_32(self.y, serializer);
    sse_encode_u_32(self.width, serializer);
    sse_encode_u_32(self.height, serializer);
  }

  @protected
  void sse_encode_u_32(int self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    serializer.buffer.putUint32(self);
  }

  @protected
  void sse_encode_u_64(BigInt self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    serializer.buffer.putBigUint64(self);
  }

  @protected
  void sse_encode_u_8(int self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    serializer.buffer.putUint8(self);
  }

  @protected
  void sse_encode_unit(void self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
  }

  @protected
  void sse_encode_usize(BigInt self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    serializer.buffer.putBigUint64(self);
  }

  @protected
  void sse_encode_i_32(int self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    serializer.buffer.putInt32(self);
  }
}

@sealed
class FilterEngineBridgeImpl extends RustOpaque implements FilterEngineBridge {
  // Not to be used by end users
  FilterEngineBridgeImpl.frbInternalDcoDecode(List<dynamic> wire) : super.frbInternalDcoDecode(wire, _kStaticData);

  // Not to be used by end users
  FilterEngineBridgeImpl.frbInternalSseDecode(BigInt ptr, int externalSizeOnNative)
    : super.frbInternalSseDecode(ptr, externalSizeOnNative, _kStaticData);

  static final _kStaticData = RustArcStaticData(
    rustArcIncrementStrongCount: TushenCore.instance.api.rust_arc_increment_strong_count_FilterEngineBridge,
    rustArcDecrementStrongCount: TushenCore.instance.api.rust_arc_decrement_strong_count_FilterEngineBridge,
    rustArcDecrementStrongCountPtr: TushenCore.instance.api.rust_arc_decrement_strong_count_FilterEngineBridgePtr,
  );

  /// Apply filter to image
  Future<ImageDataBridge> applyFilter({
    required ImageDataBridge image,
    required String filterType,
    required double intensity,
  }) => TushenCore.instance.api.crateBridgeFilterEngineBridgeApplyFilter(
    that: this,
    image: image,
    filterType: filterType,
    intensity: intensity,
  );

  /// Get available filters
  List<String> getAvailableFilters() =>
      TushenCore.instance.api.crateBridgeFilterEngineBridgeGetAvailableFilters(that: this);

  /// Get filter preview
  Future<ImageDataBridge> getFilterPreview({
    required ImageDataBridge image,
    required String filterType,
    required int previewSize,
  }) => TushenCore.instance.api.crateBridgeFilterEngineBridgeGetFilterPreview(
    that: this,
    image: image,
    filterType: filterType,
    previewSize: previewSize,
  );
}
