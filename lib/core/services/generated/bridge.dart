// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.9.0.

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import 'core/image_authenticator.dart';
import 'frb_generated.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';

// These functions are ignored because they are not marked as `pub`: `color_space_to_string`, `image_format_to_string`, `parse_filter_type`, `string_to_color_space`, `string_to_image_format`
// These function are ignored because they are on traits that is not defined in current crate (put an empty `#[frb]` on it to unignore): `clone`, `clone`, `clone`, `clone`, `clone`, `clone`, `clone`, `clone`, `clone`, `from`, `from`, `from`, `from`

/// Initialize the TuShen core library
String initTushenCore() => TushenCore.instance.api.crateBridgeInitTushenCore();

/// Get library information
String getLibraryInfo() => TushenCore.instance.api.crateBridgeGetLibraryInfo();

// Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<FilterEngineBridge>>
abstract class FilterEngineBridge implements RustOpaqueInterface {
  /// Apply filter to image
  Future<ImageDataBridge> applyFilter({
    required ImageDataBridge image,
    required String filterType,
    required double intensity,
  });

  /// Get available filters
  List<String> getAvailableFilters();

  /// Get filter preview
  Future<ImageDataBridge> getFilterPreview({
    required ImageDataBridge image,
    required String filterType,
    required int previewSize,
  });

  /// Create a new filter engine
  factory FilterEngineBridge() => TushenCore.instance.api.crateBridgeFilterEngineBridgeNew();
}

/// Analysis image for FFI
class AnalysisImageBridge {
  final String name;
  final ImageDataBridge imageData;

  const AnalysisImageBridge({required this.name, required this.imageData});

  @override
  int get hashCode => name.hashCode ^ imageData.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AnalysisImageBridge &&
          runtimeType == other.runtimeType &&
          name == other.name &&
          imageData == other.imageData;
}

/// Individual analysis result for FFI
class AnalysisResultBridge {
  final String analysisType;
  final double confidence;
  final List<String> anomalies;
  final List<MetadataPairBridge> metadata;

  const AnalysisResultBridge({
    required this.analysisType,
    required this.confidence,
    required this.anomalies,
    required this.metadata,
  });

  @override
  int get hashCode => analysisType.hashCode ^ confidence.hashCode ^ anomalies.hashCode ^ metadata.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AnalysisResultBridge &&
          runtimeType == other.runtimeType &&
          analysisType == other.analysisType &&
          confidence == other.confidence &&
          anomalies == other.anomalies &&
          metadata == other.metadata;
}

/// Authentication configuration for FFI
class AuthenticationConfigBridge {
  final int elaQuality;
  final double copyMoveThreshold;
  final int blockSize;
  final double noiseSensitivity;
  final bool analyzeMetadata;

  const AuthenticationConfigBridge({
    required this.elaQuality,
    required this.copyMoveThreshold,
    required this.blockSize,
    required this.noiseSensitivity,
    required this.analyzeMetadata,
  });

  static Future<AuthenticationConfigBridge> default_() =>
      TushenCore.instance.api.crateBridgeAuthenticationConfigBridgeDefault();

  @override
  int get hashCode =>
      elaQuality.hashCode ^
      copyMoveThreshold.hashCode ^
      blockSize.hashCode ^
      noiseSensitivity.hashCode ^
      analyzeMetadata.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AuthenticationConfigBridge &&
          runtimeType == other.runtimeType &&
          elaQuality == other.elaQuality &&
          copyMoveThreshold == other.copyMoveThreshold &&
          blockSize == other.blockSize &&
          noiseSensitivity == other.noiseSensitivity &&
          analyzeMetadata == other.analyzeMetadata;
}

/// Authentication result for FFI
class AuthenticationResultBridge {
  final double authenticityScore;
  final List<AnalysisResultBridge> analyses;
  final List<ManipulationRegionBridge> manipulationRegions;
  final BigInt processingTimeMs;
  final List<AnalysisImageBridge> analysisImages;

  const AuthenticationResultBridge({
    required this.authenticityScore,
    required this.analyses,
    required this.manipulationRegions,
    required this.processingTimeMs,
    required this.analysisImages,
  });

  @override
  int get hashCode =>
      authenticityScore.hashCode ^
      analyses.hashCode ^
      manipulationRegions.hashCode ^
      processingTimeMs.hashCode ^
      analysisImages.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AuthenticationResultBridge &&
          runtimeType == other.runtimeType &&
          authenticityScore == other.authenticityScore &&
          analyses == other.analyses &&
          manipulationRegions == other.manipulationRegions &&
          processingTimeMs == other.processingTimeMs &&
          analysisImages == other.analysisImages;
}

/// Collage engine bridge
class CollageEngineBridge {
  const CollageEngineBridge.raw();

  /// Create collage from images
  Future<ImageDataBridge> createCollage({
    required List<ImageDataBridge> images,
    required String layout,
    required int canvasWidth,
    required int canvasHeight,
    required int spacing,
    String? backgroundColor,
  }) => TushenCore.instance.api.crateBridgeCollageEngineBridgeCreateCollage(
    that: this,
    images: images,
    layout: layout,
    canvasWidth: canvasWidth,
    canvasHeight: canvasHeight,
    spacing: spacing,
    backgroundColor: backgroundColor,
  );

  /// Get available layouts
  List<String> getAvailableLayouts() =>
      TushenCore.instance.api.crateBridgeCollageEngineBridgeGetAvailableLayouts(that: this);

  /// Get layout preview
  Future<List<RectBridge>> getLayoutPreview({
    required String layout,
    required int canvasWidth,
    required int canvasHeight,
  }) => TushenCore.instance.api.crateBridgeCollageEngineBridgeGetLayoutPreview(
    that: this,
    layout: layout,
    canvasWidth: canvasWidth,
    canvasHeight: canvasHeight,
  );

  /// Create a new collage engine
  factory CollageEngineBridge() => TushenCore.instance.api.crateBridgeCollageEngineBridgeNew();

  @override
  int get hashCode => 0;

  @override
  bool operator ==(Object other) =>
      identical(this, other) || other is CollageEngineBridge && runtimeType == other.runtimeType;
}

/// Compression estimate for FFI
class CompressionEstimateBridge {
  final BigInt estimatedSize;
  final double compressionRatio;
  final BigInt spaceSaved;

  const CompressionEstimateBridge({
    required this.estimatedSize,
    required this.compressionRatio,
    required this.spaceSaved,
  });

  @override
  int get hashCode => estimatedSize.hashCode ^ compressionRatio.hashCode ^ spaceSaved.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CompressionEstimateBridge &&
          runtimeType == other.runtimeType &&
          estimatedSize == other.estimatedSize &&
          compressionRatio == other.compressionRatio &&
          spaceSaved == other.spaceSaved;
}

/// Compression result for FFI
class CompressionResultBridge {
  final ImageDataBridge imageData;
  final BigInt originalSize;
  final BigInt compressedSize;
  final double compressionRatio;
  final BigInt spaceSaved;
  final double spaceSavedPercentage;
  final int finalQuality;
  final int iterations;
  final bool targetAchieved;

  const CompressionResultBridge({
    required this.imageData,
    required this.originalSize,
    required this.compressedSize,
    required this.compressionRatio,
    required this.spaceSaved,
    required this.spaceSavedPercentage,
    required this.finalQuality,
    required this.iterations,
    required this.targetAchieved,
  });

  @override
  int get hashCode =>
      imageData.hashCode ^
      originalSize.hashCode ^
      compressedSize.hashCode ^
      compressionRatio.hashCode ^
      spaceSaved.hashCode ^
      spaceSavedPercentage.hashCode ^
      finalQuality.hashCode ^
      iterations.hashCode ^
      targetAchieved.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CompressionResultBridge &&
          runtimeType == other.runtimeType &&
          imageData == other.imageData &&
          originalSize == other.originalSize &&
          compressedSize == other.compressedSize &&
          compressionRatio == other.compressionRatio &&
          spaceSaved == other.spaceSaved &&
          spaceSavedPercentage == other.spaceSavedPercentage &&
          finalQuality == other.finalQuality &&
          iterations == other.iterations &&
          targetAchieved == other.targetAchieved;
}

/// Image authentication bridge for FFI
class ImageAuthenticatorBridge {
  final ImageAuthenticator authenticator;

  const ImageAuthenticatorBridge.raw({required this.authenticator});

  /// Authenticate an image for tampering detection
  Future<AuthenticationResultBridge> authenticateImage({required ImageDataBridge imageData}) =>
      TushenCore.instance.api.crateBridgeImageAuthenticatorBridgeAuthenticateImage(that: this, imageData: imageData);

  /// Create a new image authenticator
  factory ImageAuthenticatorBridge() => TushenCore.instance.api.crateBridgeImageAuthenticatorBridgeNew();

  /// Create authenticator with custom configuration
  static ImageAuthenticatorBridge withConfig({required AuthenticationConfigBridge config}) =>
      TushenCore.instance.api.crateBridgeImageAuthenticatorBridgeWithConfig(config: config);

  @override
  int get hashCode => authenticator.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ImageAuthenticatorBridge && runtimeType == other.runtimeType && authenticator == other.authenticator;
}

/// Image compression bridge for FFI
class ImageCompressorBridge {
  const ImageCompressorBridge.raw();

  /// Compress an image with the given options
  Future<CompressionResultBridge> compressImage({
    required ImageDataBridge image,
    required String format,
    required int quality,
    int? targetSizeKb,
    int? resizeWidth,
    int? resizeHeight,
    required bool maintainAspectRatio,
  }) => TushenCore.instance.api.crateBridgeImageCompressorBridgeCompressImage(
    that: this,
    image: image,
    format: format,
    quality: quality,
    targetSizeKb: targetSizeKb,
    resizeWidth: resizeWidth,
    resizeHeight: resizeHeight,
    maintainAspectRatio: maintainAspectRatio,
  );

  /// Estimate compression result without actually compressing
  Future<CompressionEstimateBridge> estimateCompression({
    required ImageDataBridge image,
    required String format,
    required int quality,
  }) => TushenCore.instance.api.crateBridgeImageCompressorBridgeEstimateCompression(
    that: this,
    image: image,
    format: format,
    quality: quality,
  );

  /// Get supported compression formats
  List<String> getSupportedFormats() =>
      TushenCore.instance.api.crateBridgeImageCompressorBridgeGetSupportedFormats(that: this);

  /// Create a new image compressor
  factory ImageCompressorBridge() => TushenCore.instance.api.crateBridgeImageCompressorBridgeNew();

  @override
  int get hashCode => 0;

  @override
  bool operator ==(Object other) =>
      identical(this, other) || other is ImageCompressorBridge && runtimeType == other.runtimeType;
}

/// Image data bridge structure
class ImageDataBridge {
  final int width;
  final int height;
  final String format;
  final String colorSpace;
  final Uint8List data;

  const ImageDataBridge({
    required this.width,
    required this.height,
    required this.format,
    required this.colorSpace,
    required this.data,
  });

  @override
  int get hashCode => width.hashCode ^ height.hashCode ^ format.hashCode ^ colorSpace.hashCode ^ data.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ImageDataBridge &&
          runtimeType == other.runtimeType &&
          width == other.width &&
          height == other.height &&
          format == other.format &&
          colorSpace == other.colorSpace &&
          data == other.data;
}

/// Image processing bridge
class ImageProcessorBridge {
  const ImageProcessorBridge.raw();

  /// Crop image
  Future<ImageDataBridge> cropImage({
    required ImageDataBridge image,
    required int x,
    required int y,
    required int width,
    required int height,
  }) => TushenCore.instance.api.crateBridgeImageProcessorBridgeCropImage(
    that: this,
    image: image,
    x: x,
    y: y,
    width: width,
    height: height,
  );

  /// Load image from bytes
  Future<ImageDataBridge> loadImageFromBytes({required List<int> bytes}) =>
      TushenCore.instance.api.crateBridgeImageProcessorBridgeLoadImageFromBytes(that: this, bytes: bytes);

  /// Load image from file path
  Future<ImageDataBridge> loadImageFromPath({required String path}) =>
      TushenCore.instance.api.crateBridgeImageProcessorBridgeLoadImageFromPath(that: this, path: path);

  /// Create a new image processor
  factory ImageProcessorBridge() => TushenCore.instance.api.crateBridgeImageProcessorBridgeNew();

  /// Resize image
  Future<ImageDataBridge> resizeImage({
    required ImageDataBridge image,
    required int width,
    required int height,
    required bool maintainAspectRatio,
  }) => TushenCore.instance.api.crateBridgeImageProcessorBridgeResizeImage(
    that: this,
    image: image,
    width: width,
    height: height,
    maintainAspectRatio: maintainAspectRatio,
  );

  /// Rotate image
  Future<ImageDataBridge> rotateImage({required ImageDataBridge image, required double angle}) =>
      TushenCore.instance.api.crateBridgeImageProcessorBridgeRotateImage(that: this, image: image, angle: angle);

  /// Save image to bytes
  Future<Uint8List> saveImageToBytes({required ImageDataBridge image, required String format, int? quality}) =>
      TushenCore.instance.api.crateBridgeImageProcessorBridgeSaveImageToBytes(
        that: this,
        image: image,
        format: format,
        quality: quality,
      );

  @override
  int get hashCode => 0;

  @override
  bool operator ==(Object other) =>
      identical(this, other) || other is ImageProcessorBridge && runtimeType == other.runtimeType;
}

/// Manipulation region for FFI
class ManipulationRegionBridge {
  final int x;
  final int y;
  final int width;
  final int height;
  final double confidence;
  final String manipulationType;
  final String details;

  const ManipulationRegionBridge({
    required this.x,
    required this.y,
    required this.width,
    required this.height,
    required this.confidence,
    required this.manipulationType,
    required this.details,
  });

  @override
  int get hashCode =>
      x.hashCode ^
      y.hashCode ^
      width.hashCode ^
      height.hashCode ^
      confidence.hashCode ^
      manipulationType.hashCode ^
      details.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ManipulationRegionBridge &&
          runtimeType == other.runtimeType &&
          x == other.x &&
          y == other.y &&
          width == other.width &&
          height == other.height &&
          confidence == other.confidence &&
          manipulationType == other.manipulationType &&
          details == other.details;
}

/// Metadata pair for FFI
class MetadataPairBridge {
  final String key;
  final String value;

  const MetadataPairBridge({required this.key, required this.value});

  @override
  int get hashCode => key.hashCode ^ value.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MetadataPairBridge && runtimeType == other.runtimeType && key == other.key && value == other.value;
}

/// Rectangle bridge structure
class RectBridge {
  final int x;
  final int y;
  final int width;
  final int height;

  const RectBridge({required this.x, required this.y, required this.width, required this.height});

  @override
  int get hashCode => x.hashCode ^ y.hashCode ^ width.hashCode ^ height.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RectBridge &&
          runtimeType == other.runtimeType &&
          x == other.x &&
          y == other.y &&
          width == other.width &&
          height == other.height;
}
