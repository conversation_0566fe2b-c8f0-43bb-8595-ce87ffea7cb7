import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'rust_bridge_service.dart';
import 'image_processor_service.dart';
import 'collage_service.dart';

/// Service for testing FFI integration with Rust backend
class FFITestService {
  static FFITestService? _instance;
  static FFITestService get instance => _instance ??= FFITestService._();
  
  FFITestService._();
  
  final RustBridgeService _rustBridge = RustBridgeService.instance;
  final ImageProcessorService _imageProcessor = ImageProcessorService.instance;
  final CollageService _collageService = CollageService.instance;
  
  /// Test basic FFI connectivity
  Future<bool> testFFIConnectivity() async {
    try {
      debugPrint('Testing FFI connectivity...');
      
      // Test if Rust bridge is initialized
      if (!_rustBridge.isInitialized) {
        debugPrint('Rust bridge not initialized');
        return false;
      }
      
      debugPrint('Rust bridge initialized successfully');
      return true;
    } catch (e) {
      debugPrint('FFI connectivity test failed: $e');
      return false;
    }
  }
  
  /// Test image processing functionality
  Future<bool> testImageProcessing() async {
    try {
      debugPrint('Testing image processing...');
      
      // Create a mock image
      final mockImageData = Uint8List.fromList(List.generate(1920 * 1080 * 4, (i) => i % 256));
      final mockImage = ImageDataBridge(
        data: mockImageData,
        width: 1920,
        height: 1080,
        format: ImageFormat.png.rustName,
        colorSpace: ColorSpace.srgb.rustName,
      );
      
      // Test filter application
      final filteredImage = await _imageProcessor.applyFilter(
        image: mockImage,
        filterType: FilterType.blur,
        intensity: 0.5,
      );
      
      debugPrint('Filter applied successfully: ${filteredImage.width}x${filteredImage.height}');
      
      // Test image resizing
      final resizedImage = await _imageProcessor.resizeImage(
        image: mockImage,
        width: 800,
        height: 600,
      );
      
      debugPrint('Image resized successfully: ${resizedImage.width}x${resizedImage.height}');
      
      // Test image rotation
      final rotatedImage = await _imageProcessor.rotateImage(
        image: mockImage,
        angle: 90.0,
      );
      
      debugPrint('Image rotated successfully: ${rotatedImage.width}x${rotatedImage.height}');
      
      return true;
    } catch (e) {
      debugPrint('Image processing test failed: $e');
      return false;
    }
  }
  
  /// Test collage functionality
  Future<bool> testCollageCreation() async {
    try {
      debugPrint('Testing collage creation...');
      
      // Create mock images
      final mockImages = List.generate(4, (index) {
        final mockData = Uint8List.fromList(List.generate(500 * 500 * 4, (i) => (i + index * 50) % 256));
        return ImageDataBridge(
          data: mockData,
          width: 500,
          height: 500,
          format: ImageFormat.png.rustName,
          colorSpace: ColorSpace.srgb.rustName,
        );
      });
      
      // Get available templates
      final templates = _collageService.getAvailableTemplates();
      debugPrint('Available templates: ${templates.length}');
      
      if (templates.isEmpty) {
        debugPrint('No collage templates available');
        return false;
      }
      
      // Test collage creation with 2x2 grid
      final template = templates.firstWhere(
        (t) => t.layout == CollageLayout.grid2x2,
        orElse: () => templates.first,
      );
      
      final collage = await _collageService.createCollage(
        images: mockImages,
        template: template,
        options: const CollageOptions(
          canvasWidth: 1920,
          canvasHeight: 1080,
          spacing: 20,
          backgroundColor: '#FFFFFF',
        ),
      );
      
      debugPrint('Collage created successfully: ${collage.width}x${collage.height}');
      
      // Test template categories
      final categories = _collageService.getTemplatesByCategory();
      debugPrint('Template categories: ${categories.keys.join(', ')}');
      
      return true;
    } catch (e) {
      debugPrint('Collage creation test failed: $e');
      return false;
    }
  }
  
  /// Test filter categories and availability
  Future<bool> testFilterSystem() async {
    try {
      debugPrint('Testing filter system...');
      
      // Test available filters
      final filters = _imageProcessor.getAvailableFilters();
      debugPrint('Available filters: ${filters.length}');
      
      // Test filter categories
      final categories = _imageProcessor.getFilterCategories();
      debugPrint('Filter categories: ${categories.keys.join(', ')}');
      
      // Test each category has filters
      for (final category in categories.entries) {
        if (category.value.isEmpty) {
          debugPrint('Category ${category.key} has no filters');
          return false;
        }
        debugPrint('${category.key}: ${category.value.length} filters');
      }
      
      return true;
    } catch (e) {
      debugPrint('Filter system test failed: $e');
      return false;
    }
  }
  
  /// Run comprehensive FFI tests
  Future<Map<String, bool>> runAllTests() async {
    final results = <String, bool>{};
    
    debugPrint('Starting comprehensive FFI tests...');
    
    // Test FFI connectivity
    results['connectivity'] = await testFFIConnectivity();
    
    // Test image processing
    results['imageProcessing'] = await testImageProcessing();
    
    // Test collage creation
    results['collageCreation'] = await testCollageCreation();
    
    // Test filter system
    results['filterSystem'] = await testFilterSystem();
    
    // Summary
    final passedTests = results.values.where((result) => result).length;
    final totalTests = results.length;
    
    debugPrint('FFI Tests completed: $passedTests/$totalTests passed');
    
    if (passedTests == totalTests) {
      debugPrint('🎉 All FFI tests passed!');
    } else {
      debugPrint('⚠️ Some FFI tests failed:');
      results.forEach((test, result) {
        if (!result) {
          debugPrint('  ❌ $test');
        } else {
          debugPrint('  ✅ $test');
        }
      });
    }
    
    return results;
  }
  
  /// Get test status summary
  String getTestSummary(Map<String, bool> results) {
    final passedTests = results.values.where((result) => result).length;
    final totalTests = results.length;
    
    if (passedTests == totalTests) {
      return '🎉 All $totalTests FFI tests passed!';
    } else {
      return '⚠️ $passedTests/$totalTests FFI tests passed';
    }
  }
  
  /// Check if all services are ready
  bool get allServicesReady {
    return _rustBridge.isInitialized &&
           _imageProcessor.isReady &&
           _collageService.isReady;
  }
}
