import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'rust_bridge_service.dart';
import 'generated/bridge.dart';

/// Collage layout types
enum CollageLayout {
  grid2x2,
  grid3x3,
  grid2x3,
  grid3x2,
  horizontal2,
  horizontal3,
  vertical2,
  vertical3,
  freeform,
  magazine,
  polaroid,
  filmStrip,
  heart,
  circle,
  custom,
}

/// Collage template configuration
class CollageTemplate {
  final CollageLayout layout;
  final String name;
  final String description;
  final List<RectBridge> regions;
  final int maxImages;
  final double aspectRatio;
  final String previewUrl;
  
  const CollageTemplate({
    required this.layout,
    required this.name,
    required this.description,
    required this.regions,
    required this.maxImages,
    required this.aspectRatio,
    required this.previewUrl,
  });
  
  Map<String, dynamic> toJson() => {
    'layout': layout.name,
    'name': name,
    'description': description,
    'regions': regions
        .map((r) => {'x': r.x, 'y': r.y, 'width': r.width, 'height': r.height})
        .toList(),
    'maxImages': maxImages,
    'aspectRatio': aspectRatio,
    'previewUrl': previewUrl,
  };
}

/// Collage configuration options
class CollageOptions {
  final int canvasWidth;
  final int canvasHeight;
  final int spacing;
  final String backgroundColor;
  final double borderRadius;
  final int borderWidth;
  final String borderColor;
  final bool dropShadow;
  final double shadowOpacity;
  final int shadowBlur;
  final int shadowOffsetX;
  final int shadowOffsetY;
  
  const CollageOptions({
    this.canvasWidth = 1920,
    this.canvasHeight = 1080,
    this.spacing = 10,
    this.backgroundColor = '#FFFFFF',
    this.borderRadius = 0.0,
    this.borderWidth = 0,
    this.borderColor = '#000000',
    this.dropShadow = false,
    this.shadowOpacity = 0.3,
    this.shadowBlur = 10,
    this.shadowOffsetX = 5,
    this.shadowOffsetY = 5,
  });
  
  Map<String, dynamic> toJson() => {
    'canvasWidth': canvasWidth,
    'canvasHeight': canvasHeight,
    'spacing': spacing,
    'backgroundColor': backgroundColor,
    'borderRadius': borderRadius,
    'borderWidth': borderWidth,
    'borderColor': borderColor,
    'dropShadow': dropShadow,
    'shadowOpacity': shadowOpacity,
    'shadowBlur': shadowBlur,
    'shadowOffsetX': shadowOffsetX,
    'shadowOffsetY': shadowOffsetY,
  };
}

/// Service for creating collages using Rust backend
class CollageService {
  static CollageService? _instance;
  static CollageService get instance => _instance ??= CollageService._();
  
  CollageService._();
  
  final RustBridgeService _rustBridge = RustBridgeService.instance;
  
  /// Initialize the collage service
  Future<void> initialize() async {
    await _rustBridge.initialize();
  }
  
  /// Create a collage from multiple images
  Future<ImageDataBridge> createCollage({
    required List<ImageDataBridge> images,
    required CollageTemplate template,
    CollageOptions? options,
  }) async {
    try {
      final collageOptions = options ?? const CollageOptions();
      
      // Validate inputs
      if (images.isEmpty) {
        throw ArgumentError('At least one image is required');
      }
      
      if (images.length > template.maxImages) {
        throw ArgumentError('Too many images for this template (max: ${template.maxImages})');
      }
      
      // Mock processing time based on number of images
      await Future.delayed(Duration(milliseconds: 200 * images.length));
      
      debugPrint('Creating collage with ${images.length} images using ${template.name} template');
      
      // Mock implementation - in reality this would call Rust functions
      return ImageDataBridge(
        data: Uint8List(collageOptions.canvasWidth * collageOptions.canvasHeight * 4), // Mock RGBA data
        width: collageOptions.canvasWidth,
        height: collageOptions.canvasHeight,
        format: ImageFormat.png.rustName,
        colorSpace: ColorSpace.srgb.rustName,
      );
    } catch (e) {
      throw Exception('Failed to create collage: $e');
    }
  }
  
  /// Get available collage templates
  List<CollageTemplate> getAvailableTemplates() {
    return [
      // Grid layouts
      CollageTemplate(
        layout: CollageLayout.grid2x2,
        name: '2×2 Grid',
        description: 'Classic 2x2 grid layout',
        regions: [
          const RectBridge(x: 0, y: 0, width: 50, height: 50),
          const RectBridge(x: 50, y: 0, width: 50, height: 50),
          const RectBridge(x: 0, y: 50, width: 50, height: 50),
          const RectBridge(x: 50, y: 50, width: 50, height: 50),
        ],
        maxImages: 4,
        aspectRatio: 1.0,
        previewUrl: 'assets/templates/grid_2x2.png',
      ),
      
      CollageTemplate(
        layout: CollageLayout.grid3x3,
        name: '3×3 Grid',
        description: 'Classic 3x3 grid layout',
        regions: List.generate(9, (index) {
          final row = index ~/ 3;
          final col = index % 3;
          return RectBridge(
            x: col * 33,
            y: row * 33,
            width: 33,
            height: 33,
          );
        }),
        maxImages: 9,
        aspectRatio: 1.0,
        previewUrl: 'assets/templates/grid_3x3.png',
      ),
      
      // Horizontal layouts
      CollageTemplate(
        layout: CollageLayout.horizontal2,
        name: 'Horizontal 2',
        description: 'Two images side by side',
        regions: [
          const RectBridge(x: 0, y: 0, width: 50, height: 100),
          const RectBridge(x: 50, y: 0, width: 50, height: 100),
        ],
        maxImages: 2,
        aspectRatio: 2.0,
        previewUrl: 'assets/templates/horizontal_2.png',
      ),
      
      CollageTemplate(
        layout: CollageLayout.horizontal3,
        name: 'Horizontal 3',
        description: 'Three images side by side',
        regions: [
          const RectBridge(x: 0, y: 0, width: 33, height: 100),
          const RectBridge(x: 33, y: 0, width: 33, height: 100),
          const RectBridge(x: 66, y: 0, width: 34, height: 100),
        ],
        maxImages: 3,
        aspectRatio: 3.0,
        previewUrl: 'assets/templates/horizontal_3.png',
      ),
      
      // Vertical layouts
      CollageTemplate(
        layout: CollageLayout.vertical2,
        name: 'Vertical 2',
        description: 'Two images stacked vertically',
        regions: [
          const RectBridge(x: 0, y: 0, width: 100, height: 50),
          const RectBridge(x: 0, y: 50, width: 100, height: 50),
        ],
        maxImages: 2,
        aspectRatio: 0.5,
        previewUrl: 'assets/templates/vertical_2.png',
      ),
      
      // Special layouts
      CollageTemplate(
        layout: CollageLayout.magazine,
        name: 'Magazine',
        description: 'Magazine-style asymmetric layout',
        regions: [
          const RectBridge(x: 0, y: 0, width: 60, height: 60),
          const RectBridge(x: 60, y: 0, width: 40, height: 30),
          const RectBridge(x: 60, y: 30, width: 40, height: 30),
          const RectBridge(x: 0, y: 60, width: 100, height: 40),
        ],
        maxImages: 4,
        aspectRatio: 1.0,
        previewUrl: 'assets/templates/magazine.png',
      ),
      
      CollageTemplate(
        layout: CollageLayout.polaroid,
        name: 'Polaroid',
        description: 'Polaroid-style scattered layout',
        regions: [
          const RectBridge(x: 10, y: 15, width: 35, height: 40),
          const RectBridge(x: 55, y: 10, width: 35, height: 40),
          const RectBridge(x: 20, y: 55, width: 35, height: 40),
        ],
        maxImages: 3,
        aspectRatio: 1.0,
        previewUrl: 'assets/templates/polaroid.png',
      ),
    ];
  }
  
  /// Get templates by category
  Map<String, List<CollageTemplate>> getTemplatesByCategory() {
    final templates = getAvailableTemplates();
    
    return {
      'Grid': templates.where((t) => 
        t.layout == CollageLayout.grid2x2 || 
        t.layout == CollageLayout.grid3x3 ||
        t.layout == CollageLayout.grid2x3 ||
        t.layout == CollageLayout.grid3x2
      ).toList(),
      
      'Linear': templates.where((t) => 
        t.layout == CollageLayout.horizontal2 || 
        t.layout == CollageLayout.horizontal3 ||
        t.layout == CollageLayout.vertical2 ||
        t.layout == CollageLayout.vertical3
      ).toList(),
      
      'Creative': templates.where((t) => 
        t.layout == CollageLayout.magazine || 
        t.layout == CollageLayout.polaroid ||
        t.layout == CollageLayout.filmStrip ||
        t.layout == CollageLayout.freeform
      ).toList(),
      
      'Shapes': templates.where((t) => 
        t.layout == CollageLayout.heart || 
        t.layout == CollageLayout.circle
      ).toList(),
    };
  }
  
  /// Get layout preview regions
  Future<List<RectBridge>> getLayoutPreview({
    required CollageLayout layout,
    required int canvasWidth,
    required int canvasHeight,
  }) async {
    try {
      await Future.delayed(const Duration(milliseconds: 50));
      
      final template = getAvailableTemplates().firstWhere(
        (t) => t.layout == layout,
        orElse: () => getAvailableTemplates().first,
      );
      
      // Scale regions to actual canvas size
      return template.regions.map((region) => RectBridge(
        x: (region.x * canvasWidth / 100).round(),
        y: (region.y * canvasHeight / 100).round(),
        width: (region.width * canvasWidth / 100).round(),
        height: (region.height * canvasHeight / 100).round(),
      )).toList();
    } catch (e) {
      throw Exception('Failed to get layout preview: $e');
    }
  }
  
  /// Check if the service is ready
  bool get isReady => _rustBridge.isInitialized;
}
