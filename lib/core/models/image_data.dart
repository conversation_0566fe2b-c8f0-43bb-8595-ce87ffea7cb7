import 'dart:io';

/// Image data model for handling image information
class ImageData {
  final String id;
  final String path;
  final String name;
  final int width;
  final int height;
  final int fileSize;
  final DateTime createdAt;
  final File file;

  const ImageData({
    required this.id,
    required this.path,
    required this.name,
    required this.width,
    required this.height,
    required this.fileSize,
    required this.createdAt,
    required this.file,
  });

  /// Create ImageData from file path
  static Future<ImageData> fromFile(File file) async {
    final stat = await file.stat();
    final name = file.path.split('/').last;
    
    return ImageData(
      id: file.path.hashCode.toString(),
      path: file.path,
      name: name,
      width: 0, // Will be determined when image is loaded
      height: 0, // Will be determined when image is loaded
      fileSize: stat.size,
      createdAt: stat.modified,
      file: file,
    );
  }

  /// Copy with new properties
  ImageData copyWith({
    String? id,
    String? path,
    String? name,
    int? width,
    int? height,
    int? fileSize,
    DateTime? createdAt,
    File? file,
  }) {
    return ImageData(
      id: id ?? this.id,
      path: path ?? this.path,
      name: name ?? this.name,
      width: width ?? this.width,
      height: height ?? this.height,
      fileSize: fileSize ?? this.fileSize,
      createdAt: createdAt ?? this.createdAt,
      file: file ?? this.file,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ImageData && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ImageData(id: $id, name: $name, path: $path, size: ${width}x$height)';
  }
}
