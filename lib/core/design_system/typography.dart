import 'package:flutter/material.dart';
import 'colors.dart';

/// TuShen Design System - Typography
/// 
/// Defines the typography system for the TuShen app, including font families,
/// text styles, and responsive text scaling.

class TuShenTypography {
  TuShenTypography._();

  // Font Families
  static const String primaryFontFamily = 'SF Pro Display'; // iOS style
  static const String secondaryFontFamily = 'Roboto'; // Android style
  static const String monospaceFontFamily = 'SF Mono'; // Monospace
  static const String displayFontFamily = 'SF Pro Display'; // For large text
  
  // Font Weights
  static const FontWeight thin = FontWeight.w100;
  static const FontWeight extraLight = FontWeight.w200;
  static const FontWeight light = FontWeight.w300;
  static const FontWeight regular = FontWeight.w400;
  static const FontWeight medium = FontWeight.w500;
  static const FontWeight semiBold = FontWeight.w600;
  static const FontWeight bold = FontWeight.w700;
  static const FontWeight extraBold = FontWeight.w800;
  static const FontWeight black = FontWeight.w900;
  
  // Base Text Styles
  
  /// Display Large - For hero text and major headings
  static const TextStyle displayLarge = TextStyle(
    fontFamily: displayFontFamily,
    fontSize: 57,
    fontWeight: regular,
    letterSpacing: -0.25,
    height: 1.12,
  );
  
  /// Display Medium - For large headings
  static const TextStyle displayMedium = TextStyle(
    fontFamily: displayFontFamily,
    fontSize: 45,
    fontWeight: regular,
    letterSpacing: 0,
    height: 1.16,
  );
  
  /// Display Small - For medium headings
  static const TextStyle displaySmall = TextStyle(
    fontFamily: displayFontFamily,
    fontSize: 36,
    fontWeight: regular,
    letterSpacing: 0,
    height: 1.22,
  );
  
  /// Headline Large - For section headings
  static const TextStyle headlineLarge = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 32,
    fontWeight: regular,
    letterSpacing: 0,
    height: 1.25,
  );
  
  /// Headline Medium - For subsection headings
  static const TextStyle headlineMedium = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 28,
    fontWeight: regular,
    letterSpacing: 0,
    height: 1.29,
  );
  
  /// Headline Small - For small headings
  static const TextStyle headlineSmall = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 24,
    fontWeight: regular,
    letterSpacing: 0,
    height: 1.33,
  );
  
  /// Title Large - For card titles and important text
  static const TextStyle titleLarge = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 22,
    fontWeight: regular,
    letterSpacing: 0,
    height: 1.27,
  );
  
  /// Title Medium - For dialog titles and section labels
  static const TextStyle titleMedium = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 16,
    fontWeight: medium,
    letterSpacing: 0.15,
    height: 1.50,
  );
  
  /// Title Small - For small titles and labels
  static const TextStyle titleSmall = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 14,
    fontWeight: medium,
    letterSpacing: 0.1,
    height: 1.43,
  );
  
  /// Label Large - For prominent buttons and tabs
  static const TextStyle labelLarge = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 14,
    fontWeight: medium,
    letterSpacing: 0.1,
    height: 1.43,
  );
  
  /// Label Medium - For standard buttons and form labels
  static const TextStyle labelMedium = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 12,
    fontWeight: medium,
    letterSpacing: 0.5,
    height: 1.33,
  );
  
  /// Label Small - For small buttons and captions
  static const TextStyle labelSmall = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 11,
    fontWeight: medium,
    letterSpacing: 0.5,
    height: 1.45,
  );
  
  /// Body Large - For main content text
  static const TextStyle bodyLarge = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 16,
    fontWeight: regular,
    letterSpacing: 0.5,
    height: 1.50,
  );
  
  /// Body Medium - For secondary content text
  static const TextStyle bodyMedium = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 14,
    fontWeight: regular,
    letterSpacing: 0.25,
    height: 1.43,
  );
  
  /// Body Small - For captions and small text
  static const TextStyle bodySmall = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 12,
    fontWeight: regular,
    letterSpacing: 0.4,
    height: 1.33,
  );
  
  // Specialized Text Styles
  
  /// Button Text - For button labels
  static const TextStyle button = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 14,
    fontWeight: medium,
    letterSpacing: 1.25,
    height: 1.43,
  );
  
  /// Caption - For image captions and metadata
  static const TextStyle caption = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 12,
    fontWeight: regular,
    letterSpacing: 0.4,
    height: 1.33,
  );
  
  /// Overline - For category labels and overlines
  static const TextStyle overline = TextStyle(
    fontFamily: primaryFontFamily,
    fontSize: 10,
    fontWeight: regular,
    letterSpacing: 1.5,
    height: 1.6,
  );
  
  /// Monospace - For code and technical text
  static const TextStyle monospace = TextStyle(
    fontFamily: monospaceFontFamily,
    fontSize: 14,
    fontWeight: regular,
    letterSpacing: 0,
    height: 1.43,
  );
  
  // Colored Text Styles
  
  /// Primary text color variants
  static TextStyle get displayLargePrimary => displayLarge.copyWith(color: TuShenColors.textPrimary);
  static TextStyle get displayMediumPrimary => displayMedium.copyWith(color: TuShenColors.textPrimary);
  static TextStyle get displaySmallPrimary => displaySmall.copyWith(color: TuShenColors.textPrimary);
  
  /// Secondary text color variants
  static TextStyle get bodyLargeSecondary => bodyLarge.copyWith(color: TuShenColors.textSecondary);
  static TextStyle get bodyMediumSecondary => bodyMedium.copyWith(color: TuShenColors.textSecondary);
  static TextStyle get bodySmallSecondary => bodySmall.copyWith(color: TuShenColors.textSecondary);
  
  /// Accent color variants
  static TextStyle get titleLargeAccent => titleLarge.copyWith(color: TuShenColors.accent);
  static TextStyle get titleMediumAccent => titleMedium.copyWith(color: TuShenColors.accent);
  static TextStyle get labelLargeAccent => labelLarge.copyWith(color: TuShenColors.accent);
  
  /// Error color variants
  static TextStyle get bodyMediumError => bodyMedium.copyWith(color: TuShenColors.error);
  static TextStyle get labelMediumError => labelMedium.copyWith(color: TuShenColors.error);
  
  /// Success color variants
  static TextStyle get bodyMediumSuccess => bodyMedium.copyWith(color: TuShenColors.success);
  static TextStyle get labelMediumSuccess => labelMedium.copyWith(color: TuShenColors.success);
  
  /// Warning color variants
  static TextStyle get bodyMediumWarning => bodyMedium.copyWith(color: TuShenColors.warning);
  static TextStyle get labelMediumWarning => labelMedium.copyWith(color: TuShenColors.warning);
}

/// Text theme for Material Design
class TuShenTextTheme {
  TuShenTextTheme._();
  
  /// Light text theme
  static TextTheme get lightTextTheme => TextTheme(
    displayLarge: TuShenTypography.displayLarge.copyWith(color: TuShenColors.textPrimary),
    displayMedium: TuShenTypography.displayMedium.copyWith(color: TuShenColors.textPrimary),
    displaySmall: TuShenTypography.displaySmall.copyWith(color: TuShenColors.textPrimary),
    headlineLarge: TuShenTypography.headlineLarge.copyWith(color: TuShenColors.textPrimary),
    headlineMedium: TuShenTypography.headlineMedium.copyWith(color: TuShenColors.textPrimary),
    headlineSmall: TuShenTypography.headlineSmall.copyWith(color: TuShenColors.textPrimary),
    titleLarge: TuShenTypography.titleLarge.copyWith(color: TuShenColors.textPrimary),
    titleMedium: TuShenTypography.titleMedium.copyWith(color: TuShenColors.textPrimary),
    titleSmall: TuShenTypography.titleSmall.copyWith(color: TuShenColors.textPrimary),
    labelLarge: TuShenTypography.labelLarge.copyWith(color: TuShenColors.textPrimary),
    labelMedium: TuShenTypography.labelMedium.copyWith(color: TuShenColors.textSecondary),
    labelSmall: TuShenTypography.labelSmall.copyWith(color: TuShenColors.textSecondary),
    bodyLarge: TuShenTypography.bodyLarge.copyWith(color: TuShenColors.textPrimary),
    bodyMedium: TuShenTypography.bodyMedium.copyWith(color: TuShenColors.textPrimary),
    bodySmall: TuShenTypography.bodySmall.copyWith(color: TuShenColors.textSecondary),
  );
  
  /// Dark text theme
  static TextTheme get darkTextTheme => TextTheme(
    displayLarge: TuShenTypography.displayLarge.copyWith(color: TuShenColors.textOnDark),
    displayMedium: TuShenTypography.displayMedium.copyWith(color: TuShenColors.textOnDark),
    displaySmall: TuShenTypography.displaySmall.copyWith(color: TuShenColors.textOnDark),
    headlineLarge: TuShenTypography.headlineLarge.copyWith(color: TuShenColors.textOnDark),
    headlineMedium: TuShenTypography.headlineMedium.copyWith(color: TuShenColors.textOnDark),
    headlineSmall: TuShenTypography.headlineSmall.copyWith(color: TuShenColors.textOnDark),
    titleLarge: TuShenTypography.titleLarge.copyWith(color: TuShenColors.textOnDark),
    titleMedium: TuShenTypography.titleMedium.copyWith(color: TuShenColors.textOnDark),
    titleSmall: TuShenTypography.titleSmall.copyWith(color: TuShenColors.textOnDark),
    labelLarge: TuShenTypography.labelLarge.copyWith(color: TuShenColors.textOnDark),
    labelMedium: TuShenTypography.labelMedium.copyWith(color: TuShenColors.neutral400),
    labelSmall: TuShenTypography.labelSmall.copyWith(color: TuShenColors.neutral400),
    bodyLarge: TuShenTypography.bodyLarge.copyWith(color: TuShenColors.textOnDark),
    bodyMedium: TuShenTypography.bodyMedium.copyWith(color: TuShenColors.textOnDark),
    bodySmall: TuShenTypography.bodySmall.copyWith(color: TuShenColors.neutral400),
  );
}

/// Utility class for text operations
class TextUtils {
  TextUtils._();
  
  /// Scale text size based on accessibility settings
  static double getScaledFontSize(double baseFontSize, BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return baseFontSize * mediaQuery.textScaleFactor;
  }
  
  /// Get responsive font size based on screen width
  static double getResponsiveFontSize(double baseFontSize, BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return baseFontSize * 0.9; // Smaller on mobile
    } else if (screenWidth < 1200) {
      return baseFontSize; // Normal on tablet
    } else {
      return baseFontSize * 1.1; // Larger on desktop
    }
  }
  
  /// Create text style with custom color
  static TextStyle withColor(TextStyle style, Color color) {
    return style.copyWith(color: color);
  }
  
  /// Create text style with custom weight
  static TextStyle withWeight(TextStyle style, FontWeight weight) {
    return style.copyWith(fontWeight: weight);
  }
  
  /// Create text style with custom size
  static TextStyle withSize(TextStyle style, double size) {
    return style.copyWith(fontSize: size);
  }
}
