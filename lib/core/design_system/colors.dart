import 'package:flutter/material.dart';

/// TuShen Design System - Colors
///
/// Defines the color palette for the TuShen app, including primary, secondary,
/// accent colors, and semantic colors for different states and contexts.

class TuShenColors {
  TuShenColors._();

  // Primary Colors - Modern photography inspired purple-blue gradient
  static const Color primary = Color(0xFF6366F1); // Indigo-500
  static const Color primaryDark = Color(0xFF4338CA); // Indigo-700
  static const Color primaryLight = Color(0xFF818CF8); // Indigo-400
  static const Color primaryContainer = Color(0xFFE0E7FF); // Indigo-100

  // Secondary Colors - Warm coral-pink for creativity
  static const Color secondary = Color(0xFFEC4899); // Pink-500
  static const Color secondaryDark = Color(0xFFBE185D); // Pink-700
  static const Color secondaryLight = Color(0xFFF472B6); // Pink-400
  static const Color secondaryContainer = Color(0xFFFCE7F3); // Pink-100

  // Accent Colors - Vibrant emerald for success states
  static const Color accent = Color(0xFF10B981); // Emerald-500
  static const Color accentDark = Color(0xFF047857); // Emerald-700
  static const Color accentLight = Color(0xFF34D399); // Emerald-400
  static const Color accentContainer = Color(0xFFD1FAE5); // Emerald-100

  // Neutral Colors - Grays for backgrounds and text
  static const Color neutral900 = Color(0xFF212121); // Almost black
  static const Color neutral800 = Color(0xFF424242); // Dark gray
  static const Color neutral700 = Color(0xFF616161); // Medium dark gray
  static const Color neutral600 = Color(0xFF757575); // Medium gray
  static const Color neutral500 = Color(0xFF9E9E9E); // Gray
  static const Color neutral400 = Color(0xFFBDBDBD); // Light gray
  static const Color neutral300 = Color(0xFFE0E0E0); // Very light gray
  static const Color neutral200 = Color(0xFFEEEEEE); // Off white
  static const Color neutral100 = Color(0xFFF5F5F5); // Almost white
  static const Color neutral50 = Color(0xFFFAFAFA); // White

  // Semantic Colors - For states and feedback
  static const Color success = Color(0xFF4CAF50); // Green
  static const Color successLight = Color(0xFFC8E6C9); // Light green
  static const Color successDark = Color(0xFF2E7D32); // Dark green

  static const Color warning = Color(0xFFFF9800); // Orange
  static const Color warningLight = Color(0xFFFFE0B2); // Light orange
  static const Color warningDark = Color(0xFFE65100); // Dark orange

  static const Color error = Color(0xFFF44336); // Red
  static const Color errorLight = Color(0xFFFFCDD2); // Light red
  static const Color errorDark = Color(0xFFD32F2F); // Dark red

  static const Color info = Color(0xFF2196F3); // Blue
  static const Color infoLight = Color(0xFFBBDEFB); // Light blue
  static const Color infoDark = Color(0xFF1565C0); // Dark blue

  // Surface Colors - For cards, sheets, and containers
  static const Color surface = Color(0xFFFFFFFF); // White
  static const Color surfaceDark = Color(0xFF121212); // Dark surface
  static const Color surfaceVariant = Color(0xFFF5F5F5); // Light gray surface
  static const Color surfaceVariantDark = Color(
    0xFF1E1E1E,
  ); // Dark variant surface

  // Background Colors
  static const Color background = Color(0xFFFAFAFA); // Very light gray
  static const Color backgroundDark = Color(0xFF121212); // Dark background

  // Text Colors
  static const Color textPrimary = Color(0xFF212121); // Dark gray
  static const Color textSecondary = Color(0xFF757575); // Medium gray
  static const Color textDisabled = Color(0xFFBDBDBD); // Light gray
  static const Color textOnPrimary = Color(0xFFFFFFFF); // White
  static const Color textOnSecondary = Color(0xFF000000); // Black
  static const Color textOnDark = Color(0xFFFFFFFF); // White

  // Border Colors
  static const Color border = Color(0xFFE0E0E0); // Light gray
  static const Color borderDark = Color(0xFF424242); // Dark gray
  static const Color borderFocus = Color(0xFF2196F3); // Blue

  // Shadow Colors
  static const Color shadow = Color(0x1F000000); // 12% black
  static const Color shadowDark = Color(0x3D000000); // 24% black

  // Overlay Colors
  static const Color overlay = Color(0x80000000); // 50% black
  static const Color overlayLight = Color(0x33000000); // 20% black

  // Photography-specific Colors
  static const Color lens = Color(0xFF37474F); // Blue gray
  static const Color aperture = Color(0xFF455A64); // Dark blue gray
  static const Color shutter = Color(0xFF546E7A); // Medium blue gray
  static const Color iso = Color(0xFF607D8B); // Light blue gray

  // Filter Colors - For image filter previews
  static const Color filterWarm = Color(0xFFFFB74D); // Warm orange
  static const Color filterCool = Color(0xFF64B5F6); // Cool blue
  static const Color filterVintage = Color(0xFFD7CCC8); // Vintage brown
  static const Color filterBW = Color(0xFF9E9E9E); // Black and white gray
  static const Color filterVibrant = Color(0xFFE91E63); // Vibrant pink

  // Modern Gradient Colors
  static const List<Color> primaryGradient = [
    Color(0xFF6366F1), // Indigo-500
    Color(0xFF8B5CF6), // Violet-500
  ];

  static const List<Color> secondaryGradient = [
    Color(0xFFEC4899), // Pink-500
    Color(0xFFF97316), // Orange-500
  ];

  static const List<Color> accentGradient = [
    Color(0xFF10B981), // Emerald-500
    Color(0xFF06B6D4), // Cyan-500
  ];

  static const List<Color> sunsetGradient = [
    Color(0xFFF59E0B), // Amber-500
    Color(0xFFEF4444), // Red-500
    Color(0xFFEC4899), // Pink-500
  ];

  static const List<Color> oceanGradient = [
    Color(0xFF0EA5E9), // Sky-500
    Color(0xFF3B82F6), // Blue-500
    Color(0xFF6366F1), // Indigo-500
  ];

  static const List<Color> forestGradient = [
    Color(0xFF059669), // Emerald-600
    Color(0xFF10B981), // Emerald-500
    Color(0xFF34D399), // Emerald-400
  ];

  // New modern gradients
  static const List<Color> neonGradient = [
    Color(0xFF8B5CF6), // Violet-500
    Color(0xFFEC4899), // Pink-500
    Color(0xFF06B6D4), // Cyan-500
  ];

  static const List<Color> warmGradient = [
    Color(0xFFF59E0B), // Amber-500
    Color(0xFFEF4444), // Red-500
  ];

  static const List<Color> coolGradient = [
    Color(0xFF06B6D4), // Cyan-500
    Color(0xFF3B82F6), // Blue-500
  ];
}

/// Extension for creating Material 3 color schemes
extension TuShenColorScheme on TuShenColors {
  /// Light color scheme
  static ColorScheme get lightScheme => ColorScheme.fromSeed(
    seedColor: TuShenColors.primary,
    brightness: Brightness.light,
    primary: TuShenColors.primary,
    secondary: TuShenColors.secondary,
    tertiary: TuShenColors.accent,
    surface: TuShenColors.surface,
    error: TuShenColors.error,
  );

  /// Dark color scheme
  static ColorScheme get darkScheme => ColorScheme.fromSeed(
    seedColor: TuShenColors.primary,
    brightness: Brightness.dark,
    primary: TuShenColors.primaryLight,
    secondary: TuShenColors.secondaryLight,
    tertiary: TuShenColors.accentLight,
    surface: TuShenColors.surfaceDark,
    error: TuShenColors.error,
  );
}

/// Utility class for color operations
class ColorUtils {
  ColorUtils._();

  /// Create a color with opacity
  static Color withOpacity(Color color, double opacity) {
    return color.withValues(alpha: opacity);
  }

  /// Lighten a color by a percentage
  static Color lighten(Color color, double percentage) {
    final hsl = HSLColor.fromColor(color);
    final lightness = (hsl.lightness + percentage).clamp(0.0, 1.0);
    return hsl.withLightness(lightness).toColor();
  }

  /// Darken a color by a percentage
  static Color darken(Color color, double percentage) {
    final hsl = HSLColor.fromColor(color);
    final lightness = (hsl.lightness - percentage).clamp(0.0, 1.0);
    return hsl.withLightness(lightness).toColor();
  }

  /// Get contrasting text color for a background
  static Color getContrastingTextColor(Color backgroundColor) {
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? TuShenColors.textPrimary : TuShenColors.textOnDark;
  }

  /// Create a gradient from two colors
  static LinearGradient createGradient(
    Color startColor,
    Color endColor, {
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
  }) {
    return LinearGradient(
      begin: begin,
      end: end,
      colors: [startColor, endColor],
    );
  }
}
