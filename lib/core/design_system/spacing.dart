import 'package:flutter/material.dart';

/// TuShen Design System - Spacing & Dimensions
/// 
/// Defines consistent spacing, padding, margin, and dimension values
/// throughout the TuShen app for a cohesive visual rhythm.

class TuShenSpacing {
  TuShenSpacing._();

  // Base spacing unit (8dp grid system)
  static const double baseUnit = 8.0;
  
  // Spacing Scale (based on 8dp grid)
  static const double xs = baseUnit * 0.5; // 4dp
  static const double sm = baseUnit * 1.0; // 8dp
  static const double md = baseUnit * 2.0; // 16dp
  static const double lg = baseUnit * 3.0; // 24dp
  static const double xl = baseUnit * 4.0; // 32dp
  static const double xxl = baseUnit * 5.0; // 40dp
  static const double xxxl = baseUnit * 6.0; // 48dp
  
  // Semantic spacing values
  static const double tiny = xs; // 4dp
  static const double small = sm; // 8dp
  static const double medium = md; // 16dp
  static const double large = lg; // 24dp
  static const double huge = xl; // 32dp
  static const double massive = xxl; // 40dp
  
  // Component-specific spacing
  static const double buttonPadding = md; // 16dp
  static const double cardPadding = md; // 16dp
  static const double listItemPadding = md; // 16dp
  static const double dialogPadding = lg; // 24dp
  static const double screenPadding = md; // 16dp
  static const double sectionSpacing = xl; // 32dp
  
  // Icon sizes
  static const double iconXs = 12.0;
  static const double iconSm = 16.0;
  static const double iconMd = 24.0;
  static const double iconLg = 32.0;
  static const double iconXl = 48.0;
  static const double iconXxl = 64.0;
  
  // Avatar sizes
  static const double avatarSm = 32.0;
  static const double avatarMd = 48.0;
  static const double avatarLg = 64.0;
  static const double avatarXl = 96.0;
  
  // Button heights
  static const double buttonHeightSm = 32.0;
  static const double buttonHeightMd = 40.0;
  static const double buttonHeightLg = 48.0;
  static const double buttonHeightXl = 56.0;
  
  // Input field heights
  static const double inputHeightSm = 32.0;
  static const double inputHeightMd = 40.0;
  static const double inputHeightLg = 48.0;
  
  // Border radius values
  static const double radiusXs = 2.0;
  static const double radiusSm = 4.0;
  static const double radiusMd = 8.0;
  static const double radiusLg = 12.0;
  static const double radiusXl = 16.0;
  static const double radiusXxl = 24.0;
  static const double radiusRound = 999.0; // Fully rounded
  
  // Border widths
  static const double borderThin = 1.0;
  static const double borderMedium = 2.0;
  static const double borderThick = 4.0;
  
  // Shadow elevations
  static const double elevationNone = 0.0;
  static const double elevationSm = 2.0;
  static const double elevationMd = 4.0;
  static const double elevationLg = 8.0;
  static const double elevationXl = 16.0;
  static const double elevationXxl = 24.0;
}

/// Responsive breakpoints for different screen sizes
class TuShenBreakpoints {
  TuShenBreakpoints._();
  
  static const double mobile = 480.0;
  static const double tablet = 768.0;
  static const double desktop = 1024.0;
  static const double largeDesktop = 1440.0;
  
  /// Check if screen is mobile size
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobile;
  }
  
  /// Check if screen is tablet size
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobile && width < desktop;
  }
  
  /// Check if screen is desktop size
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= desktop;
  }
  
  /// Get responsive value based on screen size
  static T getResponsiveValue<T>(
    BuildContext context, {
    required T mobile,
    T? tablet,
    T? desktop,
  }) {
    final width = MediaQuery.of(context).size.width;
    
    if (width >= TuShenBreakpoints.desktop && desktop != null) {
      return desktop;
    } else if (width >= TuShenBreakpoints.mobile && tablet != null) {
      return tablet;
    } else {
      return mobile;
    }
  }
}

/// Predefined EdgeInsets for consistent spacing
class TuShenPadding {
  TuShenPadding._();
  
  // Symmetric padding
  static const EdgeInsets allXs = EdgeInsets.all(TuShenSpacing.xs);
  static const EdgeInsets allSm = EdgeInsets.all(TuShenSpacing.sm);
  static const EdgeInsets allMd = EdgeInsets.all(TuShenSpacing.md);
  static const EdgeInsets allLg = EdgeInsets.all(TuShenSpacing.lg);
  static const EdgeInsets allXl = EdgeInsets.all(TuShenSpacing.xl);
  static const EdgeInsets allXxl = EdgeInsets.all(TuShenSpacing.xxl);
  
  // Horizontal padding
  static const EdgeInsets horizontalXs = EdgeInsets.symmetric(horizontal: TuShenSpacing.xs);
  static const EdgeInsets horizontalSm = EdgeInsets.symmetric(horizontal: TuShenSpacing.sm);
  static const EdgeInsets horizontalMd = EdgeInsets.symmetric(horizontal: TuShenSpacing.md);
  static const EdgeInsets horizontalLg = EdgeInsets.symmetric(horizontal: TuShenSpacing.lg);
  static const EdgeInsets horizontalXl = EdgeInsets.symmetric(horizontal: TuShenSpacing.xl);
  
  // Vertical padding
  static const EdgeInsets verticalXs = EdgeInsets.symmetric(vertical: TuShenSpacing.xs);
  static const EdgeInsets verticalSm = EdgeInsets.symmetric(vertical: TuShenSpacing.sm);
  static const EdgeInsets verticalMd = EdgeInsets.symmetric(vertical: TuShenSpacing.md);
  static const EdgeInsets verticalLg = EdgeInsets.symmetric(vertical: TuShenSpacing.lg);
  static const EdgeInsets verticalXl = EdgeInsets.symmetric(vertical: TuShenSpacing.xl);
  
  // Screen padding (safe area aware)
  static const EdgeInsets screen = EdgeInsets.all(TuShenSpacing.screenPadding);
  static const EdgeInsets screenHorizontal = EdgeInsets.symmetric(horizontal: TuShenSpacing.screenPadding);
  static const EdgeInsets screenVertical = EdgeInsets.symmetric(vertical: TuShenSpacing.screenPadding);
  
  // Component-specific padding
  static const EdgeInsets button = EdgeInsets.symmetric(
    horizontal: TuShenSpacing.md,
    vertical: TuShenSpacing.sm,
  );
  
  static const EdgeInsets card = EdgeInsets.all(TuShenSpacing.cardPadding);
  static const EdgeInsets listItem = EdgeInsets.all(TuShenSpacing.listItemPadding);
  static const EdgeInsets dialog = EdgeInsets.all(TuShenSpacing.dialogPadding);
}

/// Predefined SizedBox widgets for consistent spacing
class TuShenGaps {
  TuShenGaps._();
  
  // Vertical gaps
  static const SizedBox verticalXs = SizedBox(height: TuShenSpacing.xs);
  static const SizedBox verticalSm = SizedBox(height: TuShenSpacing.sm);
  static const SizedBox verticalMd = SizedBox(height: TuShenSpacing.md);
  static const SizedBox verticalLg = SizedBox(height: TuShenSpacing.lg);
  static const SizedBox verticalXl = SizedBox(height: TuShenSpacing.xl);
  static const SizedBox verticalXxl = SizedBox(height: TuShenSpacing.xxl);
  
  // Horizontal gaps
  static const SizedBox horizontalXs = SizedBox(width: TuShenSpacing.xs);
  static const SizedBox horizontalSm = SizedBox(width: TuShenSpacing.sm);
  static const SizedBox horizontalMd = SizedBox(width: TuShenSpacing.md);
  static const SizedBox horizontalLg = SizedBox(width: TuShenSpacing.lg);
  static const SizedBox horizontalXl = SizedBox(width: TuShenSpacing.xl);
  static const SizedBox horizontalXxl = SizedBox(width: TuShenSpacing.xxl);
  
  // Section gaps
  static const SizedBox section = SizedBox(height: TuShenSpacing.sectionSpacing);
}

/// Predefined BorderRadius for consistent rounded corners
class TuShenRadius {
  TuShenRadius._();
  
  static const BorderRadius xs = BorderRadius.all(Radius.circular(TuShenSpacing.radiusXs));
  static const BorderRadius sm = BorderRadius.all(Radius.circular(TuShenSpacing.radiusSm));
  static const BorderRadius md = BorderRadius.all(Radius.circular(TuShenSpacing.radiusMd));
  static const BorderRadius lg = BorderRadius.all(Radius.circular(TuShenSpacing.radiusLg));
  static const BorderRadius xl = BorderRadius.all(Radius.circular(TuShenSpacing.radiusXl));
  static const BorderRadius xxl = BorderRadius.all(Radius.circular(TuShenSpacing.radiusXxl));
  static const BorderRadius round = BorderRadius.all(Radius.circular(TuShenSpacing.radiusRound));
  
  // Directional radius
  static const BorderRadius topSm = BorderRadius.only(
    topLeft: Radius.circular(TuShenSpacing.radiusSm),
    topRight: Radius.circular(TuShenSpacing.radiusSm),
  );
  
  static const BorderRadius topMd = BorderRadius.only(
    topLeft: Radius.circular(TuShenSpacing.radiusMd),
    topRight: Radius.circular(TuShenSpacing.radiusMd),
  );
  
  static const BorderRadius topLg = BorderRadius.only(
    topLeft: Radius.circular(TuShenSpacing.radiusLg),
    topRight: Radius.circular(TuShenSpacing.radiusLg),
  );
  
  static const BorderRadius bottomSm = BorderRadius.only(
    bottomLeft: Radius.circular(TuShenSpacing.radiusSm),
    bottomRight: Radius.circular(TuShenSpacing.radiusSm),
  );
  
  static const BorderRadius bottomMd = BorderRadius.only(
    bottomLeft: Radius.circular(TuShenSpacing.radiusMd),
    bottomRight: Radius.circular(TuShenSpacing.radiusMd),
  );
  
  static const BorderRadius bottomLg = BorderRadius.only(
    bottomLeft: Radius.circular(TuShenSpacing.radiusLg),
    bottomRight: Radius.circular(TuShenSpacing.radiusLg),
  );
}

/// Utility functions for spacing calculations
class SpacingUtils {
  SpacingUtils._();
  
  /// Calculate responsive spacing based on screen size
  static double getResponsiveSpacing(BuildContext context, double baseSpacing) {
    return TuShenBreakpoints.getResponsiveValue(
      context,
      mobile: baseSpacing * 0.8,
      tablet: baseSpacing,
      desktop: baseSpacing * 1.2,
    );
  }
  
  /// Get safe area padding
  static EdgeInsets getSafeAreaPadding(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return EdgeInsets.only(
      top: mediaQuery.padding.top,
      bottom: mediaQuery.padding.bottom,
      left: mediaQuery.padding.left,
      right: mediaQuery.padding.right,
    );
  }
  
  /// Combine padding with safe area
  static EdgeInsets withSafeArea(BuildContext context, EdgeInsets padding) {
    final safeArea = getSafeAreaPadding(context);
    return EdgeInsets.only(
      top: padding.top + safeArea.top,
      bottom: padding.bottom + safeArea.bottom,
      left: padding.left + safeArea.left,
      right: padding.right + safeArea.right,
    );
  }
}
