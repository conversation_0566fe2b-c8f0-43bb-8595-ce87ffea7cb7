/// TuShen Design System
/// 
/// A comprehensive design system for the TuShen image processing app.
/// Provides consistent colors, typography, spacing, and theming across
/// the entire application.
/// 
/// Usage:
/// ```dart
/// import 'package:tushen/core/design_system/design_system.dart';
/// 
/// // Use colors
/// Container(color: TuShenColors.primary)
/// 
/// // Use typography
/// Text('Hello', style: TuShenTypography.headlineLarge)
/// 
/// // Use spacing
/// Padding(padding: TuShenPadding.allMd)
/// 
/// // Use theme
/// MaterialApp(theme: TuShenTheme.lightTheme)
/// ```

library design_system;

// Export all design system components
export 'colors.dart';
export 'typography.dart';
export 'spacing.dart';
export 'theme.dart';

// Re-export commonly used Flutter widgets for convenience
export 'package:flutter/material.dart' show
    // Core widgets
    Widget,
    StatelessWidget,
    StatefulWidget,
    State,
    BuildContext,
    
    // Layout widgets
    Container,
    Padding,
    Margin,
    SizedBox,
    Expanded,
    Flexible,
    Column,
    Row,
    Stack,
    Positioned,
    Align,
    Center,
    
    // Material widgets
    Scaffold,
    AppBar,
    Card,
    ListTile,
    Divider,
    
    // Button widgets
    ElevatedButton,
    OutlinedButton,
    TextButton,
    IconButton,
    FloatingActionButton,
    
    // Input widgets
    TextField,
    TextFormField,
    InputDecoration,
    
    // Text widgets
    Text,
    RichText,
    TextSpan,
    
    // Image widgets
    Image,
    Icon,
    
    // Navigation widgets
    BottomNavigationBar,
    BottomNavigationBarItem,
    
    // Dialog widgets
    Dialog,
    AlertDialog,
    SimpleDialog,
    
    // Other common widgets
    CircularProgressIndicator,
    LinearProgressIndicator,
    Chip,
    Switch,
    Slider,
    
    // Styling classes
    EdgeInsets,
    BorderRadius,
    Radius,
    BoxDecoration,
    BoxShadow,
    Gradient,
    LinearGradient,
    RadialGradient,
    
    // Color and theme classes
    Color,
    Colors,
    Theme,
    ThemeData,
    ColorScheme,
    TextTheme,
    TextStyle,
    FontWeight,
    
    // Layout and sizing
    MainAxisAlignment,
    CrossAxisAlignment,
    MainAxisSize,
    Axis,
    
    // Material Design
    Material,
    MaterialApp,
    MaterialPageRoute,
    
    // Animation
    AnimationController,
    Animation,
    Tween,
    Curve,
    Curves;
