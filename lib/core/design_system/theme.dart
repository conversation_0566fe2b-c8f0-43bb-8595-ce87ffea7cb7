import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'colors.dart';
import 'typography.dart';
import 'spacing.dart';

/// TuShen Design System - Theme
///
/// Defines the complete theme system for the TuShen app, including
/// light and dark themes with consistent styling across all components.

class TuShenTheme {
  TuShenTheme._();

  /// Light theme configuration
  static ThemeData get lightTheme {
    final colorScheme = TuShenColorScheme.lightScheme;

    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      textTheme: TuShenTextTheme.lightTextTheme,

      // App Bar Theme
      appBarTheme: AppBarTheme(
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        elevation: 0,
        scrolledUnderElevation: TuShenSpacing.elevationSm,
        centerTitle: true,
        titleTextStyle: TuShenTypography.titleLarge.copyWith(
          color: colorScheme.onSurface,
          fontWeight: TuShenTypography.semiBold,
        ),
        systemOverlayStyle: SystemUiOverlayStyle.dark,
      ),

      // Card Theme
      cardTheme: CardThemeData(
        color: colorScheme.surface,
        shadowColor: TuShenColors.shadow,
        elevation: TuShenSpacing.elevationSm,
        shape: const RoundedRectangleBorder(borderRadius: TuShenRadius.md),
        margin: TuShenPadding.allSm,
      ),

      // Elevated Button Theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style:
            ElevatedButton.styleFrom(
              backgroundColor: TuShenColors.primary,
              foregroundColor: Colors.white,
              elevation: TuShenSpacing.elevationSm,
              padding: TuShenPadding.button,
              minimumSize: const Size(0, TuShenSpacing.buttonHeightMd),
              shape: RoundedRectangleBorder(borderRadius: TuShenRadius.md),
              textStyle: TuShenTypography.button,
            ).copyWith(
              backgroundColor: WidgetStateProperty.resolveWith((states) {
                if (states.contains(WidgetState.pressed)) {
                  return TuShenColors.primaryDark;
                }
                if (states.contains(WidgetState.hovered)) {
                  return TuShenColors.primaryLight;
                }
                return TuShenColors.primary;
              }),
            ),
      ),

      // Outlined Button Theme
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: colorScheme.primary,
          padding: TuShenPadding.button,
          minimumSize: const Size(0, TuShenSpacing.buttonHeightMd),
          shape: RoundedRectangleBorder(borderRadius: TuShenRadius.md),
          side: BorderSide(
            color: colorScheme.outline,
            width: TuShenSpacing.borderThin,
          ),
          textStyle: TuShenTypography.button,
        ),
      ),

      // Text Button Theme
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: colorScheme.primary,
          padding: TuShenPadding.button,
          minimumSize: const Size(0, TuShenSpacing.buttonHeightMd),
          shape: RoundedRectangleBorder(borderRadius: TuShenRadius.md),
          textStyle: TuShenTypography.button,
        ),
      ),

      // Floating Action Button Theme
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: TuShenColors.primary,
        foregroundColor: Colors.white,
        elevation: TuShenSpacing.elevationMd,
        shape: RoundedRectangleBorder(borderRadius: TuShenRadius.lg),
      ),

      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: colorScheme.surfaceVariant,
        border: OutlineInputBorder(
          borderRadius: TuShenRadius.md,
          borderSide: BorderSide(
            color: colorScheme.outline,
            width: TuShenSpacing.borderThin,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: TuShenRadius.md,
          borderSide: BorderSide(
            color: colorScheme.outline,
            width: TuShenSpacing.borderThin,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: TuShenRadius.md,
          borderSide: BorderSide(
            color: TuShenColors.primary,
            width: TuShenSpacing.borderMedium,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: TuShenRadius.md,
          borderSide: BorderSide(
            color: colorScheme.error,
            width: TuShenSpacing.borderThin,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: TuShenRadius.md,
          borderSide: BorderSide(
            color: colorScheme.error,
            width: TuShenSpacing.borderMedium,
          ),
        ),
        contentPadding: TuShenPadding.allMd,
        labelStyle: TuShenTypography.bodyMedium,
        hintStyle: TuShenTypography.bodyMedium.copyWith(
          color: colorScheme.onSurfaceVariant,
        ),
      ),

      // List Tile Theme
      listTileTheme: ListTileThemeData(
        contentPadding: TuShenPadding.listItem,
        shape: RoundedRectangleBorder(borderRadius: TuShenRadius.md),
        titleTextStyle: TuShenTypography.bodyLarge,
        subtitleTextStyle: TuShenTypography.bodyMedium.copyWith(
          color: colorScheme.onSurfaceVariant,
        ),
      ),

      // Bottom Navigation Bar Theme
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: colorScheme.surface,
        selectedItemColor: TuShenColors.primary,
        unselectedItemColor: colorScheme.onSurfaceVariant,
        type: BottomNavigationBarType.fixed,
        elevation: TuShenSpacing.elevationMd,
        selectedLabelStyle: TuShenTypography.labelSmall,
        unselectedLabelStyle: TuShenTypography.labelSmall,
      ),

      // Dialog Theme
      dialogTheme: DialogThemeData(
        backgroundColor: colorScheme.surface,
        elevation: TuShenSpacing.elevationLg,
        shape: const RoundedRectangleBorder(borderRadius: TuShenRadius.lg),
        titleTextStyle: TuShenTypography.headlineSmall.copyWith(
          color: colorScheme.onSurface,
        ),
        contentTextStyle: TuShenTypography.bodyMedium.copyWith(
          color: colorScheme.onSurface,
        ),
      ),

      // Chip Theme
      chipTheme: ChipThemeData(
        backgroundColor: colorScheme.surfaceVariant,
        selectedColor: TuShenColors.primary.withValues(alpha: 0.2),
        labelStyle: TuShenTypography.labelMedium,
        padding: TuShenPadding.horizontalSm,
        shape: RoundedRectangleBorder(borderRadius: TuShenRadius.lg),
      ),

      // Switch Theme
      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return TuShenColors.primary;
          }
          return colorScheme.outline;
        }),
        trackColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return TuShenColors.primary.withValues(alpha: 0.3);
          }
          return colorScheme.surfaceVariant;
        }),
      ),

      // Slider Theme
      sliderTheme: SliderThemeData(
        activeTrackColor: TuShenColors.primary,
        inactiveTrackColor: colorScheme.surfaceVariant,
        thumbColor: TuShenColors.primary,
        overlayColor: TuShenColors.primary.withValues(alpha: 0.12),
        valueIndicatorColor: TuShenColors.primary,
        valueIndicatorTextStyle: TuShenTypography.labelSmall.copyWith(
          color: Colors.white,
        ),
      ),

      // Progress Indicator Theme
      progressIndicatorTheme: ProgressIndicatorThemeData(
        color: TuShenColors.primary,
        linearTrackColor: colorScheme.surfaceVariant,
        circularTrackColor: colorScheme.surfaceVariant,
      ),

      // Divider Theme
      dividerTheme: DividerThemeData(
        color: colorScheme.outline,
        thickness: TuShenSpacing.borderThin,
        space: TuShenSpacing.md,
      ),
    );
  }

  /// Dark theme configuration
  static ThemeData get darkTheme {
    final colorScheme = TuShenColorScheme.darkScheme;

    return lightTheme.copyWith(
      colorScheme: colorScheme,
      textTheme: TuShenTextTheme.darkTextTheme,

      // App Bar Theme for dark mode
      appBarTheme: lightTheme.appBarTheme.copyWith(
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        titleTextStyle: TuShenTypography.titleLarge.copyWith(
          color: colorScheme.onSurface,
          fontWeight: TuShenTypography.semiBold,
        ),
        systemOverlayStyle: SystemUiOverlayStyle.light,
      ),

      // Card Theme for dark mode
      cardTheme: lightTheme.cardTheme.copyWith(
        color: colorScheme.surface,
        shadowColor: TuShenColors.shadowDark,
      ),

      // Input Decoration Theme for dark mode
      inputDecorationTheme: lightTheme.inputDecorationTheme.copyWith(
        fillColor: colorScheme.surfaceVariant,
        hintStyle: TuShenTypography.bodyMedium.copyWith(
          color: colorScheme.onSurfaceVariant,
        ),
      ),

      // Bottom Navigation Bar Theme for dark mode
      bottomNavigationBarTheme: lightTheme.bottomNavigationBarTheme.copyWith(
        backgroundColor: colorScheme.surface,
        selectedItemColor: TuShenColors.primary,
        unselectedItemColor: colorScheme.onSurfaceVariant,
      ),
    );
  }
}

/// Theme extensions for custom properties
extension TuShenThemeExtension on ThemeData {
  /// Access TuShen colors
  Type get tuShenColors => TuShenColors;

  /// Access TuShen spacing
  Type get tuShenSpacing => TuShenSpacing;

  /// Access TuShen typography
  Type get tuShenTypography => TuShenTypography;
}

/// Utility class for theme operations
class ThemeUtils {
  ThemeUtils._();

  /// Check if current theme is dark
  static bool isDarkMode(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark;
  }

  /// Get appropriate color for current theme
  static Color getAdaptiveColor(
    BuildContext context, {
    required Color lightColor,
    required Color darkColor,
  }) {
    return isDarkMode(context) ? darkColor : lightColor;
  }

  /// Get system status bar style for current theme
  static SystemUiOverlayStyle getSystemUiOverlayStyle(BuildContext context) {
    return isDarkMode(context)
        ? SystemUiOverlayStyle.light
        : SystemUiOverlayStyle.dark;
  }
}
