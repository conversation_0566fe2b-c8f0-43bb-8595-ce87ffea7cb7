/// TuShen UI Components Library
/// 
/// A comprehensive collection of reusable UI components for the TuShen app.
/// All components follow the TuShen design system and provide consistent
/// styling, behavior, and accessibility features.
/// 
/// Usage:
/// ```dart
/// import 'package:tushen/core/widgets/widgets.dart';
/// 
/// // Use buttons
/// TuShenButton(
///   onPressed: () {},
///   child: Text('Click me'),
/// )
/// 
/// // Use cards
/// TuShenCard(
///   child: Text('Card content'),
/// )
/// 
/// // Use text fields
/// TuShenTextField(
///   label: 'Enter text',
///   onChanged: (value) {},
/// )
/// ```

library widgets;

// Export design system for convenience
export '../design_system/design_system.dart';

// Button components
export 'buttons/tushen_button.dart';

// Card components
export 'cards/tushen_card.dart';

// Input components
export 'inputs/tushen_text_field.dart';

// Common Flutter widgets for convenience
export 'package:flutter/material.dart' show
    // Core widgets
    Widget,
    StatelessWidget,
    StatefulWidget,
    State,
    BuildContext,
    Key,
    GlobalKey,
    
    // Layout widgets
    Container,
    Padding,
    Margin,
    SizedBox,
    Expanded,
    Flexible,
    Column,
    Row,
    Stack,
    Positioned,
    Align,
    Center,
    Wrap,
    Flow,
    
    // Scrollable widgets
    ListView,
    GridView,
    SingleChildScrollView,
    CustomScrollView,
    ScrollController,
    ScrollPhysics,
    
    // Material widgets
    Scaffold,
    AppBar,
    Drawer,
    BottomSheet,
    SnackBar,
    
    // Navigation widgets
    BottomNavigationBar,
    BottomNavigationBarItem,
    TabBar,
    TabBarView,
    Tab,
    
    // Dialog widgets
    Dialog,
    AlertDialog,
    SimpleDialog,
    showDialog,
    showModalBottomSheet,
    
    // Form widgets
    Form,
    FormField,
    
    // Gesture widgets
    GestureDetector,
    InkWell,
    InkResponse,
    
    // Animation widgets
    AnimatedContainer,
    AnimatedOpacity,
    AnimatedPositioned,
    AnimatedSize,
    AnimatedSwitcher,
    Hero,
    
    // Image widgets
    Image,
    Icon,
    CircleAvatar,
    
    // Text widgets
    Text,
    RichText,
    TextSpan,
    SelectableText,
    
    // Progress indicators
    CircularProgressIndicator,
    LinearProgressIndicator,
    RefreshIndicator,
    
    // Interactive widgets
    Checkbox,
    Radio,
    Switch,
    Slider,
    RangeSlider,
    
    // Menu widgets
    PopupMenuButton,
    PopupMenuItem,
    DropdownButton,
    DropdownMenuItem,
    
    // Other common widgets
    Divider,
    VerticalDivider,
    Spacer,
    Opacity,
    ClipRRect,
    ClipOval,
    ClipPath,
    
    // Styling classes
    EdgeInsets,
    BorderRadius,
    Radius,
    BoxDecoration,
    BoxShadow,
    BoxConstraints,
    
    // Color and theme classes
    Color,
    Colors,
    Theme,
    ThemeData,
    ColorScheme,
    TextTheme,
    TextStyle,
    FontWeight,
    FontStyle,
    
    // Layout and sizing
    MainAxisAlignment,
    CrossAxisAlignment,
    MainAxisSize,
    Axis,
    WrapAlignment,
    WrapCrossAlignment,
    
    // Input and focus
    FocusNode,
    FocusScope,
    TextEditingController,
    TextInputType,
    TextInputAction,
    TextCapitalization,
    
    // Material Design
    Material,
    MaterialApp,
    MaterialPageRoute,
    MaterialButton,
    
    // Animation
    AnimationController,
    Animation,
    Tween,
    ColorTween,
    Curve,
    Curves,
    TickerProvider,
    SingleTickerProviderStateMixin,
    TickerProviderStateMixin,
    
    // Utility classes
    MediaQuery,
    MediaQueryData,
    Orientation,
    TargetPlatform,
    
    // Callbacks and functions
    VoidCallback,
    ValueChanged,
    ValueGetter,
    ValueSetter,
    
    // Navigation
    Navigator,
    Route,
    PageRoute,
    
    // Accessibility
    Semantics,
    SemanticsProperties,
    
    // Platform services
    SystemChrome,
    SystemUiOverlayStyle,
    
    // Painting and rendering
    CustomPainter,
    Canvas,
    Paint,
    Path,
    Offset,
    Size,
    Rect;
