import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../design_system/design_system.dart';

/// TuShen Text Field Component
/// 
/// A customizable text input component that follows the TuShen design system.
/// Supports different variants, validation states, and input types.

enum TuShenTextFieldVariant {
  outlined,
  filled,
  underlined,
}

enum TuShenTextFieldSize {
  small,
  medium,
  large,
}

class TuShenTextField extends StatefulWidget {
  const TuShenTextField({
    super.key,
    this.controller,
    this.initialValue,
    this.focusNode,
    this.variant = TuShenTextFieldVariant.outlined,
    this.size = TuShenTextFieldSize.medium,
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.prefixIcon,
    this.suffixIcon,
    this.prefix,
    this.suffix,
    this.obscureText = false,
    this.enabled = true,
    this.readOnly = false,
    this.maxLines = 1,
    this.minLines,
    this.maxLength,
    this.keyboardType,
    this.textInputAction,
    this.textCapitalization = TextCapitalization.none,
    this.inputFormatters,
    this.validator,
    this.onChanged,
    this.onSubmitted,
    this.onTap,
    this.onEditingComplete,
    this.autofocus = false,
    this.autocorrect = true,
    this.enableSuggestions = true,
  });

  final TextEditingController? controller;
  final String? initialValue;
  final FocusNode? focusNode;
  final TuShenTextFieldVariant variant;
  final TuShenTextFieldSize size;
  final String? label;
  final String? hint;
  final String? helperText;
  final String? errorText;
  final IconData? prefixIcon;
  final IconData? suffixIcon;
  final Widget? prefix;
  final Widget? suffix;
  final bool obscureText;
  final bool enabled;
  final bool readOnly;
  final int? maxLines;
  final int? minLines;
  final int? maxLength;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final TextCapitalization textCapitalization;
  final List<TextInputFormatter>? inputFormatters;
  final String? Function(String?)? validator;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onSubmitted;
  final VoidCallback? onTap;
  final VoidCallback? onEditingComplete;
  final bool autofocus;
  final bool autocorrect;
  final bool enableSuggestions;

  @override
  State<TuShenTextField> createState() => _TuShenTextFieldState();
}

class _TuShenTextFieldState extends State<TuShenTextField>
    with SingleTickerProviderStateMixin {
  late FocusNode _focusNode;
  late TextEditingController _controller;
  late AnimationController _animationController;
  late Animation<Color?> _borderColorAnimation;
  late Animation<double> _labelScaleAnimation;
  
  bool _isFocused = false;
  bool _hasError = false;
  bool _obscureText = false;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _controller = widget.controller ?? TextEditingController(text: widget.initialValue);
    _obscureText = widget.obscureText;
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _borderColorAnimation = ColorTween(
      begin: Theme.of(context).colorScheme.outline,
      end: Theme.of(context).colorScheme.primary,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _labelScaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.85,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _focusNode.addListener(_handleFocusChange);
    _hasError = widget.errorText != null;
  }

  @override
  void didUpdateWidget(TuShenTextField oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.errorText != oldWidget.errorText) {
      setState(() {
        _hasError = widget.errorText != null;
      });
    }
  }

  @override
  void dispose() {
    _focusNode.removeListener(_handleFocusChange);
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    if (widget.controller == null) {
      _controller.dispose();
    }
    _animationController.dispose();
    super.dispose();
  }

  void _handleFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });
    
    if (_isFocused) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  void _toggleObscureText() {
    setState(() {
      _obscureText = !_obscureText;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return _buildTextField(context, colorScheme);
          },
        ),
        if (widget.helperText != null || widget.errorText != null)
          _buildHelperText(context, colorScheme),
      ],
    );
  }

  Widget _buildTextField(BuildContext context, ColorScheme colorScheme) {
    final inputDecoration = _getInputDecoration(colorScheme);
    final textStyle = _getTextStyle();
    
    return TextFormField(
      controller: _controller,
      focusNode: _focusNode,
      style: textStyle,
      decoration: inputDecoration,
      obscureText: _obscureText,
      enabled: widget.enabled,
      readOnly: widget.readOnly,
      maxLines: widget.maxLines,
      minLines: widget.minLines,
      maxLength: widget.maxLength,
      keyboardType: widget.keyboardType,
      textInputAction: widget.textInputAction,
      textCapitalization: widget.textCapitalization,
      inputFormatters: widget.inputFormatters,
      validator: widget.validator,
      onChanged: widget.onChanged,
      onFieldSubmitted: widget.onSubmitted,
      onTap: widget.onTap,
      onEditingComplete: widget.onEditingComplete,
      autofocus: widget.autofocus,
      autocorrect: widget.autocorrect,
      enableSuggestions: widget.enableSuggestions,
    );
  }

  InputDecoration _getInputDecoration(ColorScheme colorScheme) {
    final size = _getFieldSize();
    final colors = _getFieldColors(colorScheme);
    
    return InputDecoration(
      labelText: widget.label,
      hintText: widget.hint,
      errorText: widget.errorText,
      prefixIcon: widget.prefixIcon != null 
          ? Icon(widget.prefixIcon, size: size.iconSize)
          : null,
      suffixIcon: _buildSuffixIcon(size),
      prefix: widget.prefix,
      suffix: widget.suffix,
      filled: widget.variant == TuShenTextFieldVariant.filled,
      fillColor: colors.fillColor,
      contentPadding: size.contentPadding,
      border: _getBorder(colors.borderColor),
      enabledBorder: _getBorder(colors.enabledBorderColor),
      focusedBorder: _getBorder(colors.focusedBorderColor),
      errorBorder: _getBorder(colors.errorBorderColor),
      focusedErrorBorder: _getBorder(colors.focusedErrorBorderColor),
      disabledBorder: _getBorder(colors.disabledBorderColor),
      labelStyle: size.labelStyle?.copyWith(color: colors.labelColor),
      hintStyle: size.hintStyle?.copyWith(color: colors.hintColor),
      errorStyle: size.errorStyle?.copyWith(color: colors.errorColor),
      helperStyle: size.helperStyle?.copyWith(color: colors.helperColor),
      counterStyle: size.counterStyle?.copyWith(color: colors.counterColor),
    );
  }

  Widget? _buildSuffixIcon(_FieldSize size) {
    if (widget.obscureText) {
      return IconButton(
        icon: Icon(
          _obscureText ? Icons.visibility : Icons.visibility_off,
          size: size.iconSize,
        ),
        onPressed: _toggleObscureText,
      );
    }
    
    if (widget.suffixIcon != null) {
      return Icon(widget.suffixIcon, size: size.iconSize);
    }
    
    return null;
  }

  InputBorder _getBorder(Color color) {
    switch (widget.variant) {
      case TuShenTextFieldVariant.outlined:
        return OutlineInputBorder(
          borderRadius: TuShenRadius.md,
          borderSide: BorderSide(color: color, width: _getBorderWidth()),
        );
      case TuShenTextFieldVariant.filled:
        return OutlineInputBorder(
          borderRadius: TuShenRadius.md,
          borderSide: BorderSide.none,
        );
      case TuShenTextFieldVariant.underlined:
        return UnderlineInputBorder(
          borderSide: BorderSide(color: color, width: _getBorderWidth()),
        );
    }
  }

  double _getBorderWidth() {
    if (_hasError) return TuShenSpacing.borderMedium;
    if (_isFocused) return TuShenSpacing.borderMedium;
    return TuShenSpacing.borderThin;
  }

  _FieldSize _getFieldSize() {
    switch (widget.size) {
      case TuShenTextFieldSize.small:
        return _FieldSize(
          contentPadding: const EdgeInsets.symmetric(
            horizontal: TuShenSpacing.sm,
            vertical: TuShenSpacing.xs,
          ),
          iconSize: TuShenSpacing.iconSm,
          labelStyle: TuShenTypography.labelMedium,
          hintStyle: TuShenTypography.bodySmall,
          errorStyle: TuShenTypography.labelSmall,
          helperStyle: TuShenTypography.labelSmall,
          counterStyle: TuShenTypography.labelSmall,
        );
      case TuShenTextFieldSize.medium:
        return _FieldSize(
          contentPadding: TuShenPadding.allMd,
          iconSize: TuShenSpacing.iconMd,
          labelStyle: TuShenTypography.labelLarge,
          hintStyle: TuShenTypography.bodyMedium,
          errorStyle: TuShenTypography.labelMedium,
          helperStyle: TuShenTypography.labelMedium,
          counterStyle: TuShenTypography.labelMedium,
        );
      case TuShenTextFieldSize.large:
        return _FieldSize(
          contentPadding: const EdgeInsets.symmetric(
            horizontal: TuShenSpacing.lg,
            vertical: TuShenSpacing.md,
          ),
          iconSize: TuShenSpacing.iconLg,
          labelStyle: TuShenTypography.titleSmall,
          hintStyle: TuShenTypography.bodyLarge,
          errorStyle: TuShenTypography.labelLarge,
          helperStyle: TuShenTypography.labelLarge,
          counterStyle: TuShenTypography.labelLarge,
        );
    }
  }

  _FieldColors _getFieldColors(ColorScheme colorScheme) {
    final borderColor = _hasError 
        ? colorScheme.error 
        : (_isFocused ? _borderColorAnimation.value : colorScheme.outline);
    
    return _FieldColors(
      fillColor: widget.variant == TuShenTextFieldVariant.filled
          ? colorScheme.surfaceVariant
          : null,
      borderColor: borderColor ?? colorScheme.outline,
      enabledBorderColor: _hasError ? colorScheme.error : colorScheme.outline,
      focusedBorderColor: _hasError ? colorScheme.error : colorScheme.primary,
      errorBorderColor: colorScheme.error,
      focusedErrorBorderColor: colorScheme.error,
      disabledBorderColor: colorScheme.onSurface.withOpacity(0.12),
      labelColor: _hasError 
          ? colorScheme.error 
          : (_isFocused ? colorScheme.primary : colorScheme.onSurfaceVariant),
      hintColor: colorScheme.onSurfaceVariant,
      errorColor: colorScheme.error,
      helperColor: colorScheme.onSurfaceVariant,
      counterColor: colorScheme.onSurfaceVariant,
    );
  }

  TextStyle _getTextStyle() {
    switch (widget.size) {
      case TuShenTextFieldSize.small:
        return TuShenTypography.bodySmall;
      case TuShenTextFieldSize.medium:
        return TuShenTypography.bodyMedium;
      case TuShenTextFieldSize.large:
        return TuShenTypography.bodyLarge;
    }
  }

  Widget _buildHelperText(BuildContext context, ColorScheme colorScheme) {
    final text = widget.errorText ?? widget.helperText;
    final color = widget.errorText != null 
        ? colorScheme.error 
        : colorScheme.onSurfaceVariant;
    
    return Padding(
      padding: const EdgeInsets.only(
        top: TuShenSpacing.xs,
        left: TuShenSpacing.md,
        right: TuShenSpacing.md,
      ),
      child: Text(
        text!,
        style: _getFieldSize().helperStyle?.copyWith(color: color),
      ),
    );
  }
}

class _FieldSize {
  const _FieldSize({
    required this.contentPadding,
    required this.iconSize,
    this.labelStyle,
    this.hintStyle,
    this.errorStyle,
    this.helperStyle,
    this.counterStyle,
  });

  final EdgeInsets contentPadding;
  final double iconSize;
  final TextStyle? labelStyle;
  final TextStyle? hintStyle;
  final TextStyle? errorStyle;
  final TextStyle? helperStyle;
  final TextStyle? counterStyle;
}

class _FieldColors {
  const _FieldColors({
    this.fillColor,
    required this.borderColor,
    required this.enabledBorderColor,
    required this.focusedBorderColor,
    required this.errorBorderColor,
    required this.focusedErrorBorderColor,
    required this.disabledBorderColor,
    required this.labelColor,
    required this.hintColor,
    required this.errorColor,
    required this.helperColor,
    required this.counterColor,
  });

  final Color? fillColor;
  final Color borderColor;
  final Color enabledBorderColor;
  final Color focusedBorderColor;
  final Color errorBorderColor;
  final Color focusedErrorBorderColor;
  final Color disabledBorderColor;
  final Color labelColor;
  final Color hintColor;
  final Color errorColor;
  final Color helperColor;
  final Color counterColor;
}
