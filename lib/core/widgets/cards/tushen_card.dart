import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import '../../design_system/design_system.dart';

/// TuShen Card Component
///
/// A customizable card component that follows the TuShen design system.
/// Supports different variants, elevations, and interactive states.

enum TuShenCardVariant { elevated, outlined, filled }

class TuShenCard extends StatefulWidget {
  const TuShenCard({
    super.key,
    required this.child,
    this.variant = TuShenCardVariant.elevated,
    this.onTap,
    this.onLongPress,
    this.padding,
    this.margin,
    this.elevation,
    this.borderRadius,
    this.backgroundColor,
    this.borderColor,
    this.width,
    this.height,
    this.clipBehavior = Clip.antiAlias,
  });

  /// Card content
  final Widget child;

  /// Card visual variant
  final TuShenCardVariant variant;

  /// Tap callback
  final VoidCallback? onTap;

  /// Long press callback
  final VoidCallback? onLongPress;

  /// Internal padding
  final EdgeInsets? padding;

  /// External margin
  final EdgeInsets? margin;

  /// Card elevation (overrides variant default)
  final double? elevation;

  /// Border radius (overrides variant default)
  final BorderRadius? borderRadius;

  /// Background color (overrides variant default)
  final Color? backgroundColor;

  /// Border color (for outlined variant)
  final Color? borderColor;

  /// Card width
  final double? width;

  /// Card height
  final double? height;

  /// Clip behavior
  final Clip clipBehavior;

  @override
  State<TuShenCard> createState() => _TuShenCardState();
}

class _TuShenCardState extends State<TuShenCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _elevationAnimation;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _elevationAnimation = Tween<double>(begin: 0.0, end: 4.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.98).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (_isInteractive) {
      setState(() => _isPressed = true);
      _animationController.forward();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    if (_isInteractive) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  void _handleTapCancel() {
    if (_isInteractive) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  void _handleMouseEnter(PointerEnterEvent event) {
    if (_isInteractive) {
      setState(() => _isHovered = true);
    }
  }

  void _handleMouseExit(PointerExitEvent event) {
    if (_isInteractive) {
      setState(() => _isHovered = false);
    }
  }

  bool get _isInteractive => widget.onTap != null || widget.onLongPress != null;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _isPressed ? _scaleAnimation.value : 1.0,
          child: Container(
            width: widget.width,
            height: widget.height,
            margin: widget.margin ?? TuShenPadding.allSm,
            child: MouseRegion(
              onEnter: _handleMouseEnter,
              onExit: _handleMouseExit,
              child: GestureDetector(
                onTap: widget.onTap,
                onLongPress: widget.onLongPress,
                onTapDown: _handleTapDown,
                onTapUp: _handleTapUp,
                onTapCancel: _handleTapCancel,
                child: _buildCard(context, colorScheme),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCard(BuildContext context, ColorScheme colorScheme) {
    final cardStyle = _getCardStyle(colorScheme);

    return Material(
      color: cardStyle.backgroundColor,
      elevation: cardStyle.elevation,
      borderRadius: cardStyle.borderRadius,
      clipBehavior: widget.clipBehavior,
      child: Container(
        decoration: cardStyle.decoration,
        padding: widget.padding ?? TuShenPadding.card,
        child: widget.child,
      ),
    );
  }

  _CardStyle _getCardStyle(ColorScheme colorScheme) {
    final baseElevation = widget.elevation ?? _getDefaultElevation();
    final hoverElevation = _isHovered
        ? baseElevation + _elevationAnimation.value
        : baseElevation;

    switch (widget.variant) {
      case TuShenCardVariant.elevated:
        return _CardStyle(
          backgroundColor: widget.backgroundColor ?? colorScheme.surface,
          elevation: hoverElevation,
          borderRadius: widget.borderRadius ?? TuShenRadius.md,
          decoration: null,
        );

      case TuShenCardVariant.outlined:
        return _CardStyle(
          backgroundColor: widget.backgroundColor ?? colorScheme.surface,
          elevation: 0,
          borderRadius: widget.borderRadius ?? TuShenRadius.md,
          decoration: BoxDecoration(
            border: Border.all(
              color: widget.borderColor ?? colorScheme.outline,
              width: TuShenSpacing.borderThin,
            ),
            borderRadius: widget.borderRadius ?? TuShenRadius.md,
          ),
        );

      case TuShenCardVariant.filled:
        return _CardStyle(
          backgroundColor:
              widget.backgroundColor ?? colorScheme.surfaceContainerHighest,
          elevation: 0,
          borderRadius: widget.borderRadius ?? TuShenRadius.md,
          decoration: null,
        );
    }
  }

  double _getDefaultElevation() {
    switch (widget.variant) {
      case TuShenCardVariant.elevated:
        return TuShenSpacing.elevationSm;
      case TuShenCardVariant.outlined:
      case TuShenCardVariant.filled:
        return 0;
    }
  }
}

class _CardStyle {
  const _CardStyle({
    required this.backgroundColor,
    required this.elevation,
    required this.borderRadius,
    this.decoration,
  });

  final Color backgroundColor;
  final double elevation;
  final BorderRadius borderRadius;
  final BoxDecoration? decoration;
}

/// Specialized card variants for common use cases

/// Image Card - For displaying images with optional overlay content
class TuShenImageCard extends StatelessWidget {
  const TuShenImageCard({
    super.key,
    required this.imageProvider,
    this.onTap,
    this.onLongPress,
    this.overlay,
    this.aspectRatio = 1.0,
    this.fit = BoxFit.cover,
    this.borderRadius,
    this.margin,
  });

  final ImageProvider imageProvider;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final Widget? overlay;
  final double aspectRatio;
  final BoxFit fit;
  final BorderRadius? borderRadius;
  final EdgeInsets? margin;

  @override
  Widget build(BuildContext context) {
    return TuShenCard(
      onTap: onTap,
      onLongPress: onLongPress,
      padding: EdgeInsets.zero,
      margin: margin,
      borderRadius: borderRadius,
      child: AspectRatio(
        aspectRatio: aspectRatio,
        child: Stack(
          fit: StackFit.expand,
          children: [
            Image(image: imageProvider, fit: fit),
            if (overlay != null)
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withValues(alpha: 0.7),
                      ],
                    ),
                  ),
                  child: overlay,
                ),
              ),
          ],
        ),
      ),
    );
  }
}

/// Info Card - For displaying information with icon and text
class TuShenInfoCard extends StatelessWidget {
  const TuShenInfoCard({
    super.key,
    required this.title,
    this.subtitle,
    this.icon,
    this.trailing,
    this.onTap,
    this.variant = TuShenCardVariant.elevated,
  });

  final String title;
  final String? subtitle;
  final IconData? icon;
  final Widget? trailing;
  final VoidCallback? onTap;
  final TuShenCardVariant variant;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return TuShenCard(
      variant: variant,
      onTap: onTap,
      child: Row(
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              size: TuShenSpacing.iconLg,
              color: theme.colorScheme.primary,
            ),
            TuShenGaps.horizontalMd,
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(title, style: TuShenTypography.titleMedium),
                if (subtitle != null) ...[
                  TuShenGaps.verticalXs,
                  Text(
                    subtitle!,
                    style: TuShenTypography.bodyMedium.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ],
            ),
          ),
          if (trailing != null) ...[TuShenGaps.horizontalMd, trailing!],
        ],
      ),
    );
  }
}

/// Stat Card - For displaying statistics with value and label
class TuShenStatCard extends StatelessWidget {
  const TuShenStatCard({
    super.key,
    required this.value,
    required this.label,
    this.icon,
    this.trend,
    this.trendColor,
    this.onTap,
  });

  final String value;
  final String label;
  final IconData? icon;
  final String? trend;
  final Color? trendColor;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return TuShenCard(
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  value,
                  style: TuShenTypography.headlineMedium.copyWith(
                    color: theme.colorScheme.primary,
                    fontWeight: TuShenTypography.bold,
                  ),
                ),
              ),
              if (icon != null)
                Icon(
                  icon,
                  size: TuShenSpacing.iconMd,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
            ],
          ),
          TuShenGaps.verticalSm,
          Text(
            label,
            style: TuShenTypography.bodyMedium.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          if (trend != null) ...[
            TuShenGaps.verticalXs,
            Text(
              trend!,
              style: TuShenTypography.labelSmall.copyWith(
                color: trendColor ?? theme.colorScheme.onSurfaceVariant,
                fontWeight: TuShenTypography.medium,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
