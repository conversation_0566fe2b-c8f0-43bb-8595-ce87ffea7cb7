import 'package:flutter/material.dart';
import '../../design_system/design_system.dart';

/// TuShen <PERSON>ton Component
/// 
/// A customizable button component that follows the TuShen design system.
/// Supports different variants, sizes, and states with consistent styling.

enum TuShenButtonVariant {
  primary,
  secondary,
  outline,
  text,
  danger,
}

enum TuShenButtonSize {
  small,
  medium,
  large,
}

class TuShenButton extends StatefulWidget {
  const TuShenButton({
    super.key,
    required this.onPressed,
    required this.child,
    this.variant = TuShenButtonVariant.primary,
    this.size = TuShenButtonSize.medium,
    this.isLoading = false,
    this.isDisabled = false,
    this.fullWidth = false,
    this.icon,
    this.iconPosition = IconPosition.leading,
  });

  /// Button press callback
  final VoidCallback? onPressed;
  
  /// Button content (usually Text widget)
  final Widget child;
  
  /// Button visual variant
  final TuShenButtonVariant variant;
  
  /// Button size
  final TuShenButtonSize size;
  
  /// Whether button is in loading state
  final bool isLoading;
  
  /// Whether button is disabled
  final bool isDisabled;
  
  /// Whether button should take full width
  final bool fullWidth;
  
  /// Optional icon
  final IconData? icon;
  
  /// Icon position relative to text
  final IconPosition iconPosition;

  @override
  State<TuShenButton> createState() => _TuShenButtonState();
}

enum IconPosition { leading, trailing }

class _TuShenButtonState extends State<TuShenButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (!_isEffectivelyDisabled) {
      setState(() => _isPressed = true);
      _animationController.forward();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    if (!_isEffectivelyDisabled) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  void _handleTapCancel() {
    if (!_isEffectivelyDisabled) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  bool get _isEffectivelyDisabled => widget.isDisabled || widget.isLoading;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: _handleTapDown,
            onTapUp: _handleTapUp,
            onTapCancel: _handleTapCancel,
            child: _buildButton(context, colorScheme),
          ),
        );
      },
    );
  }

  Widget _buildButton(BuildContext context, ColorScheme colorScheme) {
    final buttonStyle = _getButtonStyle(colorScheme);
    final buttonChild = _buildButtonChild();

    switch (widget.variant) {
      case TuShenButtonVariant.primary:
        return ElevatedButton(
          onPressed: _isEffectivelyDisabled ? null : widget.onPressed,
          style: buttonStyle,
          child: buttonChild,
        );
      case TuShenButtonVariant.secondary:
        return ElevatedButton(
          onPressed: _isEffectivelyDisabled ? null : widget.onPressed,
          style: buttonStyle,
          child: buttonChild,
        );
      case TuShenButtonVariant.outline:
        return OutlinedButton(
          onPressed: _isEffectivelyDisabled ? null : widget.onPressed,
          style: buttonStyle,
          child: buttonChild,
        );
      case TuShenButtonVariant.text:
        return TextButton(
          onPressed: _isEffectivelyDisabled ? null : widget.onPressed,
          style: buttonStyle,
          child: buttonChild,
        );
      case TuShenButtonVariant.danger:
        return ElevatedButton(
          onPressed: _isEffectivelyDisabled ? null : widget.onPressed,
          style: buttonStyle,
          child: buttonChild,
        );
    }
  }

  ButtonStyle _getButtonStyle(ColorScheme colorScheme) {
    final size = _getButtonSize();
    final colors = _getButtonColors(colorScheme);
    
    return ButtonStyle(
      backgroundColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.disabled)) {
          return colors.disabledBackground;
        }
        if (states.contains(MaterialState.pressed)) {
          return colors.pressedBackground;
        }
        if (states.contains(MaterialState.hovered)) {
          return colors.hoveredBackground;
        }
        return colors.background;
      }),
      foregroundColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.disabled)) {
          return colors.disabledForeground;
        }
        return colors.foreground;
      }),
      overlayColor: MaterialStateProperty.all(colors.overlay),
      elevation: MaterialStateProperty.resolveWith((states) {
        if (widget.variant == TuShenButtonVariant.outline ||
            widget.variant == TuShenButtonVariant.text) {
          return 0;
        }
        if (states.contains(MaterialState.pressed)) {
          return TuShenSpacing.elevationSm;
        }
        return TuShenSpacing.elevationMd;
      }),
      padding: MaterialStateProperty.all(size.padding),
      minimumSize: MaterialStateProperty.all(size.minimumSize),
      shape: MaterialStateProperty.all(
        RoundedRectangleBorder(
          borderRadius: TuShenRadius.md,
          side: widget.variant == TuShenButtonVariant.outline
              ? BorderSide(color: colors.border ?? Colors.transparent)
              : BorderSide.none,
        ),
      ),
      textStyle: MaterialStateProperty.all(size.textStyle),
    );
  }

  _ButtonSize _getButtonSize() {
    switch (widget.size) {
      case TuShenButtonSize.small:
        return _ButtonSize(
          padding: const EdgeInsets.symmetric(
            horizontal: TuShenSpacing.sm,
            vertical: TuShenSpacing.xs,
          ),
          minimumSize: Size(
            widget.fullWidth ? double.infinity : 0,
            TuShenSpacing.buttonHeightSm,
          ),
          textStyle: TuShenTypography.labelMedium,
          iconSize: TuShenSpacing.iconSm,
        );
      case TuShenButtonSize.medium:
        return _ButtonSize(
          padding: TuShenPadding.button,
          minimumSize: Size(
            widget.fullWidth ? double.infinity : 0,
            TuShenSpacing.buttonHeightMd,
          ),
          textStyle: TuShenTypography.button,
          iconSize: TuShenSpacing.iconMd,
        );
      case TuShenButtonSize.large:
        return _ButtonSize(
          padding: const EdgeInsets.symmetric(
            horizontal: TuShenSpacing.lg,
            vertical: TuShenSpacing.md,
          ),
          minimumSize: Size(
            widget.fullWidth ? double.infinity : 0,
            TuShenSpacing.buttonHeightLg,
          ),
          textStyle: TuShenTypography.labelLarge,
          iconSize: TuShenSpacing.iconLg,
        );
    }
  }

  _ButtonColors _getButtonColors(ColorScheme colorScheme) {
    switch (widget.variant) {
      case TuShenButtonVariant.primary:
        return _ButtonColors(
          background: colorScheme.primary,
          foreground: colorScheme.onPrimary,
          pressedBackground: ColorUtils.darken(colorScheme.primary, 0.1),
          hoveredBackground: ColorUtils.lighten(colorScheme.primary, 0.1),
          disabledBackground: colorScheme.onSurface.withOpacity(0.12),
          disabledForeground: colorScheme.onSurface.withOpacity(0.38),
          overlay: colorScheme.onPrimary.withOpacity(0.12),
        );
      case TuShenButtonVariant.secondary:
        return _ButtonColors(
          background: colorScheme.secondary,
          foreground: colorScheme.onSecondary,
          pressedBackground: ColorUtils.darken(colorScheme.secondary, 0.1),
          hoveredBackground: ColorUtils.lighten(colorScheme.secondary, 0.1),
          disabledBackground: colorScheme.onSurface.withOpacity(0.12),
          disabledForeground: colorScheme.onSurface.withOpacity(0.38),
          overlay: colorScheme.onSecondary.withOpacity(0.12),
        );
      case TuShenButtonVariant.outline:
        return _ButtonColors(
          background: Colors.transparent,
          foreground: colorScheme.primary,
          pressedBackground: colorScheme.primary.withOpacity(0.12),
          hoveredBackground: colorScheme.primary.withOpacity(0.08),
          disabledBackground: Colors.transparent,
          disabledForeground: colorScheme.onSurface.withOpacity(0.38),
          overlay: colorScheme.primary.withOpacity(0.12),
          border: colorScheme.outline,
        );
      case TuShenButtonVariant.text:
        return _ButtonColors(
          background: Colors.transparent,
          foreground: colorScheme.primary,
          pressedBackground: colorScheme.primary.withOpacity(0.12),
          hoveredBackground: colorScheme.primary.withOpacity(0.08),
          disabledBackground: Colors.transparent,
          disabledForeground: colorScheme.onSurface.withOpacity(0.38),
          overlay: colorScheme.primary.withOpacity(0.12),
        );
      case TuShenButtonVariant.danger:
        return _ButtonColors(
          background: colorScheme.error,
          foreground: colorScheme.onError,
          pressedBackground: ColorUtils.darken(colorScheme.error, 0.1),
          hoveredBackground: ColorUtils.lighten(colorScheme.error, 0.1),
          disabledBackground: colorScheme.onSurface.withOpacity(0.12),
          disabledForeground: colorScheme.onSurface.withOpacity(0.38),
          overlay: colorScheme.onError.withOpacity(0.12),
        );
    }
  }

  Widget _buildButtonChild() {
    if (widget.isLoading) {
      return _buildLoadingChild();
    }

    if (widget.icon != null) {
      return _buildIconChild();
    }

    return widget.child;
  }

  Widget _buildLoadingChild() {
    final size = _getButtonSize();
    return SizedBox(
      width: size.iconSize,
      height: size.iconSize,
      child: CircularProgressIndicator(
        strokeWidth: 2,
        valueColor: AlwaysStoppedAnimation<Color>(
          _getButtonColors(Theme.of(context).colorScheme).foreground,
        ),
      ),
    );
  }

  Widget _buildIconChild() {
    final size = _getButtonSize();
    final icon = Icon(widget.icon, size: size.iconSize);
    
    if (widget.iconPosition == IconPosition.leading) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          icon,
          TuShenGaps.horizontalSm,
          widget.child,
        ],
      );
    } else {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          widget.child,
          TuShenGaps.horizontalSm,
          icon,
        ],
      );
    }
  }
}

class _ButtonSize {
  const _ButtonSize({
    required this.padding,
    required this.minimumSize,
    required this.textStyle,
    required this.iconSize,
  });

  final EdgeInsets padding;
  final Size minimumSize;
  final TextStyle textStyle;
  final double iconSize;
}

class _ButtonColors {
  const _ButtonColors({
    required this.background,
    required this.foreground,
    required this.pressedBackground,
    required this.hoveredBackground,
    required this.disabledBackground,
    required this.disabledForeground,
    required this.overlay,
    this.border,
  });

  final Color background;
  final Color foreground;
  final Color pressedBackground;
  final Color hoveredBackground;
  final Color disabledBackground;
  final Color disabledForeground;
  final Color overlay;
  final Color? border;
}
