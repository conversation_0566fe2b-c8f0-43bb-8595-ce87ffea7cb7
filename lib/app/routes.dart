import 'package:flutter/material.dart';

import '../features/splash/splash_screen.dart';
import '../features/onboarding/onboarding_screen.dart';
import '../features/home/<USER>';
import '../features/gallery/gallery_screen.dart';
import '../features/settings/settings_screen.dart';
import '../features/collage/collage_screen.dart';
import '../features/editor/editor_screen.dart';
import '../features/live_photo/live_photo_screen.dart';
import '../features/format_converter/format_converter_screen.dart';
import '../features/long_screenshot/long_screenshot_screen.dart';
import '../features/image_compressor/image_compressor_screen.dart';
import '../features/image_authenticator/image_authenticator_screen.dart';
import '../features/debug/ffi_test_page.dart';
import 'main_shell.dart';

/// 应用路由名称常量
class AppRoutes {
  static const String splash = '/';
  static const String onboarding = '/onboarding';
  static const String main = '/main';
  static const String home = '/main/home';
  static const String gallery = '/main/gallery';
  static const String settings = '/main/settings';
  static const String collage = '/collage';
  static const String editor = '/editor';
  static const String livePhoto = '/live-photo';
  static const String formatConverter = '/format-converter';
  static const String longScreenshot = '/long-screenshot';
  static const String imageCompressor = '/image-compressor';
  static const String imageAuthenticator = '/image-authenticator';
  static const String ffiTest = '/ffi-test';
}

/// 路由参数类
class RouteArguments {
  final Map<String, dynamic> arguments;

  RouteArguments(this.arguments);

  T? get<T>(String key) => arguments[key] as T?;
  T getRequired<T>(String key) {
    final value = arguments[key];
    if (value == null) {
      throw ArgumentError('Required argument "$key" not found');
    }
    return value as T;
  }
}

/// 拼图页面参数
class CollageArguments extends RouteArguments {
  CollageArguments({required List<String> imageIds})
    : super({'imageIds': imageIds});

  List<String> get imageIds => getRequired<List<String>>('imageIds');
}

/// 编辑器页面参数
class EditorArguments extends RouteArguments {
  EditorArguments({required String imageId}) : super({'imageId': imageId});

  String get imageId => getRequired<String>('imageId');
}

/// Live图页面参数
class LivePhotoArguments extends RouteArguments {
  LivePhotoArguments({String? imageId, String? videoId})
    : super({'imageId': imageId, 'videoId': videoId});

  String? get imageId => get<String>('imageId');
  String? get videoId => get<String>('videoId');
}

/// 格式转换页面参数
class FormatConverterArguments extends RouteArguments {
  FormatConverterArguments({String? imageId}) : super({'imageId': imageId});

  String? get imageId => get<String>('imageId');
}

/// 图片压缩页面参数
class ImageCompressorArguments extends RouteArguments {
  ImageCompressorArguments({String? imageId}) : super({'imageId': imageId});

  String? get imageId => get<String>('imageId');
}

/// 图片认证页面参数
class ImageAuthenticatorArguments extends RouteArguments {
  ImageAuthenticatorArguments({String? imageId}) : super({'imageId': imageId});

  String? get imageId => get<String>('imageId');
}

/// 主Shell页面参数
class MainShellArguments extends RouteArguments {
  MainShellArguments({int initialIndex = 0})
    : super({'initialIndex': initialIndex});

  int get initialIndex => get<int>('initialIndex') ?? 0;
}

/// 路由生成器
class AppRouteGenerator {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case AppRoutes.splash:
        return MaterialPageRoute(
          builder: (_) => const SplashScreen(),
          settings: settings,
        );

      case AppRoutes.onboarding:
        return MaterialPageRoute(
          builder: (_) => const OnboardingScreen(),
          settings: settings,
        );

      case AppRoutes.main:
        final args = settings.arguments as MainShellArguments?;
        return MaterialPageRoute(
          builder: (_) => MainShell(initialIndex: args?.initialIndex ?? 0),
          settings: settings,
        );

      case AppRoutes.home:
        return MaterialPageRoute(
          builder: (_) => const HomeScreen(),
          settings: settings,
        );

      case AppRoutes.gallery:
        return MaterialPageRoute(
          builder: (_) => const GalleryScreen(),
          settings: settings,
        );

      case AppRoutes.settings:
        return MaterialPageRoute(
          builder: (_) => const SettingsScreen(),
          settings: settings,
        );

      case AppRoutes.collage:
        final args = settings.arguments as CollageArguments?;
        if (args == null) {
          return _errorRoute('Collage page requires imageIds argument');
        }
        return MaterialPageRoute(
          builder: (_) => CollageScreen(imageIds: args.imageIds),
          settings: settings,
        );

      case AppRoutes.editor:
        final args = settings.arguments as EditorArguments?;
        if (args == null) {
          return _errorRoute('Editor page requires imageId argument');
        }
        return MaterialPageRoute(
          builder: (_) => EditorScreen(imageId: args.imageId),
          settings: settings,
        );

      case AppRoutes.livePhoto:
        final args = settings.arguments as LivePhotoArguments?;
        return MaterialPageRoute(
          builder: (_) =>
              LivePhotoScreen(imageId: args?.imageId, videoId: args?.videoId),
          settings: settings,
        );

      case AppRoutes.formatConverter:
        final args = settings.arguments as FormatConverterArguments?;
        return MaterialPageRoute(
          builder: (_) => FormatConverterScreen(imageId: args?.imageId),
          settings: settings,
        );

      case AppRoutes.longScreenshot:
        return MaterialPageRoute(
          builder: (_) => const LongScreenshotScreen(),
          settings: settings,
        );

      case AppRoutes.imageCompressor:
        final args = settings.arguments as ImageCompressorArguments?;
        return MaterialPageRoute(
          builder: (_) => ImageCompressorScreen(imageId: args?.imageId),
          settings: settings,
        );

      case AppRoutes.imageAuthenticator:
        return MaterialPageRoute(
          builder: (_) => const ImageAuthenticatorScreen(),
          settings: settings,
        );

      case AppRoutes.ffiTest:
        return MaterialPageRoute(
          builder: (_) => const FFITestPage(),
          settings: settings,
        );

      default:
        return _errorRoute('Route ${settings.name} not found');
    }
  }

  static Route<dynamic> _errorRoute(String message) {
    return MaterialPageRoute(
      builder: (context) => Scaffold(
        appBar: AppBar(title: const Text('页面未找到')),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text(
                message,
                style: const TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => Navigator.of(
                  context,
                ).pushNamedAndRemoveUntil(AppRoutes.main, (route) => false),
                child: const Text('返回首页'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 导航扩展方法
extension NavigationExtensions on BuildContext {
  /// 导航到主页面
  Future<T?> pushMain<T extends Object?>({int initialIndex = 0}) {
    return Navigator.of(this).pushNamed(
      AppRoutes.main,
      arguments: MainShellArguments(initialIndex: initialIndex),
    );
  }

  /// 导航到拼图页面
  Future<T?> pushCollage<T extends Object?>(List<String> imageIds) {
    return Navigator.of(this).pushNamed(
      AppRoutes.collage,
      arguments: CollageArguments(imageIds: imageIds),
    );
  }

  /// 导航到编辑器页面
  Future<T?> pushEditor<T extends Object?>(String imageId) {
    return Navigator.of(
      this,
    ).pushNamed(AppRoutes.editor, arguments: EditorArguments(imageId: imageId));
  }

  /// 导航到Live图页面
  Future<T?> pushLivePhoto<T extends Object?>({
    String? imageId,
    String? videoId,
  }) {
    return Navigator.of(this).pushNamed(
      AppRoutes.livePhoto,
      arguments: LivePhotoArguments(imageId: imageId, videoId: videoId),
    );
  }

  /// 导航到格式转换页面
  Future<T?> pushFormatConverter<T extends Object?>({String? imageId}) {
    return Navigator.of(this).pushNamed(
      AppRoutes.formatConverter,
      arguments: FormatConverterArguments(imageId: imageId),
    );
  }

  /// 导航到长截屏页面
  Future<T?> pushLongScreenshot<T extends Object?>() {
    return Navigator.of(this).pushNamed(AppRoutes.longScreenshot);
  }

  /// 导航到图片压缩页面
  Future<T?> pushImageCompressor<T extends Object?>({String? imageId}) {
    return Navigator.of(this).pushNamed(
      AppRoutes.imageCompressor,
      arguments: ImageCompressorArguments(imageId: imageId),
    );
  }

  /// 导航到图片认证页面
  Future<T?> pushImageAuthenticator<T extends Object?>() {
    return Navigator.of(this).pushNamed(AppRoutes.imageAuthenticator);
  }

  /// 导航到引导页面
  Future<T?> pushOnboarding<T extends Object?>() {
    return Navigator.of(this).pushNamed(AppRoutes.onboarding);
  }

  /// 导航到FFI测试页面
  Future<T?> pushFFITest<T extends Object?>() {
    return Navigator.of(this).pushNamed(AppRoutes.ffiTest);
  }

  /// 替换为主页面
  Future<T?> pushReplacementMain<T extends Object?, TO extends Object?>({
    int initialIndex = 0,
  }) {
    return Navigator.of(this).pushReplacementNamed(
      AppRoutes.main,
      arguments: MainShellArguments(initialIndex: initialIndex),
    );
  }

  /// 清除所有页面并导航到主页面
  Future<T?> pushAndRemoveUntilMain<T extends Object?>({int initialIndex = 0}) {
    return Navigator.of(this).pushNamedAndRemoveUntil(
      AppRoutes.main,
      (route) => false,
      arguments: MainShellArguments(initialIndex: initialIndex),
    );
  }
}
