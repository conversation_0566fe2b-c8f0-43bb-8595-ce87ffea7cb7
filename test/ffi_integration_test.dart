import 'package:flutter_test/flutter_test.dart';
import 'package:tushen/core/services/image_processor_service.dart';
import 'package:tushen/core/services/generated/bridge.dart';
import 'package:tushen/core/services/rust_bridge_service.dart' as rust;
import 'dart:typed_data';

void main() {
  group('FFI Integration Tests', () {
    late ImageProcessorService imageProcessor;

    setUpAll(() async {
      imageProcessor = ImageProcessorService.instance;
      await imageProcessor.initialize();
    });

    test('should initialize FilterEngineBridge', () {
      expect(imageProcessor, isNotNull);
    });

    test('should get available filters from FFI', () {
      final filters = imageProcessor.getAvailableFilterNames();
      expect(filters, isNotEmpty);
      print('Available filters: $filters');
    });

    test('should apply filter using FFI', () async {
      // Create a simple test image
      final testImageData = ImageDataBridge(
        data: Uint8List.fromList(
          List.generate(100 * 100 * 4, (i) => 128),
        ), // Gray RGBA image
        width: 100,
        height: 100,
        format: 'rgba',
        colorSpace: 'srgb',
      );

      try {
        final result = await imageProcessor.applyFilter(
          image: testImageData,
          filterType: rust.FilterType.blur,
          intensity: 0.5,
        );

        expect(result, isNotNull);
        expect(result.width, equals(100));
        expect(result.height, equals(100));
        expect(result.data, isNotEmpty);
        print('Filter applied successfully via FFI');
      } catch (e) {
        print('FFI filter call failed, using fallback: $e');
        // This is expected if FFI is not fully working yet
      }
    });
  });
}
