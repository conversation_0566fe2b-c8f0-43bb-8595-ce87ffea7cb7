# Flutter Rust Bridge configuration
rust_input: crate::bridge
dart_output: lib/core/services/generated/
dart_format_line_length: 120
llvm_path:
  - /opt/homebrew/opt/llvm/bin
  - /usr/local/opt/llvm/bin
  - /usr/bin
rust_root: rust/
rust_output: rust/src/bridge_generated.rs
c_output: ios/Classes/bridge_generated.h
duplicated_c_output:
  - macos/Classes/bridge_generated.h
dart_entrypoint_class_name: TushenCore
dart_enums_style: true
enable_lifetime: true

