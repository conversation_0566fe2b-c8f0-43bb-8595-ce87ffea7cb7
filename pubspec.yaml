name: tushen
description: "图神-图像处理的神"
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # Core dependencies
  cupertino_icons: ^1.0.8

  # State management
  # 使用原生的 ValueNotifier 和 ChangeNotifier，不使用 bloc 或 riverpod
  provider: ^6.1.2

  # Navigation - 使用传统Navigator，不使用go_router

  # Internationalization
  intl: ^0.20.2

  # Image processing and handling
  image: ^4.2.0
  image_picker: ^1.1.2
  photo_manager: ^3.7.1

  # File handling
  path_provider: ^2.1.4
  path: ^1.9.0
  file_picker: ^8.1.2

  # Permissions
  permission_handler: ^11.3.1

  # UI and animations
  flutter_staggered_grid_view: ^0.7.0
  flutter_animate: ^4.5.0

  # Utilities
  uuid: ^4.5.1
  shared_preferences: ^2.3.2
  get_it: ^8.0.0

  # Network and storage
  dio: ^5.7.0

  # Platform integration
  device_info_plus: ^10.1.2
  package_info_plus: ^8.0.2

  # FFI for Rust integration
  flutter_rust_bridge: 2.9.0
  ffi: ^2.1.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

flutter:
  generate: true
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/fonts/