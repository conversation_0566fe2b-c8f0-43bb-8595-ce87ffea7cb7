{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_DEBUG=1 DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e98814b7e2c3bac55ee99d78eaa8d1ec61e", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_PROFILE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e98c22f26ca3341c3062f2313dc737070d4", "name": "Profile"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_RELEASE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e9828903703a9fe9e3707306e58aab67b51", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d0b25d39b515a574839e998df229c3cb", "path": "../Podfile", "sourceTree": "SOURCE_ROOT", "type": "file"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98da178c3911cd4eaeb279b7069f953116", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/device_info_plus-10.1.2/ios/Classes/FPPDeviceInfoPlusPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dcff19c29aaa3c67f5ea61d45b053f58", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/device_info_plus-10.1.2/ios/Classes/FPPDeviceInfoPlusPlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ddac70b7b971298cff756ea8fa92836a", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e64760fc0ec7f240ac056f9fc01deb56", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a935e71f051811d4434161ad876667e2", "name": "device_info_plus", "path": "device_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9803b029772b493d6dfa191690be30079f", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981de4f8754f9cc948b54c6ec6b858a25c", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9853acea6328a7b27c00e9ee3667a5a9d5", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98383c736c1d1dc4d6347d1b19d5bb32eb", "name": "tushen", "path": "tushen", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a57d0e04c3cafb1e1600aecbb42ce205", "name": "Startup", "path": "Startup", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9864e8870621a87dd9ab6689a303c55c93", "name": "MyProjects", "path": "MyProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984d426200412b7f50a3a9be638f6b46a8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e985a0af9759190a1482e5bfa1dd0c5f77a", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/device_info_plus-10.1.2/ios/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984c9ee27d5fd2c0938ccf2c9817cf1942", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bbe3d17dd445a39b26cddf24213a6c11", "name": "device_info_plus", "path": "device_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dd95f85cc9336876e71e60366a865eea", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9837de80cbb5ade917423ed8d3da9498fe", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9862bae2fb0c49a6c80411b475cade9d1f", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9824c2fa3c2eb58f9f6e358c67dc8400c0", "name": "tushen", "path": "tushen", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9898317e9b8b6758eb0816d18afb60cf1a", "name": "Startup", "path": "Startup", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a93429a60e0762304b274105ea3dd751", "name": "MyProjects", "path": "../MyProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b18ade2817369a14c1f109034195360a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98efcf972a0b8e0879d935670452157176", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dd1de4fa39f4fee7e8b11607d3b997a0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988a57809d7e8ec342c152acd9022c39a9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f599a3d6ab0d0b8ab7c1bb36fad6649d", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/device_info_plus-10.1.2/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e985171333a1ac47fd5e0143f1f0b2c0538", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/device_info_plus-10.1.2/ios/device_info_plus.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9836803c4f9afd97824eb2ed394a463d1a", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/device_info_plus-10.1.2/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ff932d624150bc0248c26789b43befac", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98c294d28c2601e3ebaa43512c475a2764", "path": "device_info_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98328d071c9ec8ceac402c976fb2ab31c4", "path": "device_info_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e981738f5bb695210bda0592153e3fb374f", "path": "device_info_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988294db9aaefd765684474459e49e4eaf", "path": "device_info_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d9e18f735505eb883ea2d9ad191cde63", "path": "device_info_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988bca65f40561c5ada158e4a01b6ebd9e", "path": "device_info_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a51454d72bdaacb949c3a73569bee6f3", "path": "device_info_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987fc5354f17c2606e2705891244ce555c", "path": "ResourceBundle-device_info_plus_privacy-device_info_plus-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b69ec47cf59e40201140946ff54cfaec", "name": "Support Files", "path": "../../../../Pods/Target Support Files/device_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b34eafcba40399d8802fc4ff63d7775b", "name": "device_info_plus", "path": "../.symlinks/plugins/device_info_plus/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9895947a49a650441170bb38092b9aaaf8", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/file_picker-8.3.7/ios/file_picker/Sources/file_picker/FileInfo.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988be57e69dd0f6ae0071eee80bb69718a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/file_picker-8.3.7/ios/file_picker/Sources/file_picker/FilePickerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f17c76e3471f41c61f236779fb78c813", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/file_picker-8.3.7/ios/file_picker/Sources/file_picker/FileUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986e4c2e76e6f485eea21072f3d9665e69", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/file_picker-8.3.7/ios/file_picker/Sources/file_picker/ImageUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e986c0352197672670f82282f3be2b21e09", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/file_picker-8.3.7/ios/file_picker/Sources/file_picker/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9871e78315104c268cacb772e291dbcd2f", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/file_picker-8.3.7/ios/file_picker/Sources/file_picker/include/file_picker-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985074bda436144be94677c5ced55aeb43", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/file_picker-8.3.7/ios/file_picker/Sources/file_picker/include/file_picker/FileInfo.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9845708bed8410fb464e8d5b44e2c8c895", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/file_picker-8.3.7/ios/file_picker/Sources/file_picker/include/file_picker/FilePickerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98249fceb4871f0722d036c8d8ad0ce890", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/file_picker-8.3.7/ios/file_picker/Sources/file_picker/include/file_picker/FileUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983e4754f771825044686ea92d9da828d2", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/file_picker-8.3.7/ios/file_picker/Sources/file_picker/include/file_picker/ImageUtils.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b109b5ac6f960d51507472b5fbe492e0", "name": "file_picker", "path": "file_picker", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9814250bf0fbeec30760f41b721780f217", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981f318f5ce5b7575d3f05fd3b8021cafb", "name": "file_picker", "path": "file_picker", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983b4b5d74beb500dd1864aeb474be5e6d", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980703ad050af0796e61c531a118fc7382", "name": "file_picker", "path": "file_picker", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984e16394ddc4e614636d2f7605b7bced8", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980ec1524538b84f8aace742dfd5486dfa", "name": "file_picker", "path": "file_picker", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981694a7426509843a1bc8e8d052c61afe", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983a7d8d148011205566164432d8ee78e2", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b494ffaab340e04219e875a732254008", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dbcc7988bc287e3025b2fbef0fc59e9f", "name": "tushen", "path": "tushen", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b5cc7e56ca8f454215b7524c78128a0b", "name": "Startup", "path": "Startup", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9892ea518e267c0f9fe416a9c492babcc3", "name": "MyProjects", "path": "MyProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98449dcb5cdeb141512076aae6a15a2b05", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984af2588500e7354810203544f21a8086", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982c183eac12fc0880d32fbcc17ca680f0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9861181283e5661d5a264836d074729a6a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987fda0d655a2fa368179ee440f458f12d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ab2eb83361e6f06d047ddf45f3c79d50", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9886496226a44a8c5452e0872c6638497d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989163abef389c871b18aa0b22e26e2849", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/file_picker-8.3.7/ios/file_picker/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98dee0c4b5af4d39d83c3ab769ac096a51", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/file_picker-8.3.7/ios/file_picker/Sources/file_picker/include/file_picker.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98de7b37e3f3b91326719217d6750281b5", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/file_picker-8.3.7/ios/file_picker.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98fb4d8c77cccd83694f38d4acb5751ea0", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/file_picker-8.3.7/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986899136d007880e798387662173abe94", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98581f79984f8570a982ed4e56d60dc185", "path": "file_picker.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f11ec81e55570447eb0fe5c6d63b4071", "path": "file_picker-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98a8972d25d1703d24592e3e383602a1f8", "path": "file_picker-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fd0b128537006399b32260494b8fbe5e", "path": "file_picker-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ab5410cf6d7ff09e98dfb260de19c47b", "path": "file_picker.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e982a26e0e92ee9ad1149f960dbf57e7962", "path": "file_picker.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f4b2c0dc1ab57aef9c96c0012a68250f", "path": "ResourceBundle-file_picker_ios_privacy-file_picker-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980e239f5ffadbd8faaebdf725bb0b8605", "name": "Support Files", "path": "../../../../Pods/Target Support Files/file_picker", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9866fed201bfff62d65e6fc84bc43b2f5c", "name": "file_picker", "path": "../.symlinks/plugins/file_picker/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98615d3e51c6f8d8495041d1d078bc21dd", "path": "Flutter.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c139c50b06d7e3257e1a339aab24f710", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9819795cc3620604d9a04096bcaa20b362", "path": "Flutter.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9881bcb81463b6723115c1d7827ee6a53c", "path": "Flutter.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a901d83736ff0afcba397183ec0b7723", "name": "Support Files", "path": "../Pods/Target Support Files/Flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d772e5c38186173c6b03940279877477", "name": "Flutter", "path": "../Flutter", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98879b304b0ef23e9e3c1ee5738832e87d", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984cc0adf01ccac9d886c50bd00e8ed7b8", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9803b3b2a8f001386fc6faa28904ce972f", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986d8f579cd7648b1dddfff2ef214a5684", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98976eb5fe6de6b95d577be52c4cff44c5", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bd98b7e74a5f759927d27118ba610e0c", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988775fa877a78fc7cdb595d4778c45c30", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989ac0ed67261e4d2f162aca8a23c2df63", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f9a19aee8d3447dd6c4776adab429905", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985891452b0aeaf06b09b7f43fddd0a8da", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982caa11a4d7fbd5ebb8d1a8cc59104531", "name": "tushen", "path": "tushen", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e6fef9c120b5354c89a6ec4db230b0e2", "name": "Startup", "path": "Startup", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9854044eecab768e3987f6e72b7ebff4e1", "name": "MyProjects", "path": "MyProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fdda69e262a75af958f40fbf035c0459", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981795b5fd40b7f7cb5670cc987dbf0d7a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerImageUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f3772ce940d947b2803f07adb45063d2", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerMetaDataUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98528031a8253e441ac9bfa7640c959930", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerPhotoAssetUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9885525df42874f7a7688396c3e6875584", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985801566e470ed72712226723977a6143", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTPHPickerSaveImageToPathOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9856d87ebef3952fe41e205fbd6f66473f", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9839e77ddfd06acd24b69b368d19bca7a5", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983584bafb818f5ae97499cadf03a81877", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerImageUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9836ce4742ffc30440c68e15c3455849b3", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerMetaDataUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98574392a5cd9811a082587a7867381148", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPhotoAssetUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eb3f25825004b86b537bb8452467280d", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9869569e76192ac6431bb906a06dab778e", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e3a4c91ef16343caba75b1295ecf0452", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTPHPickerSaveImageToPathOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9886b1571636f050ac18812a3e75739f43", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/messages.g.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d4d7dade157ad06904ee3f1be2a5d8f1", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dc68f61522b833a6826b672db2b927bf", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984e5c93406011375299f18aed009812cb", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9841ca5275bd2b28fb028fc37df0e82533", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bcdf4932a8fc933d53d672fac0804414", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985c981a44ed6c1b9e9344b6591eb6aa0f", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9888df6423c3c4b14cfd39bb0d4a72d393", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b0be5d8396f60714e3ca998be782af0e", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e673d26fdebb774e9eb6859faa87f77e", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f9c79295b858d2b229e8f2e7c7177943", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bf1dad1d4c7df9fcb5a820eecffe5b41", "name": "tushen", "path": "tushen", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988124435c7f6e75447c577f4511c4dfbd", "name": "Startup", "path": "Startup", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988b86560d92b51700a8f9f2a99bc09203", "name": "MyProjects", "path": "MyProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eba3adc083a2a781ace52ec0fc6e64b9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f4fd8ddea75dc6b5ce1139e227b76f33", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a6787e79b0aa515093aceb6dfefe9bd4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aa3d4d53c6065e36b34c342b017af714", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981619ea670ac431196577593dd8f88479", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9841ea40c2cedaf2a1c8f9ef2d4d055c2e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9888a7ca63a3f4318fdf38402d6c5a40bb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ebd2af064627a92ff82b30ae62e0551e", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98e9fac421ea0e973e9f41f64520496ab2", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/ios/image_picker_ios.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e986a8d42465569a439b613aec821ac605a", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/ImagePickerPlugin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98ae5d9d1ca2780cb258360bd9c2cafba8", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/image_picker_ios-0.8.12+2/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984c1892844d82cbef09fd3352c98eb8be", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9851fc78584e7ea350032d1b73213f4a08", "path": "image_picker_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98da92b1046ac448e93cca64efb5a25915", "path": "image_picker_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9858fef75a5bce69e9aca0603f5c9aa4a4", "path": "image_picker_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98688ffe30bebcad74f606adff44ce1a9a", "path": "image_picker_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c7eff6401e56ca021f25f03feb44d8a0", "path": "image_picker_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e984a8d015708e059f8f171291aa3703707", "path": "image_picker_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98075c3a7ea2d22df462713367e91e6a88", "path": "ResourceBundle-image_picker_ios_privacy-image_picker_ios-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985f2a2f0a3baa3f050b64425b3d68ec63", "name": "Support Files", "path": "../../../../Pods/Target Support Files/image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98416d55ea7cb5b621dd05e17e0d009ea3", "name": "image_picker_ios", "path": "../.symlinks/plugins/image_picker_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984698aad61054a007e01ab84ebd3dbc9f", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/package_info_plus-8.3.0/ios/package_info_plus/Sources/package_info_plus/FPPPackageInfoPlusPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9814c1b470060529465b30d4ee40ae8584", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/package_info_plus-8.3.0/ios/package_info_plus/Sources/package_info_plus/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b8edceb44fdef23b9691df60b1267913", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/package_info_plus-8.3.0/ios/package_info_plus/Sources/package_info_plus/include/package_info_plus/FPPPackageInfoPlusPlugin.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986697a94c5063d7325a7420dc11c630d2", "name": "package_info_plus", "path": "package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987f0d70e0a404c2ff8cf355aa23dd6d9a", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eed4b8bcee53de21a885b4f9a8e09592", "name": "package_info_plus", "path": "package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c5fee9fb0bf7925a8eb5af1f4b3fa713", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a4f9e806f7f275e4feff8b1927e12bb2", "name": "package_info_plus", "path": "package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a5b1359c7b563a4900e546142d1880dd", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fb1587ea70904aebd4edda9f0000c07b", "name": "package_info_plus", "path": "package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d136c49d87ba73c5f5130209906e2e34", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e86c4e3493ecc5941ca17c1e0ec0c758", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9829e10efbbab128993f24f6be904da460", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9899bca70227d7633c4cea88937647bf09", "name": "tushen", "path": "tushen", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986126448a4f4cad81b1a95b4a591ef634", "name": "Startup", "path": "Startup", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9862ad6b214af61df9354dff269e047027", "name": "MyProjects", "path": "MyProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d9c5f7fdced90d0c3c2403aa9eeaf2bb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98371c9e46d5eedda24acb3ab79c69be6b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9872e441d2b040cb679657b606602bebf8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b344ff4f57af7e30d6e41fa731b1c94e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989d6cb3ce8e094102dabec6d068d820cc", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e1aec557fc8aa77ba21ef5351d985f33", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ac735873fbf41e94a9777426f5eb0cdf", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98465ca0022bdb77c7b343939ee3ebfe6a", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/package_info_plus-8.3.0/ios/package_info_plus/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98374a9d4296a6d03575b4de7744f1e06f", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/package_info_plus-8.3.0/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d34ba161f4194f2e7aac62a830f50011", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/package_info_plus-8.3.0/ios/package_info_plus.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9818818aa0fcf19dad917afd9ce0b07133", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e989306ad4457f683d82101e5d5a4f80caa", "path": "package_info_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988c83d499116b8eb45ac4db3ef0e5d716", "path": "package_info_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98e0dd8e9719acf9aa558d5ed27e84f78e", "path": "package_info_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98365f03898f1e9972c88893bf683ce982", "path": "package_info_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982ffc17ef8348fa8aca0996c3fac24652", "path": "package_info_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d9befcaf56df45448e41474c5d0f0165", "path": "package_info_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b560934abb4b21a0fa3ea78e89998d50", "path": "package_info_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9893eb166db4453f2637ffd34af0dab383", "path": "ResourceBundle-package_info_plus_privacy-package_info_plus-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985ede1b7cfb9603db19eb1820aab65a8f", "name": "Support Files", "path": "../../../../Pods/Target Support Files/package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ca1acd53c0f38bf97c20e90efe3fff00", "name": "package_info_plus", "path": "../.symlinks/plugins/package_info_plus/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9851bdf9675820a195444033806ef30087", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98608bb2f73e5910ed54edafb25ae8c086", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eabaf3d7fd0a1d0faba764732951e1b5", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9833fc68013ec25e8454f8d656487069b4", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eabcabaa22a7a9a27c995e85179a199a", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9876368a2b9dbb30fdfabb40ae8b11dea3", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983594914af20c874b84c241f3bdc7580c", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98143f60442b2405f61f6c87752ac02502", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a21d617630a39244d82707681b8a1a84", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9884408fb9f9da714c58aa136db4f49dde", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989103533836368d3c44e5b5a1445b5253", "name": "tushen", "path": "tushen", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f4ac12aae576806a0db865b7b5161968", "name": "Startup", "path": "Startup", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98933ac90a20a41dec3284bd20c32a6a24", "name": "MyProjects", "path": "MyProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eda65b567c8c42f29548130a931c608a", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bbc819533470e67a35657d966d3fac85", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b9de27e2875356a02b3b912d707b03f8", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/PathProviderPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ad5aa3f187dd9101571dd02f2931f4fb", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980f92cd9c8679a0e12ab03cb3243b6874", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98999f4ae9bf5dd08d14852aab28e6b725", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dbfda26d62e49c62aa1a98106862617b", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9806f68fb53bd26240c1b519df94247234", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fa5e7ef936d0845b61e3c1ec32a45402", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98753d4239227a3076684d1468f25ce941", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fff87daf0e51839dcfeac6b6461ed665", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988ba88c124175952ac44daa249393de44", "name": "tushen", "path": "tushen", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985ca7ac112a9c565483631a635e25bb75", "name": "Startup", "path": "Startup", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986cb91a774a6a280f01cd59c5bbccfcf9", "name": "MyProjects", "path": "MyProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98369f3179cf3c9f7a37e27344f1133212", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983975c51709ae6ee224aa2906595e839d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988ce9c325af309306b4cc67e30163dd5d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9808c6ed297b1e8379302cc734a97f416e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c08c070fc7b686ba862186da9ea821f5", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982d4550914c016ecf48eae5f04ba1e3c2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f4936b97a4611fa96575e8e198bf183a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98376f51dfe11af59e7c9c027328f83125", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98e831ce387034a5a7d7f7aee9200ee09f", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9803f8045e2a3b4f001e6a544808900341", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/darwin/path_provider_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985ed85acc1f9f0e80768634b5bfb593dc", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98ebac82651206c6e2d526051c7c397219", "path": "path_provider_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98615f7dd2336c8a3dc26a66388719ca93", "path": "path_provider_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98d3f8ec14e279b44d6ff6d8fd68966d12", "path": "path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dd5c9e89541c2f382682d5071d495e33", "path": "path_provider_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989b7a9b943799dbe0f063472a208fc58f", "path": "path_provider_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e982446181d2a4d88d932b406721c36268b", "path": "path_provider_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9819801dd52affa6a25616e5f04a134719", "path": "path_provider_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98757b3829ad351402413c99e91befc248", "path": "ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b812b1c34899918f5ee2d016522a43f2", "name": "Support Files", "path": "../../../../Pods/Target Support Files/path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9894a69f97e9fcdcb5f1601a5ce880f646", "name": "path_provider_foundation", "path": "../.symlinks/plugins/path_provider_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98edaa13896c5abdfbd83bfbab7b7f4b0e", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/PermissionHandlerEnums.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98689efdf078bb7fb36686039c92e06e4b", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/PermissionHandlerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e934b44f3c5571730d78b0be715a41f4", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/PermissionHandlerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983a527a654c5342321c7db4516162309c", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/PermissionManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98590f460df5ee43004ccfc310855ee6c2", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/PermissionManager.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cba934023c11175e105ae96cb5039185", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/AppTrackingTransparencyPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98694c2bbda2994197dc8238ea8e3b7b49", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/AppTrackingTransparencyPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ff2977c2e1264953fb1e1aeac2f55781", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/AssistantPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e755d068b2b1a8feab5a29dbfc929665", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/AssistantPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982dd617c93f0b9ab82cc41a84d17c9ae5", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/AudioVideoPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9890dcf64f2c45e8d0c460a82ee528a8e6", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/AudioVideoPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fb4369b29542cd382cc72c6b89ae80a6", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/BackgroundRefreshStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9895a5ecfd2d0a6150afbe4bc97448caf7", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/BackgroundRefreshStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c5bedf3b7db353cfd48683f4090c81a2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/BluetoothPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98238a24771e4ea116c5d864aed5f4c08a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/BluetoothPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98febda2ee4400b768e699489b69f6ae4a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/ContactPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98da70dfa6da72ae2c44d51ba837708a1c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/ContactPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988646847e52460b7a9389190244d069b4", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/CriticalAlertsPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9857bbeba27d246eee6acafa1b02014252", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/CriticalAlertsPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981c33a6eb1852ee19e6d50235e4fd5db3", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/EventPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ca4ae6454c7de5665466ede3ed890a53", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/EventPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984c150a8d0dd1bc4bc322932722ff269d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/LocationPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989cbd378470758494378d7686bf750c5e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/LocationPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98617a16018c24019f0442f744cca4b17a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/MediaLibraryPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986a0ee65846dcc1b15e5bf8e6f872584f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/MediaLibraryPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98338e5a2d78c0a2ce16e4991932be5163", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/NotificationPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98475ac109c6329f499406ab0086544568", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/NotificationPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ff879f7508b26b69356a9625c41ae2b1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/PermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98837cbcf5415c9813c02fffcc80986fc6", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/PhonePermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9857ee7cb723a0fac15c9f34543c5f1f47", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/PhonePermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98654a3ae1a83f7cea2c9a2e4f17431235", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/PhotoPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98432c878870daa11de8ae63f549c4e129", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/PhotoPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9868cb4d00f425fa96cc34b6d1499ff901", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/SensorPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988fe227b9801c382337cbb86b50bd2bc2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/SensorPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a9c15f6c1352bfac0c433e1e6e2c5041", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/SpeechPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c011182e47bd2f6b3867e85ff668e576", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/SpeechPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9856dbfde31dbb8454d393bc57eac715ed", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/StoragePermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b0418d03b9d5d6b7e69276de7186e262", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/StoragePermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9828d68baa910661310565862c0edc65e7", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/UnknownPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981b620d1c68b48c56e3c2ad51e3496381", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/UnknownPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98234a78f753d5320b60fb6095cdba658c", "name": "strategies", "path": "strategies", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e6be4a9e5657491114bf82ff0dc0c0ff", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/util/Codec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c012544cba32a2b0cc4013acc33db75f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/util/Codec.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9839aa23a1665c032158bb7e3e0c44b014", "name": "util", "path": "util", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989a1ae7967251ff18d8749a96d041e082", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98df4fb7ecd3553f8d827cc4ec1ff65723", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9859ed0db72a0b7ee40f674d0c683f6967", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98098b3450c37de1e13ebafd067126e68c", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982eede8e8484adf9b2a9e80e611a73f89", "name": "permission_handler_apple", "path": "permission_handler_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986e674095a3ddea707c46ffcac726aab2", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f548e70a8194eb08bf20a3fe6e2fedc2", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9867685692ef9031608c3a21ed35f032e2", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f18fa5dddd29a9371ef84a700a515058", "name": "tushen", "path": "tushen", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b65f067d9851a018da8d4360d8b2e407", "name": "Startup", "path": "Startup", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bcbc4e8770167364bb6617751c8d4381", "name": "MyProjects", "path": "MyProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9803c6d09d775d9e89ed24173b3b8d25fe", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ba53fcdf80117f3cf275b2ca198bbe7a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985824d5bbad1460a38cd2c152965ca112", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9814c017df0488eff83bbd2fbe91414695", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e82194697333ad9407883cac6368bfd5", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9855980299bcf676e15e70ba14c64781f1", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9880cb022a278a31f66af5545f358ed06f", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98402e0d288f02d44718f529dd962a4adc", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/permission_handler_apple.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9815e75f807bbe89d8791f974646072dff", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e982ac772fa34e23f3eb87d4991c779d51d", "path": "permission_handler_apple.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981d96bcceef3baa4cc4d5946a064e4732", "path": "permission_handler_apple-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e984727922769d10c6af31dd2833b69a4ff", "path": "permission_handler_apple-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9834b829319c4abd0d9b5c3ba27726889c", "path": "permission_handler_apple-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9851e6761ea5e017b154198b4e36131c0c", "path": "permission_handler_apple-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f5c75fc475791974dc336e8a35ca4f66", "path": "permission_handler_apple.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986cb66ff42dd1cc134900e2058ec5ed1d", "path": "permission_handler_apple.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98098409cc438f5aeb2130e8feb886d2bb", "path": "ResourceBundle-permission_handler_apple_privacy-permission_handler_apple-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9867da52df9806d3d0bc5d419c20e1ca61", "name": "Support Files", "path": "../../../../Pods/Target Support Files/permission_handler_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98857f57167dce31b885d6175db4294c06", "name": "permission_handler_apple", "path": "../.symlinks/plugins/permission_handler_apple/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981c6f07abedddf8fe4293d8627a3ed8cc", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/PhotoManagerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989259ab8c9f0a4e8cf0a2936d3f48850b", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/PhotoManagerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9881f61618f8ab4cc89ac94e6799e8270e", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/PMConverter.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982863b1ccd921d1d330e88f351bc38509", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/PMConverter.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98696a860f31712de2e8e3e99bbabf2f88", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/PMImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a63b39a7c4af885f96b754203b81d2ab", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/PMNotificationManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988248f3de770cc92bd45d87a524c19b2a", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/PMNotificationManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985972aefed2823e6f85993bdc2dd8859f", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/PMPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e48cfddce812839f37f364151606aead", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/PMPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a727775966b696724e530acb616a37d1", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/PMProgressHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981879a973cb51bda3419bbd9e92923737", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/PMProgressHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9893620934c28cbf79315f7c129450d5c4", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/ResultHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986323bf6e4d79bde068cd211c82b301f6", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/ResultHandler.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984d18def1f80417008a1d0422c4549421", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/AssetEntity.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988e2d2911aa1764f439cfa98a2b1ebfba", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/AssetEntity.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988456e5f7a23fda77fda38a134e3a7130", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/NSString+PM_COMMON.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9893706eba73ba95892089af13e3e46a6c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/NSString+PM_COMMON.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9884db4f36167920a80dac58c7f8582348", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/PHAsset+PM_COMMON.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b0ae54e86e89d8d4f7d2b36a219322fd", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/PHAsset+PM_COMMON.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989b20a2ed18c9f17f24ee64909fd6d224", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/PHAssetCollection+PM_COMMON.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989b23e546ae8e70e9cece2f996dd62e5a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/PHAssetCollection+PM_COMMON.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98885067840a4fb896ae0250987e87bca1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/PHAssetResource+PM_COMMON.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985a71fa9afc1aae19e78112451eca4f0b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/PHAssetResource+PM_COMMON.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98187cf215a2507b1e6cd87d0255bd8383", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/PMAssetPathEntity.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985b3ad3f7bc82139c1ef47cf7f34e4421", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/PMAssetPathEntity.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c5a714214b70857e1624b29a148d216b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/PMBaseFilter.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987263036b388759d4a8c4f454bb389e6e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/PMCacheContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984b5e2ae6b11543ba5f5c0cab5f04677d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/PMCacheContainer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9835f19626404ee65d5bda3e8a154804d7", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/PMConvertProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980766d3bc9b2ca4fb48e8ca891a750324", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/PMConvertUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9875eeaa667d08baa497d51ae1be44fbf6", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/PMConvertUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986f5e337d2cfdcea41772ee08a032e50e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/PMFileHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9815c1a3f70b5da169c17ba70a3a1f0e27", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/PMFileHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983dd695de80854ebe95a69fb702e4e75e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/PMFilterOption.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981117486150f16cf7cb0d07011503d095", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/PMFilterOption.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d049fe14ae5b21465cf02430b138b113", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/PMFolderUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dd2bc865e46b764e8d6bcfc7c74d42af", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/PMFolderUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986a7edd5a4d2a72d5077e4c42d8f80bb6", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/PMImageUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98574cf07b0c0925539c0406ff5cfed17b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/PMImageUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b47d2a552caff6a631ba44a79dbd64a3", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/PMLogUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9868b743e581913224099871b924e2a45b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/PMLogUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d20955810ecd9deb9208fff773cefae2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/PMManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98693c252991baab940ace01151f786f60", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/PMManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9812be8af4c31012c1fd903df743cd272d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/PMMD5Utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9857f8fc6c34b043cf188a147d6aa7d8ca", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/PMMD5Utils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98605d559bcfa2b9108e3003bc9d4c7dbb", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/PMPathFilterOption.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d45877ff3dfa993034956eda8985cd32", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/PMPathFilterOption.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f7e1dac5a45f671ee6d9b8e8b0356d3a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/PMProgressHandlerProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980211c987447e7c6e6517d8fbdcf1965b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/PMRequestTypeUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982b8e8b512044d9ebd79efc49a9938777", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/PMRequestTypeUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fe86d5f636329ba0bb94cc474486fc7a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/PMResultHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985fb9f59773f4b04b667dba1f5e17508c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/PMThumbLoadOption.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9814dde7547c2849df5cc50a625592d71c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/PMThumbLoadOption.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e92c1c3b3360ca87e9da7b9a3b1805b8", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/Reply.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984a89dd86af26e4b416f1254bf09f6c10", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Classes/core/Reply.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986c80e3d2101cc04c975de25a137a0ae9", "name": "core", "path": "core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c666fb7ee5b1a8dcfd686d567d524624", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e988496d3d92813695999e3a5e664c8bff4", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f774196059442778ada2c4ffdf8413bd", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f58db0be452b10f55c2aaecff3d59d32", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b2967542d98fda8aa17d1cc667471194", "name": "photo_manager", "path": "photo_manager", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c5b6d7c25ba741e0b0048af49c0075b6", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989dfcbbd4e67277ef48045da7ea24a3bd", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988c332069400d4f1bece076a694e400b5", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b7996e828d155c56550696978d790f0f", "name": "tushen", "path": "tushen", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98037cdc04c0e294d31f14cfa8700c545c", "name": "Startup", "path": "Startup", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d9e1d17b099c9168ddce04da565ab657", "name": "MyProjects", "path": "MyProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986bb26ac4315da12c384247aed36d9b65", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f8deac25101ace3ba2dfc7e42ae237bd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98be6d3092732553e7ff4318ea5284b8b4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9880d7dbfb5015907fd9e15219b5d3a5c1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9847b164becfd7e9946380ba9ea6c7b09b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9821c6b976bcebc51a49b0118964fbc7fd", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98cb2be4b93f36d9506b9e85f245c81d0d", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9893569d787b3200a4d089f350aef35580", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/photo_manager-3.7.1/ios/photo_manager.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986c1d32bcff53c1cbb6b6fa9f8e28d5ad", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9893c7a019c917e117520441cd033fe55c", "path": "photo_manager.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983f248096b4c9decb5b28682f7daac440", "path": "photo_manager-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9828be9f6b19d8acf4deebe51f8ac440dd", "path": "photo_manager-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9873a961f44162dde1c08d6ee717729231", "path": "photo_manager-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98af0c817c3897c7b54fb4ce8854c7066d", "path": "photo_manager-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e982e175e3b91fa2389d9657743eab734f9", "path": "photo_manager.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988798107b1c57f5a1d8a01bc28d856810", "path": "photo_manager.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98951f7be814a7995d7bb95f063be9d959", "path": "ResourceBundle-photo_manager_privacy-photo_manager-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9876050e87abe90297611ee3e46319f4f6", "name": "Support Files", "path": "../../../../Pods/Target Support Files/photo_manager", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bd5613346de63e295701c0ec4591f0a1", "name": "photo_manager", "path": "../.symlinks/plugins/photo_manager/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9893f2f505716f9acbad3e1fee139592e3", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98215af633ce937913a5cd57d1a20247cf", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982e1d8963bfd58c2a4dc66d7fc37d84d9", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986894f789e72e6f5611719e29afabdeda", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9875d6542c4b6158d6eb67d8197c2e179d", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98644044eb2b5b4c12376c63f690876bf7", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98651bfbd690ba1db7174e9f6c8c02c6a4", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ae0429b90e68d95c3cff01c692c3987b", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dd566a9236302e1bccd700b0041a9ded", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984650902d2e81d4bc402fb2b26f22fbbf", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98735264b7703a410c0e96c87d53a7068e", "name": "tushen", "path": "tushen", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989bd0b2bd77cb2251e817e4e088865ee7", "name": "Startup", "path": "Startup", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ce702d1e9dd77b3291cf8a2d6d56c5de", "name": "MyProjects", "path": "MyProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e00bfe1af6818e1b6952616b69df8dac", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985eeecc02d323773686876c8190023eaf", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987d663eacdca0b594b29469ad15e99918", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/SharedPreferencesPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98341748f88fd2a491e6bfa1ba75582ee1", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9813c7948dbcec86565972eceb3f1772b8", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982671a425762657add8e28f893da769d6", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d35bfb4c70d95f39417ef9f01b8a307a", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986de372549977991077f4aff9c9784445", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e365f343d98ce3ab5efcc71ec13b4b65", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a47bda8d712c42f54d1f40d0b6f0e39f", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9820d6ae6952036a2192976b15eb6ed71b", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9887196217a4404efd3c1e01c42e6411e6", "name": "tushen", "path": "tushen", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985172c98e41d48cb51779e954d72070d7", "name": "Startup", "path": "Startup", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989ab350e77dcf3b912c03cb5b412ed7ce", "name": "MyProjects", "path": "MyProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986c17b4d9f5b65a09b00d62e3370cfc42", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985f7b40f4d6b7d44b2ff41bbd21341a3c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98833aba8698c41b5444661c2edb78d09a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b74736f87b9e9bec5d2e9d1043e9921d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98be2e8347be1290eef6030374fbab09ae", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9861b455bad8c39600fc1d784bb3b46a26", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984ad2aee9fa05a9c30db7a7aead35f269", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9824a8ebfdfcc7fa68229d2af10e934b96", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e980c2abb956676697a232e004dab89bfd8", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_foundation-2.5.4/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9891e68e7e34d9723f2b69b02d45fd7c5a", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981d1a9c28df09eb88d2cd049a04ef1a73", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98826bef9e3e82bc965a83c8618a608ee8", "path": "ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98d30835831795f6064f36bb4ed297d5b9", "path": "shared_preferences_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a399713304d52b811ad88545b43e57f6", "path": "shared_preferences_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e980626196004d443bcfe4af46092720f28", "path": "shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98536bb822ddcdd19ca36242a34912d063", "path": "shared_preferences_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9825577c6876dbe1eeb44022c4c708e541", "path": "shared_preferences_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989b6c8a4ca09da23b5f741b2e5a44056c", "path": "shared_preferences_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b4ab0f54f31f988205dbdaafe008927c", "path": "shared_preferences_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f85e3047cbb1930059d4239af46bb6f7", "name": "Support Files", "path": "../../../../Pods/Target Support Files/shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981344d44816854159bf7c3598c16e620e", "name": "shared_preferences_foundation", "path": "../.symlinks/plugins/shared_preferences_foundation/darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98190e97435188248d1770b26fd3ce6df1", "name": "Development Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e9807daa3f9265322026266a25d0045b5bb", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/AVFoundation.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98d7e2dd08eff3fdc559a3f5fed1d9957c", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/AVKit.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e987b9e1c3b6563a543b6fbe5f0759aef8f", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e9884489dd7a344f146541488ed3c691894", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/ImageIO.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e9812cc64573a371c51e6cdc1c4fd688b8c", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Photos.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98e4312a17bfed5f03cd67f84d23bdd541", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/PhotosUI.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e989d11e2d400847835a96dea825dfa8835", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/UIKit.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fa56320d8a9b34b0484b7104e1ad68f0", "name": "iOS", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9818a9377e250b9d121296a9203d9c1d90", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9833d01cea44331bda1c800eda7b935ca0", "path": "Sources/DKImagePickerController/View/Cell/DKAssetGroupCellItemProtocol.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98512c98536ba573a67ee198b5633d1e06", "path": "Sources/DKImagePickerController/View/Cell/DKAssetGroupDetailBaseCell.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982209b7553c41bc450d59de2600e4d0ba", "path": "Sources/DKImagePickerController/View/Cell/DKAssetGroupDetailCameraCell.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9809ba972982903cd370c2830b507f7cdc", "path": "Sources/DKImagePickerController/View/Cell/DKAssetGroupDetailImageCell.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989527d8513b31207fb5df20aae2636849", "path": "Sources/DKImagePickerController/View/DKAssetGroupDetailVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e6330c9c6e21a4664f5beae366c00a92", "path": "Sources/DKImagePickerController/View/Cell/DKAssetGroupDetailVideoCell.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9895da6c6373e615c51e15fcb4aeeb73df", "path": "Sources/DKImagePickerController/View/DKAssetGroupGridLayout.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e0adc46b93f26ecabb549d2d494ef03f", "path": "Sources/DKImagePickerController/View/DKAssetGroupListVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b7af9bfb8e10345c1a1c53f585c3c04c", "path": "Sources/DKImagePickerController/DKImageAssetExporter.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985f83eb8f1827962c624c40e4e7e70882", "path": "Sources/DKImagePickerController/DKImageExtensionController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983f599ce34b9c5181fb8b30a916d4c1d0", "path": "Sources/DKImagePickerController/DKImagePickerController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c17123e03ebe0c296d573c6f10fb58e5", "path": "Sources/DKImagePickerController/DKImagePickerControllerBaseUIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98383f6f8bf116a9145a72ca004f627102", "path": "Sources/DKImagePickerController/View/DKPermissionView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f21122349a140b585d33ef23e0d9b181", "path": "Sources/DKImagePickerController/DKPopoverViewController.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9885892f61a9301757d399809f28e45f27", "name": "Core", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988f763bf4ea79de6aab5b94580ae10b71", "path": "Sources/DKImageDataManager/Model/DKAsset.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988cc10f8287258333398e1ad06a4a08a3", "path": "Sources/DKImageDataManager/Model/DKAsset+Export.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ca1aaf1416f26af2ae5f25634becd9c4", "path": "Sources/DKImageDataManager/Model/DKAsset+Fetch.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987b730b9fab9540947d4efc3914a5285a", "path": "Sources/DKImageDataManager/Model/DKAssetGroup.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985dd48177b19451cf5bb2e0df6e574be0", "path": "Sources/DKImageDataManager/DKImageBaseManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98995650deb78ce16a63a4e17b69f74325", "path": "Sources/DKImageDataManager/DKImageDataManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982c9340f0d7ca2817eb5340d37b215ec2", "path": "Sources/DKImageDataManager/DKImageGroupDataManager.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d7bb85edecaa79dd3e26138ffb9bb9cb", "name": "ImageDataManager", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98343fe56404bd760cbcc263e8b9fdd9cd", "path": "Sources/Extensions/DKImageExtensionGallery.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cc5cc9fb3bd374d9691868af551127d4", "name": "PhotoGallery", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985891ab7d7e3827776ba393adc0b4b906", "path": "Sources/DKImagePickerController/Resource/DKImagePickerControllerResource.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98a292699c7027eaa680bfe5da607e708f", "path": "Sources/DKImagePickerController/Resource/Resources/ar.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9852b8cf46dbb173cb75a3b48bbfbb8858", "path": "Sources/DKImagePickerController/Resource/Resources/Base.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9835648ce799d62afd8c7500844a76b60e", "path": "Sources/DKImagePickerController/Resource/Resources/da.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e981ff058d7b390b6f2003ce67e8d91cd71", "path": "Sources/DKImagePickerController/Resource/Resources/de.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98defb1e32e9bd8aba19d9a67f858e60b0", "path": "Sources/DKImagePickerController/Resource/Resources/en.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9808a0a427f1edd3500994a26f3b1b4614", "path": "Sources/DKImagePickerController/Resource/Resources/es.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98a490e75903fb2b0a8fb899a90d4287ed", "path": "Sources/DKImagePickerController/Resource/Resources/fr.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e987c76b719348323ba9f42f0d81d70b161", "path": "Sources/DKImagePickerController/Resource/Resources/hu.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder.assetcatalog", "guid": "bfdfe7dc352907fc980b868725387e980962a367e6152e96b70a125d815020af", "path": "Sources/DKImagePickerController/Resource/Resources/Images.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e980a11f800dc26d56426f6b37f8a283b0b", "path": "Sources/DKImagePickerController/Resource/Resources/it.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98b08b2c243127aba3d1fc2398f7ba08f3", "path": "Sources/DKImagePickerController/Resource/Resources/ja.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e989796f8ce5b602046976a913145a66f6c", "path": "Sources/DKImagePickerController/Resource/Resources/ko.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e986968906746efa810a96cf74bfa70670d", "path": "Sources/DKImagePickerController/Resource/Resources/nb-NO.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98936c99140c6d493cc2084861c68133a2", "path": "Sources/DKImagePickerController/Resource/Resources/nl.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98c9017cb198b86bc9ed0cf016c9d298fc", "path": "Sources/DKImagePickerController/Resource/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98135e59325df8adc673d9b81eb08333fb", "path": "Sources/DKImagePickerController/Resource/Resources/pt_BR.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98edc8787438970f71c64e1d428d742b0c", "path": "Sources/DKImagePickerController/Resource/Resources/ru.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9878653648422e683002192a6134cd98ad", "path": "Sources/DKImagePickerController/Resource/Resources/tr.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e982c46c4af4d494babb82844bf0d68a4f4", "path": "Sources/DKImagePickerController/Resource/Resources/ur.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98fbca80ed18432ed6f1cbccf6775b30bb", "path": "Sources/DKImagePickerController/Resource/Resources/vi.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98b1e46671a6db2c214e25ea65b3d2809b", "path": "Sources/DKImagePickerController/Resource/Resources/zh-Hans.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98f7b40e8c8c2cda51e62b5c5e2d2129be", "path": "Sources/DKImagePickerController/Resource/Resources/zh-Hant.lproj", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9815aec640e5fd0f105dc3affc62678a5d", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9847377bb9ded10a006b1c08b7d89c7b5b", "name": "Resource", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e981c36de5e345aa6086781a6b25a5ce4ef", "path": "DKImagePickerController.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9867fcaca65682c24521cf1614ac18eba0", "path": "DKImagePickerController-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f6c1a17b71230f44ee7b5f910f599795", "path": "DKImagePickerController-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ec2edc2a95b02b354c93b2e82552fdaa", "path": "DKImagePickerController-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985ab2e42ee779365312a2e5508ae2de1e", "path": "DKImagePickerController-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98e627b81f2277a26392e82dc437b640b9", "path": "DKImagePickerController.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e984b6d27ca3f133a8809ce8858835ba048", "path": "DKImagePickerController.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e981cce2b3ddb6697929cc0d9476faf0651", "path": "ResourceBundle-DKImagePickerController-DKImagePickerController-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9899a52a0c7edba1510f77b848b8dbe12e", "name": "Support Files", "path": "../Target Support Files/DKImagePickerController", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985e36f3b5aa67b15d66ec13e17d5c6f7c", "name": "DKImagePickerController", "path": "DKImagePickerController", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98051e74ac2dfb008bbd683e30491aa7be", "path": "DKPhotoGallery/DKPhotoGallery.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9834f1d1fb0dd3d6538989f7502ec1644c", "path": "DKPhotoGallery/DKPhotoGalleryContentVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9825db5c0b0de22f7488309691c8541b75", "path": "DKPhotoGallery/Transition/DKPhotoGalleryInteractiveTransition.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983aecacec943b3b7fb13ca4f783aeb124", "path": "DKPhotoGallery/DKPhotoGalleryScrollView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982683856913254da4512b6e02f6e4f14f", "path": "DKPhotoGallery/Transition/DKPhotoGalleryTransitionController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d9552c70edc7387c0167f655a2f9cab9", "path": "DKPhotoGallery/Transition/DKPhotoGalleryTransitionDismiss.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a0333415fb47d981a93980d7783a25bc", "path": "DKPhotoGallery/Transition/DKPhotoGalleryTransitionPresent.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987d2693191bcef035b93392dabd29e915", "path": "DKPhotoGallery/DKPhotoIncrementalIndicator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986504caedde90e4b7bbf90bc3c93b56f5", "path": "DKPhotoGallery/DKPhotoPreviewFactory.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982f9d7a9efaa218339cd035406810a89d", "name": "Core", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982aa0b02535812fd0e603a89bcfb30ef5", "path": "DKPhotoGallery/DKPhotoGalleryItem.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98609e92aabf035e03bb7e4a151d701ba8", "name": "Model", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986cd8b63f1efadf20d9ea1ed8f5a55c59", "path": "DKPhotoGallery/Preview/PDFPreview/DKPDFView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d637eecdfef95bbdf30369120352bd46", "path": "DKPhotoGallery/Preview/ImagePreview/DKPhotoBaseImagePreviewVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9834be2bf990d387cbce4913d90fc0866d", "path": "DKPhotoGallery/Preview/DKPhotoBasePreviewVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984767f7ebe824e56e6af065bd8c127fb1", "path": "DKPhotoGallery/Preview/DKPhotoContentAnimationView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9864c787672805f0b55134cb199dc71c0e", "path": "DKPhotoGallery/Preview/ImagePreview/DKPhotoImageDownloader.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98850d9e8cfb22b221e8e383807ea38a07", "path": "DKPhotoGallery/Preview/ImagePreview/DKPhotoImagePreviewVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a5b79d94ff93ec63566064eb357dbe2c", "path": "DKPhotoGallery/Preview/ImagePreview/DKPhotoImageUtility.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9830b4739ebde111a52547b4d18a503410", "path": "DKPhotoGallery/Preview/ImagePreview/DKPhotoImageView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c7133de307e021954fb1ea816636ae53", "path": "DKPhotoGallery/Preview/PDFPreview/DKPhotoPDFPreviewVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9817c885c72c438eb48d3c624d21c32879", "path": "DKPhotoGallery/Preview/PlayerPreview/DKPhotoPlayerPreviewVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98119755cf3c3ab367d61b1f6a2b919fbf", "path": "DKPhotoGallery/Preview/DKPhotoProgressIndicator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98dcde9c1fdc32784294392718f0d45ce0", "path": "DKPhotoGallery/Preview/DKPhotoProgressIndicatorProtocol.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e630bb3219120e5c9a7785f505fc0eba", "path": "DKPhotoGallery/Preview/QRCode/DKPhotoQRCodeResultVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c3eb35d57bc96b724d16fb6a815089bd", "path": "DKPhotoGallery/Preview/QRCode/DKPhotoWebVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98807401bfb1caf2d2d87935ce9df53b6e", "path": "DKPhotoGallery/Preview/PlayerPreview/DKPlayerView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f62a4ebe888f2b21588dca632385955b", "name": "Preview", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b13723b07b108638ef45e70068037a0f", "path": "DKPhotoGallery/Resource/DKPhotoGalleryResource.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e985e10b359dd12b7f39fea9ff06ea97d76", "path": "DKPhotoGallery/Resource/Resources/Base.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98a35234dbbe4590b9a85e7457f0c28ec1", "path": "DKPhotoGallery/Resource/Resources/en.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder.assetcatalog", "guid": "bfdfe7dc352907fc980b868725387e98dee9c8b9a30b693ced32e187ebf0f900", "path": "DKPhotoGallery/Resource/Resources/Images.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98c41ed424f75c34475286f94a0c89533f", "path": "DKPhotoGallery/Resource/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98561ed8ca05356672713bcaada5ce132e", "path": "DKPhotoGallery/Resource/Resources/zh-Hans.lproj", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fd589bed63dbd8bebe1a5c6ebd77fa7f", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9810c721d9ccf7775a1e865973b53124dc", "name": "Resource", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98f4368a401dd84634730b8a7ec311ca2a", "path": "DKPhotoGallery.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982675e10da08f11651d5e552f26200541", "path": "DKPhotoGallery-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98dfeffaafe77f0a9f12afd0af803aa659", "path": "DKPhotoGallery-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98067efb6a104a1e6dd64ab5a67dca02ce", "path": "DKPhotoGallery-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e61c3706c4c5522b7ecd10e962efb922", "path": "DKPhotoGallery-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ae529f76a1aeb84aed676cbc438f2337", "path": "DKPhotoGallery.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e987c0ad1a68339e983c25e4de21965f4a8", "path": "DKPhotoGallery.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987dceea10fa683c352737bd3e00a06fe0", "path": "ResourceBundle-DKPhotoGallery-DKPhotoGallery-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98168ac8d18abf7c14f2544af5037dabce", "name": "Support Files", "path": "../Target Support Files/DKPhotoGallery", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f88f8e668e9a71e7a3b166e80eca878d", "name": "DKPhotoGallery", "path": "DKPhotoGallery", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984ef5f54b72a7372aba1e579dc0ccd846", "path": "SDWebImage/Private/NSBezierPath+SDRoundedCorners.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98faf397fda2ddaa2ab86308bb1ccfbec4", "path": "SDWebImage/Private/NSBezierPath+SDRoundedCorners.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983c9e4b25cc4f3d4a01ef0e2732f2904d", "path": "SDWebImage/Core/NSButton+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982bcd01b2e7d47428849eea60ca087ac6", "path": "SDWebImage/Core/NSButton+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986cf82ded5cbcd0aedb1e047492bd59f4", "path": "SDWebImage/Core/NSData+ImageContentType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9868cdbe67df46143e9f745fd310463034", "path": "SDWebImage/Core/NSData+ImageContentType.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98327653faf119453e0c6aec2865862035", "path": "SDWebImage/Core/NSImage+Compatibility.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d9df8e99e47daa9bb4a03befe01dca9f", "path": "SDWebImage/Core/NSImage+Compatibility.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9822231aaaedf7df013e4b108e0e6fd285", "path": "SDWebImage/Core/SDAnimatedImage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983575e840ba29943289aabf8a52b94f3a", "path": "SDWebImage/Core/SDAnimatedImage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989aaed392d2dd6ac61db9e4ecbcf25857", "path": "SDWebImage/Core/SDAnimatedImagePlayer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d178aebc944dc6c1f7c17e580cb3bcde", "path": "SDWebImage/Core/SDAnimatedImagePlayer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ac24602a43c5bc279fcb6dc508107692", "path": "SDWebImage/Core/SDAnimatedImageRep.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a85f20575a80a01df973b5cbf5643a84", "path": "SDWebImage/Core/SDAnimatedImageRep.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9881d7487e23dae3c1d8ac7dcb1c48647e", "path": "SDWebImage/Core/SDAnimatedImageView.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9862eafd0df198f795a83399156bdb5c8b", "path": "SDWebImage/Core/SDAnimatedImageView.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9886e56f60d034e013f9166c77217fcfb3", "path": "SDWebImage/Core/SDAnimatedImageView+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989df1ff4fee00b35104ab2a52832eebba", "path": "SDWebImage/Core/SDAnimatedImageView+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f9a5a95c5243aca73f52fcb99bedfb77", "path": "SDWebImage/Private/SDAssociatedObject.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987b02c0c60bac9857ac732ccb8f55413f", "path": "SDWebImage/Private/SDAssociatedObject.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986afa174c0865d30180b368d609fc3de1", "path": "SDWebImage/Private/SDAsyncBlockOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98aca48a01b7c7bc45ef1d357396fd629a", "path": "SDWebImage/Private/SDAsyncBlockOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e09c4624c6ccb84dd7bab33cab629301", "path": "SDWebImage/Core/SDCallbackQueue.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982364b563b9ac22c675af6b5fe9f43c50", "path": "SDWebImage/Core/SDCallbackQueue.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c2f34eec7a795f7c07394fc03a4ac660", "path": "SDWebImage/Private/SDDeviceHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987821323a4b6f3a5493e2b890abbcb6a6", "path": "SDWebImage/Private/SDDeviceHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983201c8cd9c126cc9377419cd3f8c4a17", "path": "SDWebImage/Core/SDDiskCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98482fc6a6b713546225738d7280a6c08b", "path": "SDWebImage/Core/SDDiskCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f049625c80b215cfb859bb6622378c70", "path": "SDWebImage/Private/SDDisplayLink.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988fdd1e56038299f049d7cb11a34e668c", "path": "SDWebImage/Private/SDDisplayLink.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989d9b8b1b181883bd2716674d7e89a51a", "path": "SDWebImage/Private/SDFileAttributeHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ea810e1ae0e285fd22f12b795cf1b242", "path": "SDWebImage/Private/SDFileAttributeHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f42eb8c3275d61fa716365f46906e0a1", "path": "SDWebImage/Core/SDGraphicsImageRenderer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9817c2de7aab139a33ae091298d11321f9", "path": "SDWebImage/Core/SDGraphicsImageRenderer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981032b2d7cd4789daa0f0fcdd59c4d604", "path": "SDWebImage/Core/SDImageAPNGCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980958ea84ee35085a9f320ef5e0f05571", "path": "SDWebImage/Core/SDImageAPNGCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c5d0f846b430b6f8723de77f32af5bfd", "path": "SDWebImage/Private/SDImageAssetManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b75580672e2e2383ef6302cc60789918", "path": "SDWebImage/Private/SDImageAssetManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f84146dab4beca70d81974a04abac1cb", "path": "SDWebImage/Core/SDImageAWebPCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9893e29eda59fa7c9a1f2b2bdb7aed75db", "path": "SDWebImage/Core/SDImageAWebPCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c581bf9c90beaf11315253e12401a23b", "path": "SDWebImage/Core/SDImageCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a4d1414bcb02e12c0e0c6b41f90d6b7a", "path": "SDWebImage/Core/SDImageCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98583a2ae7c823fa86fd9bb9870321df3b", "path": "SDWebImage/Core/SDImageCacheConfig.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f7cb45568762457f2bacf0642da55aa7", "path": "SDWebImage/Core/SDImageCacheConfig.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9817d62cf4923abcd8b6fd8ecf30d48623", "path": "SDWebImage/Core/SDImageCacheDefine.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f4f42e467a72f15b19589f73b8c32ce3", "path": "SDWebImage/Core/SDImageCacheDefine.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9866d7d0364c3a68360aceb3fae390a64b", "path": "SDWebImage/Core/SDImageCachesManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982adbcecce1f16d5963805e0c7f9e8a31", "path": "SDWebImage/Core/SDImageCachesManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c9ab6ca5f18e88eb87cb529b2c44848b", "path": "SDWebImage/Private/SDImageCachesManagerOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fe6cb2cd358b8452fa7f0746fbd8cd27", "path": "SDWebImage/Private/SDImageCachesManagerOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9871e7be9d188bdd022c9c6e49bad46115", "path": "SDWebImage/Core/SDImageCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988b44bdc5f2017bd94d0a3838a62a182b", "path": "SDWebImage/Core/SDImageCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987dcef8efb32a04b196f8f144ebce9a01", "path": "SDWebImage/Core/SDImageCoderHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9819e8998b7e4bbd6e3420595fc4b326fb", "path": "SDWebImage/Core/SDImageCoderHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ba9b1cd892443a44bc9e3324544d1f15", "path": "SDWebImage/Core/SDImageCodersManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985b19b4bfa089e7106c94628ca5ac2c1e", "path": "SDWebImage/Core/SDImageCodersManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983648b79bc2fa5969bd82c41bf0afa90a", "path": "SDWebImage/Core/SDImageFrame.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981ee9a9df4767619f2e3ce6521f5035a0", "path": "SDWebImage/Core/SDImageFrame.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9815af565ced1a1e5100ee0a0b1ce0298a", "path": "SDWebImage/Private/SDImageFramePool.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ed85219c2804b7403565111017594d12", "path": "SDWebImage/Private/SDImageFramePool.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b36c899130effac0e989fda899725890", "path": "SDWebImage/Core/SDImageGIFCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982cd070ae60bfa814cb203c5c4bf49b33", "path": "SDWebImage/Core/SDImageGIFCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ecf87ca10c1cdb10fc883d48a97decbf", "path": "SDWebImage/Core/SDImageGraphics.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e11dcd089c21bbc3922722a7462c34ad", "path": "SDWebImage/Core/SDImageGraphics.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98442143b143b34c80206816e52470e04f", "path": "SDWebImage/Core/SDImageHEICCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984fddf5da66f5aa4e9dd4b486c2eb0b03", "path": "SDWebImage/Core/SDImageHEICCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985550d5493f17390a75c3f297dcc8cce5", "path": "SDWebImage/Core/SDImageIOAnimatedCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98415407ee8183779a268a45012004eaea", "path": "SDWebImage/Core/SDImageIOAnimatedCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983244c512d63a8a3c8a29849539c05551", "path": "SDWebImage/Private/SDImageIOAnimatedCoderInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983c947ca5e52621bdbd94b07a94f7c964", "path": "SDWebImage/Core/SDImageIOCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98655b757305a251b4fdc570c17e83ad59", "path": "SDWebImage/Core/SDImageIOCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c382d41e627c0cd0bb2b71160d1ce407", "path": "SDWebImage/Core/SDImageLoader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985932f638d4376f4e0056f2bfde48eacd", "path": "SDWebImage/Core/SDImageLoader.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986f7caa2c2a4e783ed04ea75df008a09f", "path": "SDWebImage/Core/SDImageLoadersManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98419451fbc9f50242255ea2bd9ff22c73", "path": "SDWebImage/Core/SDImageLoadersManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9857624689b106aed8ee3d1fb5ad2daada", "path": "SDWebImage/Core/SDImageTransformer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983ee1d0e0331ed6d95a58f4aaa7e4b9a1", "path": "SDWebImage/Core/SDImageTransformer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fb5b74d4d3b3ee893c684f34352544ea", "path": "SDWebImage/Private/SDInternalMacros.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9890cc2477e35b25d7f694bc07a85c7b4b", "path": "SDWebImage/Private/SDInternalMacros.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983cae890dc76ed103c097745ef75f8a58", "path": "SDWebImage/Core/SDMemoryCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b94b00fbc9ede9cf3821ec6ba6f8b470", "path": "SDWebImage/Core/SDMemoryCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98732710817ab76255f8939e6489976ea6", "path": "SDWebImage/Private/SDmetamacros.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e16fe19a0ea7cce68f310c20ed7796f1", "path": "SDWebImage/Private/SDWeakProxy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982559fa11c23cf0b3beeee2209e57fd58", "path": "SDWebImage/Private/SDWeakProxy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9859509e90c4b7ac0c76e6c760871a7105", "path": "WebImage/SDWebImage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983ad43ce49edc461e8cea868eee0f3144", "path": "SDWebImage/Core/SDWebImageCacheKeyFilter.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98de6bdaae48f0bcb35525ffab003ed916", "path": "SDWebImage/Core/SDWebImageCacheKeyFilter.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a91a6df9bf3c52d694395f9ee759b589", "path": "SDWebImage/Core/SDWebImageCacheSerializer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cd9902635a0ce28fc03fbcc9a9535696", "path": "SDWebImage/Core/SDWebImageCacheSerializer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98538ee9b344b032cd3e657bd3301569d1", "path": "SDWebImage/Core/SDWebImageCompat.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98654b73bc0f8c481bf77ae995c6da45a1", "path": "SDWebImage/Core/SDWebImageCompat.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a9c2384ac7da82e6014ee5dcf465bf38", "path": "SDWebImage/Core/SDWebImageDefine.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984746c62bc5ce2ce4c04430d91c371ce4", "path": "SDWebImage/Core/SDWebImageDefine.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984b2e99ad4e69fa7903931eca62d84744", "path": "SDWebImage/Core/SDWebImageDownloader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98da0f10ab2644e018144c34052eefa8d9", "path": "SDWebImage/Core/SDWebImageDownloader.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98beea87df02c199aeaf52abcc17977857", "path": "SDWebImage/Core/SDWebImageDownloaderConfig.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a50f3040552354322811494c3443165b", "path": "SDWebImage/Core/SDWebImageDownloaderConfig.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988104d09019af59e4f7de013cf2bf8c84", "path": "SDWebImage/Core/SDWebImageDownloaderDecryptor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9807562b9cc7f600d608e269e6ac5d30f9", "path": "SDWebImage/Core/SDWebImageDownloaderDecryptor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98effa317b0b595e00d770c5bd2a046a2f", "path": "SDWebImage/Core/SDWebImageDownloaderOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984087ca59f81183dce98bbb215b486d7d", "path": "SDWebImage/Core/SDWebImageDownloaderOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989e4b3a8d82eed21fbfc1f6eafd6156ee", "path": "SDWebImage/Core/SDWebImageDownloaderRequestModifier.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ba0a6c8bc5a3c9dfc983ffca7b0e3e5a", "path": "SDWebImage/Core/SDWebImageDownloaderRequestModifier.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c2719f5f36fde6aa6cff602be96f429c", "path": "SDWebImage/Core/SDWebImageDownloaderResponseModifier.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f71275c362a3ea1a165a6b9cdcc8dfb7", "path": "SDWebImage/Core/SDWebImageDownloaderResponseModifier.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98639de22ff7745d4857a104c6e05b876b", "path": "SDWebImage/Core/SDWebImageError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fcfb3a906dfa63c74b2fde202108f7b4", "path": "SDWebImage/Core/SDWebImageError.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9858a534cb872af9998ddab7a678de9eed", "path": "SDWebImage/Core/SDWebImageIndicator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9811c9c07393bcd0603bcaedc4960520cb", "path": "SDWebImage/Core/SDWebImageIndicator.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9847e0f36d453c007eb40eab965aa2eab1", "path": "SDWebImage/Core/SDWebImageManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c2b7aebcd119de3c6456662895d8945b", "path": "SDWebImage/Core/SDWebImageManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b8ea95006290f201a50befe0f443d1b4", "path": "SDWebImage/Core/SDWebImageOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98aeb3a7cb98b20900a05ec1f44a52374a", "path": "SDWebImage/Core/SDWebImageOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989f24a2d6287f12478f2bd87afcd2d8a5", "path": "SDWebImage/Core/SDWebImageOptionsProcessor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98945433b73de627663cfe7a66a2c8aa49", "path": "SDWebImage/Core/SDWebImageOptionsProcessor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ddd7ffafe0923026e2c4ced956ad593e", "path": "SDWebImage/Core/SDWebImagePrefetcher.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98557bb4a52a77d421b5db5c6ee624abd0", "path": "SDWebImage/Core/SDWebImagePrefetcher.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98032ededb6fe38068a0f634982ca18b32", "path": "SDWebImage/Core/SDWebImageTransition.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983c8d0aec6bf0b8b4a5f1fa8e6a995837", "path": "SDWebImage/Core/SDWebImageTransition.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b7e30274661e40c599c986c1a561c06e", "path": "SDWebImage/Private/SDWebImageTransitionInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987d148632fcacd1c698c5edcf10462ddd", "path": "SDWebImage/Core/UIButton+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fe73452028b1e0324e1cef93a11345ac", "path": "SDWebImage/Core/UIButton+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f4b01b953e509480834e7a191dd42479", "path": "SDWebImage/Private/UIColor+SDHexString.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98953fd35294ccd7b5d7bf2d4c8d0f9442", "path": "SDWebImage/Private/UIColor+SDHexString.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ad5cb9cf75d686d18f06de22e53a885b", "path": "SDWebImage/Core/UIImage+ExtendedCacheData.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9860acd9f79bfb1ac67fcd43c1700d9a9c", "path": "SDWebImage/Core/UIImage+ExtendedCacheData.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982a24deaf6194fb60a7a7dabf183da017", "path": "SDWebImage/Core/UIImage+ForceDecode.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98201736226a09cf9aa451a69dd87e52ff", "path": "SDWebImage/Core/UIImage+ForceDecode.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982cbfd78f1b5b22955e7d030bde3d6ff9", "path": "SDWebImage/Core/UIImage+GIF.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986ea5dfba52397a3ef2fc2ec020beb7fa", "path": "SDWebImage/Core/UIImage+GIF.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981e883b1236c502ae3ba50d16d295a9cb", "path": "SDWebImage/Core/UIImage+MemoryCacheCost.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983e810ad5f01da61514d84dc1ee9f6258", "path": "SDWebImage/Core/UIImage+MemoryCacheCost.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b17fd229104e1b220eaacfaeac4143d9", "path": "SDWebImage/Core/UIImage+Metadata.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987d88d626ec0c257fe4624cb49c5db8b7", "path": "SDWebImage/Core/UIImage+Metadata.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98db7ccf756194068c62beebc2d5ceb389", "path": "SDWebImage/Core/UIImage+MultiFormat.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9809bad0b387bff6a3435c36d264b9e730", "path": "SDWebImage/Core/UIImage+MultiFormat.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c6f0f5f14deac1c4411c65b5e26e827e", "path": "SDWebImage/Core/UIImage+Transform.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b01103f66f027ade3b6d44a2f04aba81", "path": "SDWebImage/Core/UIImage+Transform.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980a40e0f256847cf5d9bb43babf9521ab", "path": "SDWebImage/Core/UIImageView+HighlightedWebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988f58c26c26ca046631f8058261177828", "path": "SDWebImage/Core/UIImageView+HighlightedWebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b77c64fc4f6debc54552f47963b1031e", "path": "SDWebImage/Core/UIImageView+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986302e41fe8fe60c92abea59fd0d2a56b", "path": "SDWebImage/Core/UIImageView+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e97ed5fe8545e8a9d40f7faf14427469", "path": "SDWebImage/Core/UIView+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cc0748c319f7574eb6e0e15b00ee1864", "path": "SDWebImage/Core/UIView+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b7c404941a9183ea0444743686ce5e9f", "path": "SDWebImage/Core/UIView+WebCacheOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9881c26ea49b924db31924f0433f68bc53", "path": "SDWebImage/Core/UIView+WebCacheOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f24ae7d28bc462c61a59efc023eb9fe6", "path": "SDWebImage/Core/UIView+WebCacheState.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982bf4102df0a01e53abe5295e131aa8a8", "path": "SDWebImage/Core/UIView+WebCacheState.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98072810b3f251fe9adc8a780b3ece2d19", "path": "WebImage/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984149ea9179d668f64953cdc2648a35fc", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986afd5e4bb238cf0873bc57c2d5868bdd", "name": "Core", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9842b7c93a12dba3f4c2f2ff1d1aef923d", "path": "ResourceBundle-SDWebImage-SDWebImage-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e981e75d6cd2d97137220ce39cfdfe609c8", "path": "SDWebImage.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a3614c0cdbbb1289be0ee6221ea10b22", "path": "SDWebImage-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98a04f07fcd9d6f65bf97327627570c980", "path": "SDWebImage-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986959b6973ee54ade426e00f66ffcdafb", "path": "SDWebImage-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9837d5a1414edf4319ba38d9251e085d72", "path": "SDWebImage-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9807dced212da9d57ff037610b6063ab15", "path": "SDWebImage.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9817591ddf25ade48b1de942ba4d9a652c", "path": "SDWebImage.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cb8762a09745d54fa4965b6117781373", "name": "Support Files", "path": "../Target Support Files/SDWebImage", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a8b47be66f4a4e41674ee94ab4719f79", "name": "SDWebImage", "path": "SDWebImage", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e5ec22b9c4ec919ca16aaf1106fdfda7", "path": "SwiftyGif/NSImage+SwiftyGif.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c1f3f2ed47d0040183997f8bbf5e6ac1", "path": "SwiftyGif/NSImageView+SwiftyGif.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987b5362145f0a8c83082dc613a6cf5234", "path": "SwiftyGif/ObjcAssociatedWeakObject.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98841e69c77a33c992a383444bcd690833", "path": "SwiftyGif/SwiftyGif.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a09117dc7d3b1c63324941c308702cff", "path": "SwiftyGif/SwiftyGifManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98da799bba8441f8c7fb816c76aba9c6c5", "path": "SwiftyGif/UIImage+SwiftyGif.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e813830c6fcdcb4cb8bc6548d3e992d1", "path": "SwiftyGif/UIImageView+SwiftyGif.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9818ad385274ce64def8d4f9f9ce74f574", "path": "SwiftyGif/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ddfd6ca8fa039b28bef5447f4ab0e579", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9851c995aa0b25bfd1034ba42b042684dd", "path": "ResourceBundle-SwiftyGif-SwiftyGif-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98107f47d528f00f9c2b25b54f5e3f57f1", "path": "SwiftyGif.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ec31ea89f37bf925e7a4e918d80427c7", "path": "SwiftyGif-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98000291fe575e781645526f10839d69ef", "path": "SwiftyGif-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e25d2ce3a7155b518e2dfa84ae0744ca", "path": "SwiftyGif-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986b9414b84d9ae46c6cfb7c8966e95c73", "path": "SwiftyGif-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988d017df6eb26a1783b874214268dea26", "path": "SwiftyGif.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f191c54bfc8bd2078a4337a8bbbe4787", "path": "SwiftyGif.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98269dbb5b1a9ce03fd3baa9ee7000bd3d", "name": "Support Files", "path": "../Target Support Files/SwiftyGif", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98076ab6869ae66a19d6d4d7f3b02b1d86", "name": "SwiftyGif", "path": "SwiftyGif", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9862ddf3649a7efb07559238001a21afa9", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"guid": "bfdfe7dc352907fc980b868725387e98db8497239a5e5556bf5f4ac0385843ae", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98a3f4680c874ecbcb5b5a6338bc13a426", "path": "Pods-Runner.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9814181933c973297b0001d0e9ee64381d", "path": "Pods-Runner-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9887440869a53d6020d968ddf6b9b30aa0", "path": "Pods-Runner-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fed0970e703d355c9aff813f280aafdc", "path": "Pods-Runner-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e9826e505869a67d81656859ded68178c1d", "path": "Pods-Runner-frameworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98de02ac5005ab1eb052abf25c63731e95", "path": "Pods-Runner-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e98f6f4dcb116856f2da0fa97d3015cb1af", "path": "Pods-Runner-resources.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980ec6fbca7264a6936f2adfc48dd7f5bc", "path": "Pods-Runner-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9850cc7fc2d23136fb4fac488d6c47df20", "path": "Pods-Runner.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98dc1a21852c7a085c7dd8f65cf0fa9907", "path": "Pods-Runner.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f17abf55d75f35efcaf45a1185b085b6", "path": "Pods-Runner.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cf3c1972df678a5e36df75a46391500d", "name": "Pods-Runner", "path": "Target Support Files/Pods-Runner", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984c4f55ec853c945e234980557a98aed8", "path": "Pods-RunnerTests.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98fc0f7e7242f459f81e455145932dcafd", "path": "Pods-RunnerTests-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985f8b68b152f46f18718da20c04e675cb", "path": "Pods-RunnerTests-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98022654f1ff78dd844d694dba2439dab2", "path": "Pods-RunnerTests-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989e5ad6b9a07953a12c7008a15bd9c99c", "path": "Pods-RunnerTests-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e5e8bcdff29e5f8321be18f7989b4bc7", "path": "Pods-RunnerTests-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98144cd18850e477837c238075d5256ffe", "path": "Pods-RunnerTests.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981b663a2c82f0220040296818ba53477e", "path": "Pods-RunnerTests.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98965b92d39d30a7872295adc2841cd1b1", "path": "Pods-RunnerTests.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9859551a2ccb1df711861b574920cd49bf", "name": "Pods-RunnerTests", "path": "Target Support Files/Pods-RunnerTests", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dafc421ff02609f2772b356038eb9849", "name": "Targets Support Files", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98677e601b37074db53aff90e47c8f96d1", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "bfdfe7dc352907fc980b868725387e98", "path": "/Users/<USER>/MyProjects/Startup/tushen/ios/Pods/Pods.xcodeproj", "projectDirectory": "/Users/<USER>/MyProjects/Startup/tushen/ios/Pods", "targets": ["TARGET@v11_hash=ea697cc04a446333ad139d9bf8fec5f7", "TARGET@v11_hash=d525daad7f58dfb4365f906c375801b8", "TARGET@v11_hash=bccd09130bd4c045aaadd77cdcbb576d", "TARGET@v11_hash=090c3eaabfe268f898c5bfb39b6ebe37", "TARGET@v11_hash=e408313d46518bb193161284f025f045", "TARGET@v11_hash=791b1cf90d055af11ffecb42435d1034", "TARGET@v11_hash=531d1d29cee6cbba60337fcf3411b5cc", "TARGET@v11_hash=e6b9912e91591e9177353b0334024dad", "TARGET@v11_hash=0962a7a2d6960bd66ed46049efb91231", "TARGET@v11_hash=b0f00357c04cdc8de2c6b53db776d442", "TARGET@v11_hash=11944795575578bf2e620560f4dfc1fc", "TARGET@v11_hash=175832b26f16ed7bd1ecb0cc44ec9e86", "TARGET@v11_hash=256002f44c4ede4d8fc972c6ea3c55d7", "TARGET@v11_hash=16cd84f6c09016f5825657ba52b9eb8c", "TARGET@v11_hash=f9f9c35aac2b1cd27a59df6c09b53904", "TARGET@v11_hash=a0f682512a172545ceaccfcf48939883", "TARGET@v11_hash=69852295fd3d169bfcf9494c2c4be93f", "TARGET@v11_hash=d371264fa2f9e5cd58bf904f442cd5a6", "TARGET@v11_hash=27192349f7fc64f355d732dd07ae6216", "TARGET@v11_hash=3893619ae7b5a78c7c86ca67c2efbfcc", "TARGET@v11_hash=445ffd9b4b4062a47cd0bfab921ae006", "TARGET@v11_hash=99140dffe20db4a00d6d67c1dab67a76", "TARGET@v11_hash=c244a678906725717b3ff299e6422a55", "TARGET@v11_hash=e62f21462fcd616bf5cc45e0f12dafd1", "TARGET@v11_hash=69d0771685deffb8d8efe6e067730a30", "TARGET@v11_hash=863cbf9f17605e755ec57fb44cdd7900", "TARGET@v11_hash=2b9a77eea9469e4da2baaf11c94b5272"]}