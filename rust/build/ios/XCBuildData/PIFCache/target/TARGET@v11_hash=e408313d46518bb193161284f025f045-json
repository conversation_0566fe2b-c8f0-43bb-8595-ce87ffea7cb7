{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ae529f76a1aeb84aed676cbc438f2337", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98dc8190807f22ab9baa784d7fdc383f63", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987c0ad1a68339e983c25e4de21965f4a8", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984adc140729c6bfd712c1f4f5050bc0cb", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987c0ad1a68339e983c25e4de21965f4a8", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985cf4c883e3fcc6112fdc3a2b61a2061e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e61c3706c4c5522b7ecd10e962efb922", "guid": "bfdfe7dc352907fc980b868725387e98500e17c668e6ec147c7b1f230486a9e6", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9833799a0af57f98589977027adf28fba3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986cd8b63f1efadf20d9ea1ed8f5a55c59", "guid": "bfdfe7dc352907fc980b868725387e98e532d329647db24c366d372897ee5860"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d637eecdfef95bbdf30369120352bd46", "guid": "bfdfe7dc352907fc980b868725387e98b920346eca2abad2431c1d0edeeb26b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834be2bf990d387cbce4913d90fc0866d", "guid": "bfdfe7dc352907fc980b868725387e980010f7eed2954ed4ebdb3aa9f3a00e5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984767f7ebe824e56e6af065bd8c127fb1", "guid": "bfdfe7dc352907fc980b868725387e986616dc69201ee213d3f63629d57a0369"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98051e74ac2dfb008bbd683e30491aa7be", "guid": "bfdfe7dc352907fc980b868725387e98696895c1042a26bbe9a2a999a84cd637"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982675e10da08f11651d5e552f26200541", "guid": "bfdfe7dc352907fc980b868725387e9815fef9b30c60f955ba021ebf825fc1f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834f1d1fb0dd3d6538989f7502ec1644c", "guid": "bfdfe7dc352907fc980b868725387e982cf8b2cf08953bb31f9c66e1453a385f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825db5c0b0de22f7488309691c8541b75", "guid": "bfdfe7dc352907fc980b868725387e988620f366bcce695cb1c1e407f15306be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982aa0b02535812fd0e603a89bcfb30ef5", "guid": "bfdfe7dc352907fc980b868725387e98dcff5896ada6aea7d395c9ee97659a73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b13723b07b108638ef45e70068037a0f", "guid": "bfdfe7dc352907fc980b868725387e98f527f236a5d7c4765f4d9ac55f303075"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983aecacec943b3b7fb13ca4f783aeb124", "guid": "bfdfe7dc352907fc980b868725387e98b4ab81a4810b158b9f82de75c34aa913"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982683856913254da4512b6e02f6e4f14f", "guid": "bfdfe7dc352907fc980b868725387e984fb986544b202bf0edce470d808c16c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9552c70edc7387c0167f655a2f9cab9", "guid": "bfdfe7dc352907fc980b868725387e9806386429b684caa2170d612ffdfbc2e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0333415fb47d981a93980d7783a25bc", "guid": "bfdfe7dc352907fc980b868725387e986fb4bde58620c62079b6b7608fc2a85c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864c787672805f0b55134cb199dc71c0e", "guid": "bfdfe7dc352907fc980b868725387e988e5dcd221783fdf70b362dea0e741a79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98850d9e8cfb22b221e8e383807ea38a07", "guid": "bfdfe7dc352907fc980b868725387e98b07485ba361a86c908870d0236e67731"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5b79d94ff93ec63566064eb357dbe2c", "guid": "bfdfe7dc352907fc980b868725387e98af3a0ddf58ff97d41bfa3f5df9cf78d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830b4739ebde111a52547b4d18a503410", "guid": "bfdfe7dc352907fc980b868725387e982cc7f1bb4a29711895d23c8fedaed954"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d2693191bcef035b93392dabd29e915", "guid": "bfdfe7dc352907fc980b868725387e9854625a62cb1d354204e96dc5ffcec746"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7133de307e021954fb1ea816636ae53", "guid": "bfdfe7dc352907fc980b868725387e983e95e95585b820f3b2f08cae0fe95843"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817c885c72c438eb48d3c624d21c32879", "guid": "bfdfe7dc352907fc980b868725387e98531a4e45b1e84f50044cce2a24d6c23e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986504caedde90e4b7bbf90bc3c93b56f5", "guid": "bfdfe7dc352907fc980b868725387e98207cf5036a0b9358a292aa02cd17f708"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98119755cf3c3ab367d61b1f6a2b919fbf", "guid": "bfdfe7dc352907fc980b868725387e987ce3f222c37f4c2fccf0b0b707e89704"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcde9c1fdc32784294392718f0d45ce0", "guid": "bfdfe7dc352907fc980b868725387e986016fcf7b76108d0219bed06ba5cee28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e630bb3219120e5c9a7785f505fc0eba", "guid": "bfdfe7dc352907fc980b868725387e98c6fa43e8b48db77e0e03d2f75d4c0944"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3eb35d57bc96b724d16fb6a815089bd", "guid": "bfdfe7dc352907fc980b868725387e98f4d3e74e8c8dcf3918eaaca358d96c99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98807401bfb1caf2d2d87935ce9df53b6e", "guid": "bfdfe7dc352907fc980b868725387e989370845ddb22029df10057903379abed"}], "guid": "bfdfe7dc352907fc980b868725387e9818aad09e0d4aff9706568aa85c9fbe2d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9807daa3f9265322026266a25d0045b5bb", "guid": "bfdfe7dc352907fc980b868725387e981b1d016b3bdb28825c8951b23727c4cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7e2dd08eff3fdc559a3f5fed1d9957c", "guid": "bfdfe7dc352907fc980b868725387e989c0b1e0530eed8aca012cd85e50fcb77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b9e1c3b6563a543b6fbe5f0759aef8f", "guid": "bfdfe7dc352907fc980b868725387e98df8455a0ceae2c81966ee6cba5f0a2c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812cc64573a371c51e6cdc1c4fd688b8c", "guid": "bfdfe7dc352907fc980b868725387e9873e44238f22416cd37fbe4a5cec43a54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d11e2d400847835a96dea825dfa8835", "guid": "bfdfe7dc352907fc980b868725387e982de8ed143b5c37d7b37c4cdd7e9f09dc"}], "guid": "bfdfe7dc352907fc980b868725387e98099c6f264999109d7e9eef51afb7abed", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e984f987c2ef5e3f24721492254a7eb074e", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e981f7a94a1f943fb6aee542fc7ec12b4f4", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}