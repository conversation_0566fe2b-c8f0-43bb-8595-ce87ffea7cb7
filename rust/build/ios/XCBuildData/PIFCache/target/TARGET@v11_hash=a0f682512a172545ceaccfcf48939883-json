{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f5c75fc475791974dc336e8a35ca4f66", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Env/google/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Env/google/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986cb66ff42dd1cc134900e2058ec5ed1d", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Env/google/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Env/google/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986cb66ff42dd1cc134900e2058ec5ed1d", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Env/google/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Env/google/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cba934023c11175e105ae96cb5039185", "guid": "bfdfe7dc352907fc980b868725387e98e10cd95552ae34663000acf140be1a34", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff2977c2e1264953fb1e1aeac2f55781", "guid": "bfdfe7dc352907fc980b868725387e98057497c7275da5d07f829fa9dc9a0c67", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982dd617c93f0b9ab82cc41a84d17c9ae5", "guid": "bfdfe7dc352907fc980b868725387e9891780e9fabe4066580dd59167528423c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb4369b29542cd382cc72c6b89ae80a6", "guid": "bfdfe7dc352907fc980b868725387e98db92fcc26bd7e275089fd78631ca7c74", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5bedf3b7db353cfd48683f4090c81a2", "guid": "bfdfe7dc352907fc980b868725387e9822b164515ef48190e2d6038a1faf6e13", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6be4a9e5657491114bf82ff0dc0c0ff", "guid": "bfdfe7dc352907fc980b868725387e98be40087abf16f8cb0bc60e0a495fac35", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98febda2ee4400b768e699489b69f6ae4a", "guid": "bfdfe7dc352907fc980b868725387e98d183cb5bc28590035fe09e6a8bd24166", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988646847e52460b7a9389190244d069b4", "guid": "bfdfe7dc352907fc980b868725387e98c825ef9ccdf9acac65baaab8839fe7c9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c33a6eb1852ee19e6d50235e4fd5db3", "guid": "bfdfe7dc352907fc980b868725387e9850ee07a344fec25c7e075ef0b481f68e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c150a8d0dd1bc4bc322932722ff269d", "guid": "bfdfe7dc352907fc980b868725387e9882f4adc6aaddeeb952381acd342064e2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98617a16018c24019f0442f744cca4b17a", "guid": "bfdfe7dc352907fc980b868725387e98120836ba3c292348f11196c68eddd0ed", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98338e5a2d78c0a2ce16e4991932be5163", "guid": "bfdfe7dc352907fc980b868725387e9832200864efacf0406f5f96fa5b103c04", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851e6761ea5e017b154198b4e36131c0c", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edaa13896c5abdfbd83bfbab7b7f4b0e", "guid": "bfdfe7dc352907fc980b868725387e981ed42983049501f671f5c691b24fad7f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98689efdf078bb7fb36686039c92e06e4b", "guid": "bfdfe7dc352907fc980b868725387e986ceb96a03a1fa786f47b367635725125", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a527a654c5342321c7db4516162309c", "guid": "bfdfe7dc352907fc980b868725387e9836804f4c0c874ca992e2f177cc788284", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff879f7508b26b69356a9625c41ae2b1", "guid": "bfdfe7dc352907fc980b868725387e98e2d11f06a9fe1aca176161109d040fa1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98837cbcf5415c9813c02fffcc80986fc6", "guid": "bfdfe7dc352907fc980b868725387e985db7085072c17dad3bbfa3d7ea9437ee", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98654a3ae1a83f7cea2c9a2e4f17431235", "guid": "bfdfe7dc352907fc980b868725387e98ffdb218ddd8440bd20de46b439efb3c0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868cb4d00f425fa96cc34b6d1499ff901", "guid": "bfdfe7dc352907fc980b868725387e986b5f48f1266443ccf2d324e46e192766", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9c15f6c1352bfac0c433e1e6e2c5041", "guid": "bfdfe7dc352907fc980b868725387e98ab3ce4fe706d0bc722fafc835ca3c14c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856dbfde31dbb8454d393bc57eac715ed", "guid": "bfdfe7dc352907fc980b868725387e989baf494ad4b83638a2935c39a03c2370", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828d68baa910661310565862c0edc65e7", "guid": "bfdfe7dc352907fc980b868725387e98440dd316c1b6ee637de907b969359e61", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98694c2bbda2994197dc8238ea8e3b7b49", "guid": "bfdfe7dc352907fc980b868725387e982f4e8ff5df9a4f7ded07b949447414c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e755d068b2b1a8feab5a29dbfc929665", "guid": "bfdfe7dc352907fc980b868725387e98380107a926beb55d6a22f8a6a91cfdb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890dcf64f2c45e8d0c460a82ee528a8e6", "guid": "bfdfe7dc352907fc980b868725387e987669ac7d7ba803652ae49fb524fa0f9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895a5ecfd2d0a6150afbe4bc97448caf7", "guid": "bfdfe7dc352907fc980b868725387e98e6d7716f7fec9fd603b5c66aa09fff65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98238a24771e4ea116c5d864aed5f4c08a", "guid": "bfdfe7dc352907fc980b868725387e9853449e6376c1f3b367f4b848c35cbc5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c012544cba32a2b0cc4013acc33db75f", "guid": "bfdfe7dc352907fc980b868725387e98be796ebc8036861a483b19fb298e450d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da70dfa6da72ae2c44d51ba837708a1c", "guid": "bfdfe7dc352907fc980b868725387e98061890186730350f074409dc34d9a901"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857bbeba27d246eee6acafa1b02014252", "guid": "bfdfe7dc352907fc980b868725387e986c92b8c22590a9bb51a23b3ceee3910d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca4ae6454c7de5665466ede3ed890a53", "guid": "bfdfe7dc352907fc980b868725387e9871cdfe04d6053bb3f110c25b7dc34a87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cbd378470758494378d7686bf750c5e", "guid": "bfdfe7dc352907fc980b868725387e986cd442065f4ad94bee86fb4bd4e2c957"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a0ee65846dcc1b15e5bf8e6f872584f", "guid": "bfdfe7dc352907fc980b868725387e98c8d3adcc5fb233c33b115701f7d8f61b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98475ac109c6329f499406ab0086544568", "guid": "bfdfe7dc352907fc980b868725387e988dbebfdcbc9bf98966ee9a29d0a32786"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d96bcceef3baa4cc4d5946a064e4732", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e934b44f3c5571730d78b0be715a41f4", "guid": "bfdfe7dc352907fc980b868725387e985a1a4b4eb81720e19c6bf5c125e4a9fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98590f460df5ee43004ccfc310855ee6c2", "guid": "bfdfe7dc352907fc980b868725387e98278d254c11860af61d922a42f1d74565"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857ee7cb723a0fac15c9f34543c5f1f47", "guid": "bfdfe7dc352907fc980b868725387e9826c4659abba47c187b39849268218353"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98432c878870daa11de8ae63f549c4e129", "guid": "bfdfe7dc352907fc980b868725387e989352f0836c3d8e95ccbd66cf58dd1606"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fe227b9801c382337cbb86b50bd2bc2", "guid": "bfdfe7dc352907fc980b868725387e986ef546d16dd2d4c52c8f634aabc98216"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c011182e47bd2f6b3867e85ff668e576", "guid": "bfdfe7dc352907fc980b868725387e98c5ba47e9c60876c5eddeca4800f7091d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0418d03b9d5d6b7e69276de7186e262", "guid": "bfdfe7dc352907fc980b868725387e989261a62aaa1f8d5f32ad776d6ad5cede"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b620d1c68b48c56e3c2ad51e3496381", "guid": "bfdfe7dc352907fc980b868725387e9859c49c32155b1a29360894f790b0f8cb"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987b9e1c3b6563a543b6fbe5f0759aef8f", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}