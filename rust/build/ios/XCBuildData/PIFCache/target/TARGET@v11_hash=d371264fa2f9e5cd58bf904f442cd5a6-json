{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982e175e3b91fa2389d9657743eab734f9", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Env/google/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Env/google/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/photo_manager/photo_manager-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/photo_manager/photo_manager-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/photo_manager/photo_manager.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "photo_manager", "PRODUCT_NAME": "photo_manager", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984dabebdf6f54c881cf6ce3f503dbafc8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988798107b1c57f5a1d8a01bc28d856810", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Env/google/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Env/google/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/photo_manager/photo_manager-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/photo_manager/photo_manager-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/photo_manager/photo_manager.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "photo_manager", "PRODUCT_NAME": "photo_manager", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9879370f9b6762ae3b167643fc360811ad", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988798107b1c57f5a1d8a01bc28d856810", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Env/google/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Env/google/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/photo_manager/photo_manager-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/photo_manager/photo_manager-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/photo_manager/photo_manager.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "photo_manager", "PRODUCT_NAME": "photo_manager", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e020dca05c43366d73fdccc591333ac8", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984d18def1f80417008a1d0422c4549421", "guid": "bfdfe7dc352907fc980b868725387e98d11df61534df1bf6bbdab4065ce71422", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988456e5f7a23fda77fda38a134e3a7130", "guid": "bfdfe7dc352907fc980b868725387e98d972661364f3e1d647d8a29653da73a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884db4f36167920a80dac58c7f8582348", "guid": "bfdfe7dc352907fc980b868725387e982e3dca7740a3489373efc2e0e4048cf7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b20a2ed18c9f17f24ee64909fd6d224", "guid": "bfdfe7dc352907fc980b868725387e98c93d33e4fe1ed5730573a649df1eee88", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98885067840a4fb896ae0250987e87bca1", "guid": "bfdfe7dc352907fc980b868725387e98b0ef26e54f4fc5ffffb5d09191291823", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af0c817c3897c7b54fb4ce8854c7066d", "guid": "bfdfe7dc352907fc980b868725387e9887f0c60e1ef68bf38dd2a564f13f9f93", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c6f07abedddf8fe4293d8627a3ed8cc", "guid": "bfdfe7dc352907fc980b868725387e982da920b84d5fd65a50d5c1b61fee0061", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98187cf215a2507b1e6cd87d0255bd8383", "guid": "bfdfe7dc352907fc980b868725387e983b8433d8a57996c5766bcdfbaf0c6bff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5a714214b70857e1624b29a148d216b", "guid": "bfdfe7dc352907fc980b868725387e988af366c647e64baeb119c10f1a3f582b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987263036b388759d4a8c4f454bb389e6e", "guid": "bfdfe7dc352907fc980b868725387e986b264a6fd9928e2e244ee28d9b53c328", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881f61618f8ab4cc89ac94e6799e8270e", "guid": "bfdfe7dc352907fc980b868725387e9841671a4829f90b4ad5b05db92c2d2de0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835f19626404ee65d5bda3e8a154804d7", "guid": "bfdfe7dc352907fc980b868725387e98ee102460c55622140292b81329a01535", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980766d3bc9b2ca4fb48e8ca891a750324", "guid": "bfdfe7dc352907fc980b868725387e988a9f72953739748f75355b280d9fd610", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f5e337d2cfdcea41772ee08a032e50e", "guid": "bfdfe7dc352907fc980b868725387e9806fcec1716d9e9aa3716a74f076421e3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983dd695de80854ebe95a69fb702e4e75e", "guid": "bfdfe7dc352907fc980b868725387e9826e214c0158b7b68d4d73c3b9d8082e7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d049fe14ae5b21465cf02430b138b113", "guid": "bfdfe7dc352907fc980b868725387e984756350d9c85a29fc79d0adac2f1f3bf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a7edd5a4d2a72d5077e4c42d8f80bb6", "guid": "bfdfe7dc352907fc980b868725387e98bf3360ef8be9198a3903482b331cfee7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98696a860f31712de2e8e3e99bbabf2f88", "guid": "bfdfe7dc352907fc980b868725387e98fe27d5bae66164e3434ba2d112a798ff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b47d2a552caff6a631ba44a79dbd64a3", "guid": "bfdfe7dc352907fc980b868725387e98d341c5fccb58f0c2934e707d505b44dd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d20955810ecd9deb9208fff773cefae2", "guid": "bfdfe7dc352907fc980b868725387e989acf6a8000bf8016c3bef65cce847175", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812be8af4c31012c1fd903df743cd272d", "guid": "bfdfe7dc352907fc980b868725387e98101e5585b55296fd94839e5eb7a2e991", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a63b39a7c4af885f96b754203b81d2ab", "guid": "bfdfe7dc352907fc980b868725387e98a85bf390f0a72db02a53b68c2db119c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98605d559bcfa2b9108e3003bc9d4c7dbb", "guid": "bfdfe7dc352907fc980b868725387e98da92830b79612fc87f95f09b6ecf5028", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985972aefed2823e6f85993bdc2dd8859f", "guid": "bfdfe7dc352907fc980b868725387e986324ef0a0ac3054e3c5aa4a9b3904031", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a727775966b696724e530acb616a37d1", "guid": "bfdfe7dc352907fc980b868725387e98643e5c5558a743d299675c1ae3e7a79b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7e1dac5a45f671ee6d9b8e8b0356d3a", "guid": "bfdfe7dc352907fc980b868725387e98f032ea8a6b4056ac7f88203910cf47af", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980211c987447e7c6e6517d8fbdcf1965b", "guid": "bfdfe7dc352907fc980b868725387e98778c888f3f7ebe06f6a56c1d247a0400", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe86d5f636329ba0bb94cc474486fc7a", "guid": "bfdfe7dc352907fc980b868725387e98d9fb86ec084b7fe86187ffceb750630b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fb9f59773f4b04b667dba1f5e17508c", "guid": "bfdfe7dc352907fc980b868725387e986afeb5d4ecd9c9fc7bc8508a0160c4e7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e92c1c3b3360ca87e9da7b9a3b1805b8", "guid": "bfdfe7dc352907fc980b868725387e9880b48240219e6da1e8aac7831f3c36b0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893620934c28cbf79315f7c129450d5c4", "guid": "bfdfe7dc352907fc980b868725387e98b8b5f4bb715cf32879b80071b7b4ce75", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9850d0827f95b51d4b1cdee62dc0ff2ebb", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988e2d2911aa1764f439cfa98a2b1ebfba", "guid": "bfdfe7dc352907fc980b868725387e9883af16dc4a1a31abf2103654b028a6a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893706eba73ba95892089af13e3e46a6c", "guid": "bfdfe7dc352907fc980b868725387e98f7c8110e761a641bc3ee127f425e0974"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0ae54e86e89d8d4f7d2b36a219322fd", "guid": "bfdfe7dc352907fc980b868725387e98cbb209b766c2fc3aeaa18e4c91929a1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b23e546ae8e70e9cece2f996dd62e5a", "guid": "bfdfe7dc352907fc980b868725387e9867cdd3417825f40f2a622b1a40463b04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a71fa9afc1aae19e78112451eca4f0b", "guid": "bfdfe7dc352907fc980b868725387e989d0e1772f276ff6c0783d7013b124a66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f248096b4c9decb5b28682f7daac440", "guid": "bfdfe7dc352907fc980b868725387e9848301141b51218205a2ff0abaef22a42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989259ab8c9f0a4e8cf0a2936d3f48850b", "guid": "bfdfe7dc352907fc980b868725387e98c8a53e78af2fb9cdd3c8a2a9277dd0e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b3ad3f7bc82139c1ef47cf7f34e4421", "guid": "bfdfe7dc352907fc980b868725387e98bbce41b616386beb946ec330fb7ddc0e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b5e2ae6b11543ba5f5c0cab5f04677d", "guid": "bfdfe7dc352907fc980b868725387e98c4b0c3b22dabdc7b9304834cd04f7ac1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982863b1ccd921d1d330e88f351bc38509", "guid": "bfdfe7dc352907fc980b868725387e989a4f8848b14904173013c499c91a70f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875eeaa667d08baa497d51ae1be44fbf6", "guid": "bfdfe7dc352907fc980b868725387e98a968f94c002510b81f52a39719e30522"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815c1a3f70b5da169c17ba70a3a1f0e27", "guid": "bfdfe7dc352907fc980b868725387e987746c30bc4896c8ba14fa20b9dc4fb0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981117486150f16cf7cb0d07011503d095", "guid": "bfdfe7dc352907fc980b868725387e981b4c46ceed877862856053b4f570f901"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd2bc865e46b764e8d6bcfc7c74d42af", "guid": "bfdfe7dc352907fc980b868725387e98fc20fce87c130003219c51337113c171"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98574cf07b0c0925539c0406ff5cfed17b", "guid": "bfdfe7dc352907fc980b868725387e9897a28f9c101f5c09b0f712225e832378"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868b743e581913224099871b924e2a45b", "guid": "bfdfe7dc352907fc980b868725387e98364bc64b9460518d6321d74d6c1002b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98693c252991baab940ace01151f786f60", "guid": "bfdfe7dc352907fc980b868725387e981b3e83c814c37fc9393dd29704632b4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857f8fc6c34b043cf188a147d6aa7d8ca", "guid": "bfdfe7dc352907fc980b868725387e987a904ddde910eb7705436a6714a10ebd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988248f3de770cc92bd45d87a524c19b2a", "guid": "bfdfe7dc352907fc980b868725387e98849b6072279198841dd8af65a00ff923"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d45877ff3dfa993034956eda8985cd32", "guid": "bfdfe7dc352907fc980b868725387e98d4e708647b4358e56d359b33b306314b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e48cfddce812839f37f364151606aead", "guid": "bfdfe7dc352907fc980b868725387e98abac48ed5854e9819320a6d9266bf10b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981879a973cb51bda3419bbd9e92923737", "guid": "bfdfe7dc352907fc980b868725387e98fec81f6377ebfbb4c9a208baaa800c54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b8e8b512044d9ebd79efc49a9938777", "guid": "bfdfe7dc352907fc980b868725387e98f483e883635b7f2123988427beda3fb6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814dde7547c2849df5cc50a625592d71c", "guid": "bfdfe7dc352907fc980b868725387e98ff9594d7b4e830f56e9bf58cb8e0e38e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a89dd86af26e4b416f1254bf09f6c10", "guid": "bfdfe7dc352907fc980b868725387e98fd58b9ef3706ddfa7b6d6de659b5768d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986323bf6e4d79bde068cd211c82b301f6", "guid": "bfdfe7dc352907fc980b868725387e98f1692792c20e1e8d06f845e4cdf9fad3"}], "guid": "bfdfe7dc352907fc980b868725387e98320e1412584a90fd41da063c83d2dd21", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987b9e1c3b6563a543b6fbe5f0759aef8f", "guid": "bfdfe7dc352907fc980b868725387e98c66fe7d2ba435345befdf4d53ffe9324"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4312a17bfed5f03cd67f84d23bdd541", "guid": "bfdfe7dc352907fc980b868725387e9837f82c7d5501ad000577f511c7066743"}], "guid": "bfdfe7dc352907fc980b868725387e986639f5be6da1c90ed3fe0682aa68698f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98569e89e49d03cc7e5bfa84b901e57021", "targetReference": "bfdfe7dc352907fc980b868725387e98e6c2666f7221fa58caaa0668238bc4de"}], "guid": "bfdfe7dc352907fc980b868725387e9878595d290b1af8130556a3317b477895", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e6c2666f7221fa58caaa0668238bc4de", "name": "photo_manager-photo_manager_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98cd1e97b2c7c0c2a96b4035a4c62b427d", "name": "photo_manager", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9841b881a585d538cf3da17a18e8b8ed12", "name": "photo_manager.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}