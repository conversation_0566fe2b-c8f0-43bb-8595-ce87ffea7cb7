{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e627b81f2277a26392e82dc437b640b9", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9835077b8a589e9ef232e180d12ee7f97f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984b6d27ca3f133a8809ce8858835ba048", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98b0a352d14c117364b3e34c4ce1c662c9", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984b6d27ca3f133a8809ce8858835ba048", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98b8e99ae1dd0326ff32d6e6342418c050", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e989986976558a64d002bffbdd138a633fa", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98b8a6fecd49b25b64d468d7fcedf1fd13", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a292699c7027eaa680bfe5da607e708f", "guid": "bfdfe7dc352907fc980b868725387e98d167c677ef3a26645335f702bed10fd7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852b8cf46dbb173cb75a3b48bbfbb8858", "guid": "bfdfe7dc352907fc980b868725387e980ecf815b600d81c63868622266d71107"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835648ce799d62afd8c7500844a76b60e", "guid": "bfdfe7dc352907fc980b868725387e98792b480650cb3cad2e8df7f897a4bb1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ff058d7b390b6f2003ce67e8d91cd71", "guid": "bfdfe7dc352907fc980b868725387e98377aeea9c19a0612ee34efd678ccdf1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98defb1e32e9bd8aba19d9a67f858e60b0", "guid": "bfdfe7dc352907fc980b868725387e9873d3a579fa9aca86f96b42e742118541"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808a0a427f1edd3500994a26f3b1b4614", "guid": "bfdfe7dc352907fc980b868725387e985bbd6f02ac6fc4a090a4355f53e670c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a490e75903fb2b0a8fb899a90d4287ed", "guid": "bfdfe7dc352907fc980b868725387e983faf3142908fa84178e1dbb8e12edfdf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c76b719348323ba9f42f0d81d70b161", "guid": "bfdfe7dc352907fc980b868725387e986c86a68429b31d1ed30d03500971594f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980962a367e6152e96b70a125d815020af", "guid": "bfdfe7dc352907fc980b868725387e98a403928b55cbbb99319ddef7ed2602e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a11f800dc26d56426f6b37f8a283b0b", "guid": "bfdfe7dc352907fc980b868725387e987aa59120b7acbb6a21a42b223a7fedf1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b08b2c243127aba3d1fc2398f7ba08f3", "guid": "bfdfe7dc352907fc980b868725387e98bcf64a73f2abe43f19685b6b2f3b5502"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989796f8ce5b602046976a913145a66f6c", "guid": "bfdfe7dc352907fc980b868725387e98d501ef6c4b098b184120d48806ee76fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986968906746efa810a96cf74bfa70670d", "guid": "bfdfe7dc352907fc980b868725387e982795650d67cc50575f906c429a828cf8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98936c99140c6d493cc2084861c68133a2", "guid": "bfdfe7dc352907fc980b868725387e9836729f90cb96bc81fbcc7fb610345c87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9017cb198b86bc9ed0cf016c9d298fc", "guid": "bfdfe7dc352907fc980b868725387e9892d426f39aff43eb56c6991925cd1285"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98135e59325df8adc673d9b81eb08333fb", "guid": "bfdfe7dc352907fc980b868725387e98ab0084f2a25f06971a2e830213d7286b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edc8787438970f71c64e1d428d742b0c", "guid": "bfdfe7dc352907fc980b868725387e98de9e2274a8240fd26e2e928157ce1e76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878653648422e683002192a6134cd98ad", "guid": "bfdfe7dc352907fc980b868725387e98260b2da21a97a064b3585dbd29d05c36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c46c4af4d494babb82844bf0d68a4f4", "guid": "bfdfe7dc352907fc980b868725387e98de10b49c0b77d5504387564bec9af04b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbca80ed18432ed6f1cbccf6775b30bb", "guid": "bfdfe7dc352907fc980b868725387e987b656c0ca0e5da6b28c0e2a8a947d609"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1e46671a6db2c214e25ea65b3d2809b", "guid": "bfdfe7dc352907fc980b868725387e9819afdf86ee9792ced92787c3ee93c2d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7b40e8c8c2cda51e62b5c5e2d2129be", "guid": "bfdfe7dc352907fc980b868725387e9855f30b1516e15951809f8a8220ce94ec"}], "guid": "bfdfe7dc352907fc980b868725387e98a85cca3d7152d703df7358c4f56046c2", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ab5e1f747dfe477b655528b07584898d", "name": "DKImagePickerController.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}