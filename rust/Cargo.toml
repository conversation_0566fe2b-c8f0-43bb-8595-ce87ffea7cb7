[package]
name = "tushen_core"
version = "0.1.0"
edition = "2021"
authors = ["TuShen Team"]
description = "High-performance image processing core for TuShen app"
license = "MIT"
repository = "https://github.com/tushen-team/tushen"
keywords = ["image", "processing", "filter", "collage", "mobile"]
categories = ["multimedia::images", "graphics"]

[lib]
name = "tushen_core"
crate-type = ["cdylib", "staticlib"]

[dependencies]
# 图像处理核心库
image = { version = "0.24", features = ["jpeg", "png", "gif", "webp", "tiff", "ico", "bmp"] }
imageproc = "0.23"
photon-rs = "0.3"

# 并行处理
rayon = { version = "1.7", optional = true }
tokio = { version = "1.0", features = ["full"] }

# FFI 桥接
flutter_rust_bridge = "=2.9.0"

# 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 错误处理
anyhow = "1.0"
thiserror = "1.0"

# 数学计算
nalgebra = "0.32"
ndarray = "0.15"

# 颜色处理
palette = "0.7"

# 日志
log = { version = "0.4", optional = true }
env_logger = { version = "0.10", optional = true }

# 性能监控
criterion = { version = "0.5", features = ["html_reports"] }

# 内存管理
once_cell = "1.19"

# 文件处理
walkdir = "2.4"

# 时间处理
chrono = { version = "0.4", features = ["serde"] }

# 平台特定依赖
[target.'cfg(target_os = "android")'.dependencies]
jni = "0.21"
ndk = "0.7"

[target.'cfg(any(target_os = "ios", target_os = "macos"))'.dependencies]
objc = "0.2"
core-graphics = "0.23"
metal = "0.24"

[target.'cfg(target_os = "windows")'.dependencies]
winapi = { version = "0.3", features = ["d3d11", "dxgi", "winuser"] }

[target.'cfg(target_os = "linux")'.dependencies]
x11 = "2.21"

[dev-dependencies]
criterion = "0.5"
proptest = "1.0"
tempfile = "3.8"

# Benchmarks will be added later
# [[bench]]
# name = "image_processing"
# harness = false

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true

[profile.dev]
opt-level = 1
debug = true

[profile.bench]
opt-level = 3
debug = false
lto = true

[features]
default = ["parallel", "gpu-acceleration"]
parallel = ["rayon"]
gpu-acceleration = []
debug-mode = ["log", "env_logger"]
flutter_rust_bridge = []

# 平台特定功能
android = []
ios = []
macos = []
windows = []
linux = []

# 图像格式支持
heic = []
raw = []
avif = []

# 高级功能
ai-filters = []
video-processing = []
live-photo = []
