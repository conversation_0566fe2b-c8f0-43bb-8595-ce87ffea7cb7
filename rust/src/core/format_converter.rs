//! Image format conversion functionality

use crate::utils::{ImageData, ProcessingOptions, ImageFormat};
use crate::error::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TuShenError};
use image::{DynamicImage, ImageFormat as ImageCrateFormat};
use std::io::Cursor;

/// Image format converter
pub struct FormatConverter {
    quality_settings: QualitySettings,
}

/// Quality settings for different formats
#[derive(Debug, Clone)]
pub struct QualitySettings {
    pub jpeg_quality: u8,
    pub webp_quality: f32,
    pub png_compression: u8,
}

impl Default for QualitySettings {
    fn default() -> Self {
        Self {
            jpeg_quality: 85,
            webp_quality: 80.0,
            png_compression: 6,
        }
    }
}

impl FormatConverter {
    /// Create a new format converter
    pub fn new() -> Self {
        Self {
            quality_settings: QualitySettings::default(),
        }
    }

    /// Create a new format converter with custom quality settings
    pub fn with_quality_settings(quality_settings: QualitySettings) -> Self {
        Self {
            quality_settings,
        }
    }

    /// Convert image to target format
    pub async fn convert(
        &self,
        image_data: ImageData,
        target_format: ImageFormat,
        _options: ProcessingOptions,
    ) -> TuShenResult<ImageData> {
        // Validate input
        crate::core::validate_image_data(&image_data)?;

        // If already in target format, return as-is (optionally re-encode with quality settings)
        if image_data.format == target_format {
            return self.re_encode_with_quality(image_data, target_format);
        }

        // Load image
        let img = self.load_image_from_bytes(&image_data.data)?;

        // Convert to target format
        self.encode_image(img, target_format)
    }

    /// Batch convert multiple images
    pub async fn batch_convert(
        &self,
        images: Vec<ImageData>,
        target_format: ImageFormat,
        options: ProcessingOptions,
    ) -> TuShenResult<Vec<ImageData>> {
        let mut converted_images = Vec::new();

        for image_data in images {
            let converted = self.convert(image_data, target_format.clone(), options.clone()).await?;
            converted_images.push(converted);
        }

        Ok(converted_images)
    }

    /// Get supported conversion formats
    pub fn get_supported_formats() -> Vec<ImageFormat> {
        vec![
            ImageFormat::Jpeg,
            ImageFormat::Png,
            ImageFormat::WebP,
            ImageFormat::Bmp,
            ImageFormat::Gif,
            ImageFormat::Tiff,
        ]
    }

    /// Check if conversion is supported
    pub fn is_conversion_supported(from: &ImageFormat, to: &ImageFormat) -> bool {
        let supported = Self::get_supported_formats();
        supported.contains(from) && supported.contains(to)
    }

    /// Estimate output file size
    pub fn estimate_output_size(
        &self,
        image_data: &ImageData,
        target_format: &ImageFormat,
    ) -> TuShenResult<usize> {
        let pixel_count = image_data.width as usize * image_data.height as usize;
        
        let estimated_size = match target_format {
            ImageFormat::Jpeg => {
                // JPEG compression ratio depends on quality
                let compression_ratio = match self.quality_settings.jpeg_quality {
                    90..=100 => 0.1,
                    80..=89 => 0.05,
                    70..=79 => 0.03,
                    60..=69 => 0.02,
                    _ => 0.01,
                };
                (pixel_count as f64 * 3.0 * compression_ratio) as usize
            },
            ImageFormat::Png => {
                // PNG is lossless, size depends on compression level and image complexity
                pixel_count * 4 / 2 // Rough estimate
            },
            ImageFormat::WebP => {
                // WebP compression similar to JPEG but more efficient
                let compression_ratio = self.quality_settings.webp_quality / 100.0 * 0.08;
                (pixel_count as f64 * 3.0 * compression_ratio as f64) as usize
            },
            ImageFormat::Bmp => {
                // BMP is uncompressed
                pixel_count * 4 + 54 // 54 bytes header
            },
            ImageFormat::Gif => {
                // GIF uses palette compression
                pixel_count / 2 // Rough estimate
            },
            ImageFormat::Tiff => {
                // TIFF can be compressed or uncompressed
                pixel_count * 3 // Rough estimate for compressed TIFF
            },
            _ => return Err(TuShenError::unsupported("Format not supported for size estimation")),
        };

        Ok(estimated_size)
    }

    /// Load image from bytes
    fn load_image_from_bytes(&self, bytes: &[u8]) -> TuShenResult<DynamicImage> {
        image::load_from_memory(bytes)
            .map_err(|e| TuShenError::processing(format!("Failed to load image: {}", e)))
    }

    /// Encode image to target format
    fn encode_image(&self, img: DynamicImage, target_format: ImageFormat) -> TuShenResult<ImageData> {
        let mut buffer = Vec::new();
        let mut cursor = Cursor::new(&mut buffer);

        match target_format {
            ImageFormat::Jpeg => {
                let encoder = image::codecs::jpeg::JpegEncoder::new_with_quality(
                    &mut cursor,
                    self.quality_settings.jpeg_quality,
                );
                img.write_with_encoder(encoder)
                    .map_err(|e| TuShenError::processing(format!("JPEG encoding failed: {}", e)))?;
            },
            ImageFormat::Png => {
                let encoder = image::codecs::png::PngEncoder::new_with_quality(
                    &mut cursor,
                    image::codecs::png::CompressionType::Default,
                    image::codecs::png::FilterType::Adaptive,
                );
                img.write_with_encoder(encoder)
                    .map_err(|e| TuShenError::processing(format!("PNG encoding failed: {}", e)))?;
            },
            ImageFormat::WebP => {
                img.write_to(&mut cursor, ImageCrateFormat::WebP)
                    .map_err(|e| TuShenError::processing(format!("WebP encoding failed: {}", e)))?;
            },
            ImageFormat::Bmp => {
                img.write_to(&mut cursor, ImageCrateFormat::Bmp)
                    .map_err(|e| TuShenError::processing(format!("BMP encoding failed: {}", e)))?;
            },
            ImageFormat::Gif => {
                img.write_to(&mut cursor, ImageCrateFormat::Gif)
                    .map_err(|e| TuShenError::processing(format!("GIF encoding failed: {}", e)))?;
            },
            ImageFormat::Tiff => {
                img.write_to(&mut cursor, ImageCrateFormat::Tiff)
                    .map_err(|e| TuShenError::processing(format!("TIFF encoding failed: {}", e)))?;
            },
            _ => return Err(TuShenError::unsupported("Target format not supported")),
        }

        Ok(ImageData::new(
            buffer,
            img.width(),
            img.height(),
            target_format,
        ))
    }

    /// Re-encode image with quality settings
    fn re_encode_with_quality(&self, image_data: ImageData, format: ImageFormat) -> TuShenResult<ImageData> {
        let img = self.load_image_from_bytes(&image_data.data)?;
        self.encode_image(img, format)
    }

    /// Update quality settings
    pub fn set_quality_settings(&mut self, quality_settings: QualitySettings) {
        self.quality_settings = quality_settings;
    }

    /// Get current quality settings
    pub fn get_quality_settings(&self) -> &QualitySettings {
        &self.quality_settings
    }
}

impl Default for FormatConverter {
    fn default() -> Self {
        Self::new()
    }
}

/// Utility functions for format conversion
pub mod utils {
    use super::*;

    /// Get file extension for image format
    pub fn get_file_extension(format: &ImageFormat) -> &'static str {
        match format {
            ImageFormat::Jpeg => "jpg",
            ImageFormat::Png => "png",
            ImageFormat::WebP => "webp",
            ImageFormat::Bmp => "bmp",
            ImageFormat::Gif => "gif",
            ImageFormat::Tiff => "tiff",
            ImageFormat::Heic => "heic",
            ImageFormat::Avif => "avif",
            _ => "bin",
        }
    }

    /// Get MIME type for image format
    pub fn get_mime_type(format: &ImageFormat) -> &'static str {
        match format {
            ImageFormat::Jpeg => "image/jpeg",
            ImageFormat::Png => "image/png",
            ImageFormat::WebP => "image/webp",
            ImageFormat::Bmp => "image/bmp",
            ImageFormat::Gif => "image/gif",
            ImageFormat::Tiff => "image/tiff",
            ImageFormat::Heic => "image/heic",
            ImageFormat::Avif => "image/avif",
            _ => "application/octet-stream",
        }
    }

    /// Detect format from file extension
    pub fn format_from_extension(extension: &str) -> Option<ImageFormat> {
        match extension.to_lowercase().as_str() {
            "jpg" | "jpeg" => Some(ImageFormat::Jpeg),
            "png" => Some(ImageFormat::Png),
            "webp" => Some(ImageFormat::WebP),
            "bmp" => Some(ImageFormat::Bmp),
            "gif" => Some(ImageFormat::Gif),
            "tiff" | "tif" => Some(ImageFormat::Tiff),
            "heic" => Some(ImageFormat::Heic),
            "avif" => Some(ImageFormat::Avif),
            _ => None,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use image::{ImageBuffer, Rgba, RgbaImage};

    #[tokio::test]
    async fn test_format_converter_creation() {
        let converter = FormatConverter::new();
        assert_eq!(converter.quality_settings.jpeg_quality, 85);
    }

    #[tokio::test]
    async fn test_format_conversion() {
        let converter = FormatConverter::new();
        let test_image = create_test_image_data();
        
        let result = converter.convert(test_image, ImageFormat::Jpeg, ProcessingOptions::default()).await;
        assert!(result.is_ok());
        
        let converted = result.unwrap();
        assert_eq!(converted.format, ImageFormat::Jpeg);
    }

    #[test]
    fn test_supported_formats() {
        let formats = FormatConverter::get_supported_formats();
        assert!(!formats.is_empty());
        assert!(formats.contains(&ImageFormat::Jpeg));
        assert!(formats.contains(&ImageFormat::Png));
    }

    fn create_test_image_data() -> ImageData {
        let img: RgbaImage = ImageBuffer::from_fn(100, 100, |_, _| {
            Rgba([255, 0, 0, 255])
        });
        
        let dynamic_img = DynamicImage::ImageRgba8(img);
        let mut buffer = Vec::new();
        let mut cursor = Cursor::new(&mut buffer);
        
        dynamic_img.write_to(&mut cursor, ImageCrateFormat::Png).unwrap();
        
        ImageData::new(buffer, 100, 100, ImageFormat::Png)
    }
}
