//! Collage creation engine

use crate::utils::{ImageData, ProcessingOptions, ImageFormat};
use crate::error::{Tu<PERSON>hen<PERSON><PERSON>ult, TuShenError};
use image::{DynamicImage, ImageBuffer, Rgba, RgbaImage};
use std::io::Cursor;

/// Collage layout types
#[derive(Debug, <PERSON>lone, PartialEq)]
pub enum LayoutType {
    Grid,
    Mosaic,
    Freeform,
    Magazine,
    Polaroid,
}

/// Region definition for collage layout
#[derive(Debug, Clone)]
pub struct Region {
    pub x: u32,
    pub y: u32,
    pub width: u32,
    pub height: u32,
    pub rotation: f32,
    pub border_width: u32,
    pub border_color: [u8; 4], // RGBA
}

/// Collage layout configuration
#[derive(Debug, Clone)]
pub struct CollageLayout {
    pub layout_type: LayoutType,
    pub canvas_width: u32,
    pub canvas_height: u32,
    pub background_color: [u8; 4], // RGBA
    pub spacing: u32,
    pub regions: Vec<Region>,
}

/// Collage creation engine
pub struct CollageEngine {
    max_images: usize,
}

impl CollageEngine {
    /// Create a new collage engine
    pub fn new() -> Self {
        Self {
            max_images: 20,
        }
    }

    /// Create a collage from multiple images
    pub async fn create_collage(
        &self,
        images: Vec<ImageData>,
        layout: CollageLayout,
        _options: ProcessingOptions,
    ) -> TuShenResult<ImageData> {
        // Validate inputs
        if images.is_empty() {
            return Err(TuShenError::invalid_input("No images provided for collage"));
        }

        if images.len() > self.max_images {
            return Err(TuShenError::invalid_input(format!(
                "Too many images: {} (max: {})",
                images.len(),
                self.max_images
            )));
        }

        // Load all images
        let loaded_images = self.load_images(images).await?;

        // Create canvas
        let mut canvas = self.create_canvas(&layout)?;

        // Place images according to layout
        self.place_images_on_canvas(&mut canvas, &loaded_images, &layout)?;

        // Convert canvas to ImageData
        self.canvas_to_image_data(canvas, &layout)
    }

    /// Generate automatic layout for given number of images
    pub fn generate_layout(
        &self,
        image_count: usize,
        canvas_width: u32,
        canvas_height: u32,
        layout_type: LayoutType,
    ) -> TuShenResult<CollageLayout> {
        match layout_type {
            LayoutType::Grid => self.generate_grid_layout(image_count, canvas_width, canvas_height),
            LayoutType::Mosaic => self.generate_mosaic_layout(image_count, canvas_width, canvas_height),
            LayoutType::Magazine => self.generate_magazine_layout(image_count, canvas_width, canvas_height),
            LayoutType::Polaroid => self.generate_polaroid_layout(image_count, canvas_width, canvas_height),
            LayoutType::Freeform => self.generate_freeform_layout(image_count, canvas_width, canvas_height),
        }
    }

    /// Load images from ImageData
    async fn load_images(&self, image_data_list: Vec<ImageData>) -> TuShenResult<Vec<DynamicImage>> {
        let mut images = Vec::new();
        
        for image_data in image_data_list {
            let img = image::load_from_memory(&image_data.data)
                .map_err(|e| TuShenError::processing(format!("Failed to load image: {}", e)))?;
            images.push(img);
        }
        
        Ok(images)
    }

    /// Create canvas with background
    fn create_canvas(&self, layout: &CollageLayout) -> TuShenResult<RgbaImage> {
        let canvas = ImageBuffer::from_fn(layout.canvas_width, layout.canvas_height, |_, _| {
            Rgba(layout.background_color)
        });
        Ok(canvas)
    }

    /// Place images on canvas according to layout
    fn place_images_on_canvas(
        &self,
        canvas: &mut RgbaImage,
        images: &[DynamicImage],
        layout: &CollageLayout,
    ) -> TuShenResult<()> {
        for (i, region) in layout.regions.iter().enumerate() {
            if i >= images.len() {
                break;
            }

            let img = &images[i];
            let resized_img = img.resize_exact(
                region.width,
                region.height,
                image::imageops::FilterType::Lanczos3,
            );

            // Convert to RGBA
            let rgba_img = resized_img.to_rgba8();

            // Place image on canvas
            image::imageops::overlay(canvas, &rgba_img, region.x as i64, region.y as i64);

            // Add border if specified
            if region.border_width > 0 {
                self.add_border(canvas, region)?;
            }
        }

        Ok(())
    }

    /// Add border to a region
    fn add_border(&self, canvas: &mut RgbaImage, region: &Region) -> TuShenResult<()> {
        let border_color = Rgba(region.border_color);
        let border_width = region.border_width;

        // Top border
        for x in region.x..region.x + region.width {
            for y in region.y..region.y + border_width.min(region.height) {
                if x < canvas.width() && y < canvas.height() {
                    canvas.put_pixel(x, y, border_color);
                }
            }
        }

        // Bottom border
        for x in region.x..region.x + region.width {
            for y in (region.y + region.height).saturating_sub(border_width)..region.y + region.height {
                if x < canvas.width() && y < canvas.height() {
                    canvas.put_pixel(x, y, border_color);
                }
            }
        }

        // Left border
        for x in region.x..region.x + border_width.min(region.width) {
            for y in region.y..region.y + region.height {
                if x < canvas.width() && y < canvas.height() {
                    canvas.put_pixel(x, y, border_color);
                }
            }
        }

        // Right border
        for x in (region.x + region.width).saturating_sub(border_width)..region.x + region.width {
            for y in region.y..region.y + region.height {
                if x < canvas.width() && y < canvas.height() {
                    canvas.put_pixel(x, y, border_color);
                }
            }
        }

        Ok(())
    }

    /// Convert canvas to ImageData
    fn canvas_to_image_data(&self, canvas: RgbaImage, layout: &CollageLayout) -> TuShenResult<ImageData> {
        let dynamic_img = DynamicImage::ImageRgba8(canvas);
        let mut buffer = Vec::new();
        let mut cursor = Cursor::new(&mut buffer);

        dynamic_img.write_to(&mut cursor, image::ImageFormat::Png)
            .map_err(|e| TuShenError::processing(format!("Failed to encode collage: {}", e)))?;

        Ok(ImageData::new(
            buffer,
            layout.canvas_width,
            layout.canvas_height,
            ImageFormat::Png,
        ))
    }

    /// Generate grid layout
    fn generate_grid_layout(
        &self,
        image_count: usize,
        canvas_width: u32,
        canvas_height: u32,
    ) -> TuShenResult<CollageLayout> {
        let spacing = 10u32;
        let cols = (image_count as f64).sqrt().ceil() as u32;
        let rows = (image_count as f64 / cols as f64).ceil() as u32;

        let cell_width = (canvas_width - spacing * (cols + 1)) / cols;
        let cell_height = (canvas_height - spacing * (rows + 1)) / rows;

        let mut regions = Vec::new();
        for i in 0..image_count {
            let col = (i as u32) % cols;
            let row = (i as u32) / cols;

            let x = spacing + col * (cell_width + spacing);
            let y = spacing + row * (cell_height + spacing);

            regions.push(Region {
                x,
                y,
                width: cell_width,
                height: cell_height,
                rotation: 0.0,
                border_width: 2,
                border_color: [255, 255, 255, 255],
            });
        }

        Ok(CollageLayout {
            layout_type: LayoutType::Grid,
            canvas_width,
            canvas_height,
            background_color: [240, 240, 240, 255],
            spacing,
            regions,
        })
    }

    /// Generate mosaic layout (simplified)
    fn generate_mosaic_layout(
        &self,
        image_count: usize,
        canvas_width: u32,
        canvas_height: u32,
    ) -> TuShenResult<CollageLayout> {
        // For now, use grid layout as base for mosaic
        // In a real implementation, this would create irregular shapes
        self.generate_grid_layout(image_count, canvas_width, canvas_height)
    }

    /// Generate magazine layout
    fn generate_magazine_layout(
        &self,
        image_count: usize,
        canvas_width: u32,
        canvas_height: u32,
    ) -> TuShenResult<CollageLayout> {
        // Simplified magazine layout with varying sizes
        let mut regions = Vec::new();
        let spacing = 15u32;

        if image_count >= 1 {
            // Main large image
            regions.push(Region {
                x: spacing,
                y: spacing,
                width: canvas_width * 2 / 3 - spacing * 2,
                height: canvas_height * 2 / 3 - spacing * 2,
                rotation: 0.0,
                border_width: 0,
                border_color: [255, 255, 255, 255],
            });
        }

        // Add smaller images
        for i in 1..image_count.min(4) {
            let small_width = canvas_width / 4 - spacing;
            let small_height = canvas_height / 4 - spacing;
            
            regions.push(Region {
                x: canvas_width * 2 / 3 + spacing,
                y: spacing + (i as u32 - 1) * (small_height + spacing),
                width: small_width,
                height: small_height,
                rotation: 0.0,
                border_width: 1,
                border_color: [200, 200, 200, 255],
            });
        }

        Ok(CollageLayout {
            layout_type: LayoutType::Magazine,
            canvas_width,
            canvas_height,
            background_color: [255, 255, 255, 255],
            spacing,
            regions,
        })
    }

    /// Generate polaroid layout
    fn generate_polaroid_layout(
        &self,
        image_count: usize,
        canvas_width: u32,
        canvas_height: u32,
    ) -> TuShenResult<CollageLayout> {
        let mut layout = self.generate_grid_layout(image_count, canvas_width, canvas_height)?;
        
        // Add polaroid-style borders and slight rotations
        for (i, region) in layout.regions.iter_mut().enumerate() {
            region.border_width = 20;
            region.border_color = [255, 255, 255, 255];
            region.rotation = (i as f32 * 7.0) % 15.0 - 7.5; // Random-ish rotation
        }

        layout.layout_type = LayoutType::Polaroid;
        layout.background_color = [245, 245, 220, 255]; // Beige background
        
        Ok(layout)
    }

    /// Generate freeform layout
    fn generate_freeform_layout(
        &self,
        image_count: usize,
        canvas_width: u32,
        canvas_height: u32,
    ) -> TuShenResult<CollageLayout> {
        // For now, use grid as base for freeform
        // In a real implementation, this would use more complex algorithms
        self.generate_grid_layout(image_count, canvas_width, canvas_height)
    }
}

impl Default for CollageEngine {
    fn default() -> Self {
        Self::new()
    }
}
