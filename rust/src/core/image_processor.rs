//! Image processing core functionality

use crate::utils::{ImageData, ProcessingOptions, ImageFormat};
use crate::error::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TuShenError};
use crate::filters::{FilterEngine, FilterType};
use image::DynamicImage;
use std::io::Cursor;

/// Core image processor for TuShen
pub struct ImageProcessor {
    filter_engine: FilterEngine,
}

impl ImageProcessor {
    /// Create a new image processor
    pub fn new() -> Self {
        Self {
            filter_engine: FilterEngine::new(),
        }
    }

    /// Process an image with the given options
    pub async fn process(
        &mut self,
        image_data: ImageData,
        options: ProcessingOptions,
    ) -> TuShenResult<ImageData> {
        // Validate input
        crate::core::validate_image_data(&image_data)?;

        // Load image from bytes
        let img = self.load_image_from_bytes(&image_data.data)?;

        // Apply processing operations
        let processed_img = self.apply_processing_operations(img, &options, &image_data.format).await?;

        // Convert back to ImageData
        self.image_to_data(processed_img, &image_data.format)
    }

    /// Apply filters to an image
    pub async fn apply_filter(
        &mut self,
        image_data: ImageData,
        filter_type: FilterType,
        intensity: f32,
    ) -> TuShenResult<ImageData> {
        self.filter_engine.apply_filter(image_data, filter_type, intensity).await
    }

    /// Resize an image
    pub fn resize(
        &self,
        image_data: ImageData,
        new_width: u32,
        new_height: u32,
        preserve_aspect_ratio: bool,
    ) -> TuShenResult<ImageData> {
        let img = self.load_image_from_bytes(&image_data.data)?;
        
        let resized_img = if preserve_aspect_ratio {
            img.resize(new_width, new_height, image::imageops::FilterType::Lanczos3)
        } else {
            img.resize_exact(new_width, new_height, image::imageops::FilterType::Lanczos3)
        };

        self.image_to_data(resized_img, &image_data.format)
    }

    /// Crop an image
    pub fn crop(
        &self,
        image_data: ImageData,
        x: u32,
        y: u32,
        width: u32,
        height: u32,
    ) -> TuShenResult<ImageData> {
        let mut img = self.load_image_from_bytes(&image_data.data)?;
        
        // Validate crop bounds
        if x + width > img.width() || y + height > img.height() {
            return Err(TuShenError::invalid_input("Crop bounds exceed image dimensions"));
        }

        let cropped_img = img.crop(x, y, width, height);
        self.image_to_data(cropped_img, &image_data.format)
    }

    /// Rotate an image
    pub fn rotate(
        &self,
        image_data: ImageData,
        angle: f32,
    ) -> TuShenResult<ImageData> {
        let img = self.load_image_from_bytes(&image_data.data)?;
        
        let rotated_img = match angle {
            90.0 => img.rotate90(),
            180.0 => img.rotate180(),
            270.0 => img.rotate270(),
            _ => {
                // For arbitrary angles, we'd need more complex rotation
                // For now, just return the original image
                img
            }
        };

        self.image_to_data(rotated_img, &image_data.format)
    }

    /// Adjust brightness
    pub fn adjust_brightness(
        &self,
        image_data: ImageData,
        brightness: i32,
    ) -> TuShenResult<ImageData> {
        let img = self.load_image_from_bytes(&image_data.data)?;
        let adjusted_img = img.brighten(brightness);
        self.image_to_data(adjusted_img, &image_data.format)
    }

    /// Adjust contrast
    pub fn adjust_contrast(
        &self,
        image_data: ImageData,
        contrast: f32,
    ) -> TuShenResult<ImageData> {
        let img = self.load_image_from_bytes(&image_data.data)?;
        let adjusted_img = img.adjust_contrast(contrast);
        self.image_to_data(adjusted_img, &image_data.format)
    }

    /// Load image from bytes
    fn load_image_from_bytes(&self, bytes: &[u8]) -> TuShenResult<DynamicImage> {
        image::load_from_memory(bytes)
            .map_err(|e| TuShenError::processing(format!("Failed to load image: {}", e)))
    }

    /// Convert DynamicImage to ImageData
    fn image_to_data(&self, img: DynamicImage, format: &ImageFormat) -> TuShenResult<ImageData> {
        let mut buffer = Vec::new();
        let mut cursor = Cursor::new(&mut buffer);

        let image_format = match format {
            ImageFormat::Jpeg => image::ImageFormat::Jpeg,
            ImageFormat::Png => image::ImageFormat::Png,
            ImageFormat::WebP => image::ImageFormat::WebP,
            ImageFormat::Bmp => image::ImageFormat::Bmp,
            ImageFormat::Gif => image::ImageFormat::Gif,
            ImageFormat::Tiff => image::ImageFormat::Tiff,
            _ => return Err(TuShenError::unsupported("Format conversion not supported")),
        };

        img.write_to(&mut cursor, image_format)
            .map_err(|e| TuShenError::processing(format!("Failed to encode image: {}", e)))?;

        Ok(ImageData::new(
            buffer,
            img.width(),
            img.height(),
            format.clone(),
        ))
    }

    /// Apply multiple processing operations
    async fn apply_processing_operations(
        &mut self,
        mut img: DynamicImage,
        options: &ProcessingOptions,
        original_format: &ImageFormat,
    ) -> TuShenResult<DynamicImage> {
        // Apply filters if specified
        if let Some(filter_type) = &options.filter_type {
            let image_data = self.image_to_data(img.clone(), original_format)?;
            let filtered_data = self.filter_engine.apply_filter(image_data, filter_type.clone(), options.filter_intensity).await?;
            img = self.load_image_from_bytes(&filtered_data.data)?;
        }

        // Apply brightness adjustment
        if options.brightness != 0 {
            img = img.brighten(options.brightness);
        }

        // Apply contrast adjustment
        if options.contrast != 1.0 {
            img = img.adjust_contrast(options.contrast);
        }

        // Apply resize if specified
        if let (Some(width), Some(height)) = (options.target_width, options.target_height) {
            img = if options.preserve_aspect_ratio {
                img.resize(width, height, image::imageops::FilterType::Lanczos3)
            } else {
                img.resize_exact(width, height, image::imageops::FilterType::Lanczos3)
            };
        }

        Ok(img)
    }
}

impl Default for ImageProcessor {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::utils::ImageFormat;
    use image::{ImageBuffer, Rgba, RgbaImage};

    #[tokio::test]
    async fn test_image_processor_creation() {
        let processor = ImageProcessor::new();
        // Just test that it can be created
        drop(processor);
    }

    #[test]
    fn test_resize() {
        let processor = ImageProcessor::new();
        
        // Create a simple test image
        let test_data = create_test_image_data();
        
        let result = processor.resize(test_data, 50, 50, true);
        assert!(result.is_ok());
        
        let resized = result.unwrap();
        assert_eq!(resized.width, 50);
        assert_eq!(resized.height, 50);
    }

    fn create_test_image_data() -> ImageData {
        // Create a simple 100x100 red image
        let img: RgbaImage = ImageBuffer::from_fn(100, 100, |_, _| {
            Rgba([255, 0, 0, 255])
        });
        
        let dynamic_img = DynamicImage::ImageRgba8(img);
        let mut buffer = Vec::new();
        let mut cursor = Cursor::new(&mut buffer);
        
        dynamic_img.write_to(&mut cursor, image::ImageFormat::Png).unwrap();
        
        ImageData::new(buffer, 100, 100, ImageFormat::Png)
    }
}
