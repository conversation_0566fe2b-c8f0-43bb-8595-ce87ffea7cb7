//! Image Authentication and Tampering Detection Engine
//! 
//! This module implements professional-grade algorithms for detecting image manipulation,
//! including Error Level Analysis (ELA), Copy-Move Detection, JPEG artifacts analysis,
//! and noise pattern analysis.

use crate::error::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>};
use crate::utils::ImageData;
use image::{DynamicImage, Rgb, RgbImage, GrayImage, Luma};
use std::collections::HashMap;
use serde::{Deserialize, Serialize};

/// Image authentication engine for detecting tampering and manipulation
pub struct ImageAuthenticator {
    /// Configuration for authentication algorithms
    pub config: AuthenticationConfig,
}

/// Configuration for image authentication algorithms
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AuthenticationConfig {
    /// ELA compression quality for analysis (1-100)
    pub ela_quality: u8,
    /// Threshold for copy-move detection
    pub copy_move_threshold: f32,
    /// Block size for analysis (8, 16, 32)
    pub block_size: u32,
    /// Noise analysis sensitivity (0.0-1.0)
    pub noise_sensitivity: f32,
    /// Enable metadata analysis
    pub analyze_metadata: bool,
}

/// Results of image authentication analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuthenticationResult {
    /// Overall authenticity score (0.0 = likely tampered, 1.0 = likely authentic)
    pub authenticity_score: f32,
    /// Individual analysis results
    pub analyses: Vec<AnalysisResult>,
    /// Detected manipulation regions (if any)
    pub manipulation_regions: Vec<ManipulationRegion>,
    /// Processing time in milliseconds
    pub processing_time_ms: u64,
    /// Generated analysis images for visualization
    pub analysis_images: HashMap<String, ImageData>,
}

/// Individual analysis result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnalysisResult {
    /// Type of analysis performed
    pub analysis_type: AnalysisType,
    /// Confidence score (0.0-1.0)
    pub confidence: f32,
    /// Detected anomalies
    pub anomalies: Vec<String>,
    /// Analysis-specific data
    pub metadata: HashMap<String, String>,
}

/// Types of image analysis
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AnalysisType {
    /// Error Level Analysis
    ErrorLevelAnalysis,
    /// Copy-Move Detection
    CopyMoveDetection,
    /// JPEG Compression Artifacts
    JpegArtifacts,
    /// Noise Pattern Analysis
    NoisePattern,
    /// Metadata Analysis
    MetadataAnalysis,
    /// Color Histogram Analysis
    ColorHistogram,
}

/// Detected manipulation region
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ManipulationRegion {
    /// Bounding box coordinates (x, y, width, height)
    pub bounds: (u32, u32, u32, u32),
    /// Confidence of manipulation (0.0-1.0)
    pub confidence: f32,
    /// Type of manipulation detected
    pub manipulation_type: ManipulationType,
    /// Additional details
    pub details: String,
}

/// Types of detected manipulation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ManipulationType {
    /// Copy-move (cloning)
    CopyMove,
    /// Splicing (composition)
    Splicing,
    /// Retouching
    Retouching,
    /// Compression inconsistency
    CompressionInconsistency,
    /// Unknown manipulation
    Unknown,
}

impl Default for AuthenticationConfig {
    fn default() -> Self {
        Self {
            ela_quality: 90,
            copy_move_threshold: 0.8,
            block_size: 16,
            noise_sensitivity: 0.5,
            analyze_metadata: true,
        }
    }
}

impl ImageAuthenticator {
    /// Create a new image authenticator with default configuration
    pub fn new() -> Self {
        Self {
            config: AuthenticationConfig::default(),
        }
    }

    /// Create a new image authenticator with custom configuration
    pub fn with_config(config: AuthenticationConfig) -> Self {
        Self { config }
    }

    /// Perform comprehensive image authentication analysis
    pub async fn authenticate(&self, image_data: ImageData) -> TuShenResult<AuthenticationResult> {
        let start_time = std::time::Instant::now();
        
        // Convert to DynamicImage for processing
        let dynamic_image = self.image_data_to_dynamic_image(&image_data)?;
        
        let mut analyses = Vec::new();
        let mut manipulation_regions = Vec::new();
        let mut analysis_images = HashMap::new();

        // 1. Error Level Analysis (ELA)
        if let Ok(ela_result) = self.perform_ela(&dynamic_image).await {
            analyses.push(ela_result.analysis);
            if let Some(ela_image) = ela_result.visualization {
                analysis_images.insert("ela".to_string(), ela_image);
            }
            manipulation_regions.extend(ela_result.regions);
        }

        // 2. Copy-Move Detection
        if let Ok(copy_move_result) = self.detect_copy_move(&dynamic_image).await {
            analyses.push(copy_move_result.analysis);
            if let Some(copy_move_image) = copy_move_result.visualization {
                analysis_images.insert("copy_move".to_string(), copy_move_image);
            }
            manipulation_regions.extend(copy_move_result.regions);
        }

        // 3. JPEG Artifacts Analysis
        if let Ok(jpeg_result) = self.analyze_jpeg_artifacts(&dynamic_image).await {
            analyses.push(jpeg_result.analysis);
            if let Some(jpeg_image) = jpeg_result.visualization {
                analysis_images.insert("jpeg_artifacts".to_string(), jpeg_image);
            }
            manipulation_regions.extend(jpeg_result.regions);
        }

        // 4. Noise Pattern Analysis
        if let Ok(noise_result) = self.analyze_noise_patterns(&dynamic_image).await {
            analyses.push(noise_result.analysis);
            if let Some(noise_image) = noise_result.visualization {
                analysis_images.insert("noise_pattern".to_string(), noise_image);
            }
            manipulation_regions.extend(noise_result.regions);
        }

        // 5. Metadata Analysis (if enabled)
        if self.config.analyze_metadata {
            if let Ok(metadata_result) = self.analyze_metadata(&image_data).await {
                analyses.push(metadata_result);
            }
        }

        // Calculate overall authenticity score
        let authenticity_score = self.calculate_authenticity_score(&analyses);

        let processing_time_ms = start_time.elapsed().as_millis() as u64;

        Ok(AuthenticationResult {
            authenticity_score,
            analyses,
            manipulation_regions,
            processing_time_ms,
            analysis_images,
        })
    }

    /// Convert ImageData to DynamicImage
    fn image_data_to_dynamic_image(&self, image_data: &ImageData) -> TuShenResult<DynamicImage> {
        image::load_from_memory(&image_data.data)
            .map_err(|e| TuShenError::internal(format!("Failed to decode image: {}", e)))
    }

    /// Convert DynamicImage to ImageData
    fn dynamic_image_to_image_data(&self, image: &DynamicImage, format: &str) -> TuShenResult<ImageData> {
        let mut buffer = Vec::new();
        let format = match format.to_lowercase().as_str() {
            "png" => image::ImageFormat::Png,
            "jpg" | "jpeg" => image::ImageFormat::Jpeg,
            _ => image::ImageFormat::Png,
        };
        
        image.write_to(&mut std::io::Cursor::new(&mut buffer), format)
            .map_err(|e| TuShenError::internal(format!("Failed to encode image: {}", e)))?;

        Ok(ImageData {
            data: buffer,
            width: image.width(),
            height: image.height(),
            format: match format {
                image::ImageFormat::Png => crate::utils::ImageFormat::Png,
                image::ImageFormat::Jpeg => crate::utils::ImageFormat::Jpeg,
                _ => crate::utils::ImageFormat::Png,
            },
            color_space: crate::utils::ColorSpace::Srgb,
            metadata: None,
        })
    }

    /// Calculate overall authenticity score from individual analyses
    fn calculate_authenticity_score(&self, analyses: &[AnalysisResult]) -> f32 {
        if analyses.is_empty() {
            return 0.5; // Neutral score if no analyses
        }

        let total_confidence: f32 = analyses.iter().map(|a| a.confidence).sum();
        let weighted_score: f32 = analyses.iter()
            .map(|a| {
                // Lower confidence in authenticity means higher likelihood of tampering
                let authenticity_indicator = match a.analysis_type {
                    AnalysisType::ErrorLevelAnalysis => 1.0 - a.confidence,
                    AnalysisType::CopyMoveDetection => 1.0 - a.confidence,
                    AnalysisType::JpegArtifacts => 1.0 - a.confidence,
                    AnalysisType::NoisePattern => 1.0 - a.confidence,
                    AnalysisType::MetadataAnalysis => a.confidence,
                    AnalysisType::ColorHistogram => 1.0 - a.confidence,
                };
                authenticity_indicator * a.confidence
            })
            .sum();

        (weighted_score / total_confidence).clamp(0.0, 1.0)
    }
}

/// Result of a specific analysis with visualization
struct AnalysisResultWithVisualization {
    analysis: AnalysisResult,
    visualization: Option<ImageData>,
    regions: Vec<ManipulationRegion>,
}

impl ImageAuthenticator {
    /// Perform Error Level Analysis (ELA)
    async fn perform_ela(&self, image: &DynamicImage) -> TuShenResult<AnalysisResultWithVisualization> {
        // Convert to RGB for processing
        let rgb_image = image.to_rgb8();

        // Re-compress the image at specified quality
        let mut compressed_buffer = Vec::new();
        let mut cursor = std::io::Cursor::new(&mut compressed_buffer);

        // Create JPEG encoder with specified quality
        let mut encoder = image::codecs::jpeg::JpegEncoder::new_with_quality(&mut cursor, self.config.ela_quality);
        encoder.encode(
            &rgb_image,
            rgb_image.width(),
            rgb_image.height(),
            image::ColorType::Rgb8,
        ).map_err(|e| TuShenError::internal(format!("ELA compression failed: {}", e)))?;

        // Load the compressed image
        let compressed_image = image::load_from_memory(&compressed_buffer)
            .map_err(|e| TuShenError::internal(format!("ELA decompression failed: {}", e)))?
            .to_rgb8();

        // Calculate error levels
        let ela_image = self.calculate_error_levels(&rgb_image, &compressed_image)?;

        // Analyze the ELA result for anomalies
        let (confidence, anomalies, regions) = self.analyze_ela_result(&ela_image)?;

        // Convert ELA image to ImageData for visualization
        let ela_dynamic = DynamicImage::ImageRgb8(ela_image);
        let visualization = Some(self.dynamic_image_to_image_data(&ela_dynamic, "png")?);

        let analysis = AnalysisResult {
            analysis_type: AnalysisType::ErrorLevelAnalysis,
            confidence,
            anomalies,
            metadata: HashMap::from([
                ("quality".to_string(), self.config.ela_quality.to_string()),
                ("regions_detected".to_string(), regions.len().to_string()),
            ]),
        };

        Ok(AnalysisResultWithVisualization {
            analysis,
            visualization,
            regions,
        })
    }

    /// Calculate error levels between original and compressed images
    fn calculate_error_levels(&self, original: &RgbImage, compressed: &RgbImage) -> TuShenResult<RgbImage> {
        if original.dimensions() != compressed.dimensions() {
            return Err(TuShenError::internal("Image dimensions mismatch".to_string()));
        }

        let (width, height) = original.dimensions();
        let mut ela_image = RgbImage::new(width, height);

        for (x, y, pixel) in ela_image.enumerate_pixels_mut() {
            let orig_pixel = original.get_pixel(x, y);
            let comp_pixel = compressed.get_pixel(x, y);

            // Calculate absolute difference and amplify
            let r_diff = ((orig_pixel[0] as i16 - comp_pixel[0] as i16).abs() * 10).min(255) as u8;
            let g_diff = ((orig_pixel[1] as i16 - comp_pixel[1] as i16).abs() * 10).min(255) as u8;
            let b_diff = ((orig_pixel[2] as i16 - comp_pixel[2] as i16).abs() * 10).min(255) as u8;

            *pixel = Rgb([r_diff, g_diff, b_diff]);
        }

        Ok(ela_image)
    }

    /// Analyze ELA result for anomalies
    fn analyze_ela_result(&self, ela_image: &RgbImage) -> TuShenResult<(f32, Vec<String>, Vec<ManipulationRegion>)> {
        let (width, height) = ela_image.dimensions();
        let mut anomalies = Vec::new();
        let mut regions = Vec::new();

        // Calculate average error level
        let mut total_error = 0u64;
        let mut high_error_pixels = 0u32;

        for pixel in ela_image.pixels() {
            let error_level = (pixel[0] as u32 + pixel[1] as u32 + pixel[2] as u32) / 3;
            total_error += error_level as u64;

            if error_level > 50 { // Threshold for high error
                high_error_pixels += 1;
            }
        }

        let avg_error = total_error as f32 / (width * height) as f32;
        let high_error_ratio = high_error_pixels as f32 / (width * height) as f32;

        // Determine confidence based on error patterns
        let confidence = if avg_error > 30.0 {
            0.8 // High confidence of tampering
        } else if avg_error > 15.0 {
            0.6 // Medium confidence
        } else {
            0.3 // Low confidence
        };

        if high_error_ratio > 0.1 {
            anomalies.push("High error level regions detected".to_string());
        }

        if avg_error > 25.0 {
            anomalies.push("Inconsistent compression artifacts".to_string());

            // Add a general manipulation region
            regions.push(ManipulationRegion {
                bounds: (0, 0, width, height),
                confidence: confidence,
                manipulation_type: ManipulationType::CompressionInconsistency,
                details: format!("Average error level: {:.2}", avg_error),
            });
        }

        Ok((confidence, anomalies, regions))
    }

    /// Detect copy-move tampering using block matching
    async fn detect_copy_move(&self, image: &DynamicImage) -> TuShenResult<AnalysisResultWithVisualization> {
        let gray_image = image.to_luma8();
        let (_width, _height) = gray_image.dimensions();
        let block_size = self.config.block_size;

        // Extract overlapping blocks
        let blocks = self.extract_blocks(&gray_image, block_size)?;

        // Find similar blocks using correlation
        let matches = self.find_similar_blocks(&blocks, self.config.copy_move_threshold)?;

        // Create visualization image
        let mut visualization = image.to_rgb8();
        let mut regions = Vec::new();
        let mut anomalies = Vec::new();

        // Mark detected copy-move regions
        for (block1, block2, similarity) in &matches {
            if *similarity > self.config.copy_move_threshold {
                // Draw rectangles around matched blocks
                self.draw_detection_rectangle(&mut visualization, *block1, block_size, [255, 0, 0]);
                self.draw_detection_rectangle(&mut visualization, *block2, block_size, [0, 255, 0]);

                regions.push(ManipulationRegion {
                    bounds: (
                        block1.0,
                        block1.1,
                        block_size,
                        block_size,
                    ),
                    confidence: *similarity,
                    manipulation_type: ManipulationType::CopyMove,
                    details: format!("Similarity: {:.3}", similarity),
                });

                anomalies.push(format!("Copy-move detected at ({}, {}) and ({}, {})",
                    block1.0, block1.1, block2.0, block2.1));
            }
        }

        let confidence = if matches.is_empty() {
            0.1 // Low confidence of tampering
        } else {
            (matches.len() as f32 * 0.2).min(0.9) // Scale with number of matches
        };

        let visualization_data = if !matches.is_empty() {
            let vis_dynamic = DynamicImage::ImageRgb8(visualization);
            Some(self.dynamic_image_to_image_data(&vis_dynamic, "png")?)
        } else {
            None
        };

        let analysis = AnalysisResult {
            analysis_type: AnalysisType::CopyMoveDetection,
            confidence,
            anomalies,
            metadata: HashMap::from([
                ("block_size".to_string(), block_size.to_string()),
                ("matches_found".to_string(), matches.len().to_string()),
                ("threshold".to_string(), self.config.copy_move_threshold.to_string()),
            ]),
        };

        Ok(AnalysisResultWithVisualization {
            analysis,
            visualization: visualization_data,
            regions,
        })
    }

    /// Extract overlapping blocks from grayscale image
    fn extract_blocks(&self, image: &GrayImage, block_size: u32) -> TuShenResult<Vec<((u32, u32), Vec<u8>)>> {
        let (width, height) = image.dimensions();
        let mut blocks = Vec::new();

        let step = block_size / 2; // 50% overlap

        for y in (0..height.saturating_sub(block_size)).step_by(step as usize) {
            for x in (0..width.saturating_sub(block_size)).step_by(step as usize) {
                let mut block_data = Vec::with_capacity((block_size * block_size) as usize);

                for by in 0..block_size {
                    for bx in 0..block_size {
                        if let Some(pixel) = image.get_pixel_checked(x + bx, y + by) {
                            block_data.push(pixel[0]);
                        }
                    }
                }

                if block_data.len() == (block_size * block_size) as usize {
                    blocks.push(((x, y), block_data));
                }
            }
        }

        Ok(blocks)
    }

    /// Find similar blocks using normalized cross-correlation
    fn find_similar_blocks(&self, blocks: &[((u32, u32), Vec<u8>)], threshold: f32) -> TuShenResult<Vec<((u32, u32), (u32, u32), f32)>> {
        let mut matches = Vec::new();

        for i in 0..blocks.len() {
            for j in (i + 1)..blocks.len() {
                let (pos1, block1) = &blocks[i];
                let (pos2, block2) = &blocks[j];

                // Skip if blocks are too close (likely not copy-move)
                let distance = ((pos1.0 as i32 - pos2.0 as i32).pow(2) +
                               (pos1.1 as i32 - pos2.1 as i32).pow(2)) as f32;
                if distance.sqrt() < self.config.block_size as f32 * 2.0 {
                    continue;
                }

                let similarity = self.calculate_block_similarity(block1, block2)?;

                if similarity > threshold {
                    matches.push((*pos1, *pos2, similarity));
                }
            }
        }

        Ok(matches)
    }

    /// Calculate normalized cross-correlation between two blocks
    fn calculate_block_similarity(&self, block1: &[u8], block2: &[u8]) -> TuShenResult<f32> {
        if block1.len() != block2.len() {
            return Ok(0.0);
        }

        let n = block1.len() as f32;

        // Calculate means
        let mean1 = block1.iter().map(|&x| x as f32).sum::<f32>() / n;
        let mean2 = block2.iter().map(|&x| x as f32).sum::<f32>() / n;

        // Calculate correlation coefficient
        let mut numerator = 0.0;
        let mut sum_sq1 = 0.0;
        let mut sum_sq2 = 0.0;

        for i in 0..block1.len() {
            let diff1 = block1[i] as f32 - mean1;
            let diff2 = block2[i] as f32 - mean2;

            numerator += diff1 * diff2;
            sum_sq1 += diff1 * diff1;
            sum_sq2 += diff2 * diff2;
        }

        let denominator = (sum_sq1 * sum_sq2).sqrt();

        if denominator == 0.0 {
            Ok(0.0)
        } else {
            Ok((numerator / denominator).abs())
        }
    }

    /// Draw detection rectangle on RGB image
    fn draw_detection_rectangle(&self, image: &mut RgbImage, pos: (u32, u32), size: u32, color: [u8; 3]) {
        let (x, y) = pos;
        let (width, height) = image.dimensions();

        // Draw rectangle border
        for i in 0..size {
            // Top and bottom borders
            if x + i < width {
                if y < height {
                    image.put_pixel(x + i, y, Rgb(color));
                }
                if y + size - 1 < height {
                    image.put_pixel(x + i, y + size - 1, Rgb(color));
                }
            }

            // Left and right borders
            if y + i < height {
                if x < width {
                    image.put_pixel(x, y + i, Rgb(color));
                }
                if x + size - 1 < width {
                    image.put_pixel(x + size - 1, y + i, Rgb(color));
                }
            }
        }
    }

    /// Analyze JPEG compression artifacts for inconsistencies
    async fn analyze_jpeg_artifacts(&self, image: &DynamicImage) -> TuShenResult<AnalysisResultWithVisualization> {
        let gray_image = image.to_luma8();
        let (width, height) = gray_image.dimensions();

        // Analyze 8x8 DCT blocks for JPEG artifacts
        let mut artifact_map = GrayImage::new(width, height);
        let mut anomalies = Vec::new();
        let mut regions = Vec::new();

        let mut total_artifact_score = 0.0;
        let mut block_count = 0;

        // Process 8x8 blocks (JPEG standard)
        for y in (0..height.saturating_sub(8)).step_by(8) {
            for x in (0..width.saturating_sub(8)).step_by(8) {
                let artifact_score = self.analyze_dct_block(&gray_image, x, y)?;
                total_artifact_score += artifact_score;
                block_count += 1;

                // Mark high artifact areas
                let intensity = (artifact_score * 255.0).min(255.0) as u8;
                for by in 0..8 {
                    for bx in 0..8 {
                        if x + bx < width && y + by < height {
                            artifact_map.put_pixel(x + bx, y + by, Luma([intensity]));
                        }
                    }
                }

                // Detect suspicious blocks
                if artifact_score > 0.7 {
                    regions.push(ManipulationRegion {
                        bounds: (x, y, 8, 8),
                        confidence: artifact_score,
                        manipulation_type: ManipulationType::CompressionInconsistency,
                        details: format!("Artifact score: {:.3}", artifact_score),
                    });
                }
            }
        }

        let avg_artifact_score = if block_count > 0 {
            total_artifact_score / block_count as f32
        } else {
            0.0
        };

        if avg_artifact_score > 0.5 {
            anomalies.push("Inconsistent JPEG compression detected".to_string());
        }

        if regions.len() > block_count / 10 {
            anomalies.push("Multiple compression inconsistencies found".to_string());
        }

        let confidence = avg_artifact_score;

        let visualization = if !regions.is_empty() {
            let vis_dynamic = DynamicImage::ImageLuma8(artifact_map);
            Some(self.dynamic_image_to_image_data(&vis_dynamic, "png")?)
        } else {
            None
        };

        let analysis = AnalysisResult {
            analysis_type: AnalysisType::JpegArtifacts,
            confidence,
            anomalies,
            metadata: HashMap::from([
                ("avg_artifact_score".to_string(), format!("{:.3}", avg_artifact_score)),
                ("suspicious_blocks".to_string(), regions.len().to_string()),
                ("total_blocks".to_string(), block_count.to_string()),
            ]),
        };

        Ok(AnalysisResultWithVisualization {
            analysis,
            visualization,
            regions,
        })
    }

    /// Analyze DCT block for compression artifacts
    fn analyze_dct_block(&self, image: &GrayImage, x: u32, y: u32) -> TuShenResult<f32> {
        let mut block = [[0.0f32; 8]; 8];

        // Extract 8x8 block
        for by in 0..8 {
            for bx in 0..8 {
                if let Some(pixel) = image.get_pixel_checked(x + bx, y + by) {
                    block[by as usize][bx as usize] = pixel[0] as f32;
                }
            }
        }

        // Simple artifact detection based on high-frequency content
        let mut high_freq_energy = 0.0;
        let mut total_energy = 0.0;

        for i in 0..8 {
            for j in 0..8 {
                let energy = block[i][j] * block[i][j];
                total_energy += energy;

                // High frequency components (bottom-right of DCT)
                if i + j > 8 {
                    high_freq_energy += energy;
                }
            }
        }

        if total_energy == 0.0 {
            Ok(0.0)
        } else {
            Ok((high_freq_energy / total_energy).min(1.0))
        }
    }

    /// Analyze noise patterns for inconsistencies
    async fn analyze_noise_patterns(&self, image: &DynamicImage) -> TuShenResult<AnalysisResultWithVisualization> {
        let gray_image = image.to_luma8();
        let (width, height) = gray_image.dimensions();

        // Calculate local noise variance
        let noise_map = self.calculate_noise_variance(&gray_image)?;

        // Analyze noise consistency
        let (confidence, anomalies, regions) = self.analyze_noise_consistency(&noise_map)?;

        let visualization = if confidence > 0.5 {
            // Convert noise map to visible image
            let mut vis_image = GrayImage::new(width, height);
            let max_variance = noise_map.iter().fold(0.0f32, |acc, &x| acc.max(x));

            for (i, &variance) in noise_map.iter().enumerate() {
                let x = (i % width as usize) as u32;
                let y = (i / width as usize) as u32;
                let intensity = if max_variance > 0.0 {
                    ((variance / max_variance) * 255.0) as u8
                } else {
                    0
                };
                vis_image.put_pixel(x, y, Luma([intensity]));
            }

            let vis_dynamic = DynamicImage::ImageLuma8(vis_image);
            Some(self.dynamic_image_to_image_data(&vis_dynamic, "png")?)
        } else {
            None
        };

        let analysis = AnalysisResult {
            analysis_type: AnalysisType::NoisePattern,
            confidence,
            anomalies,
            metadata: HashMap::from([
                ("sensitivity".to_string(), self.config.noise_sensitivity.to_string()),
                ("regions_detected".to_string(), regions.len().to_string()),
            ]),
        };

        Ok(AnalysisResultWithVisualization {
            analysis,
            visualization,
            regions,
        })
    }

    /// Calculate local noise variance using a sliding window
    fn calculate_noise_variance(&self, image: &GrayImage) -> TuShenResult<Vec<f32>> {
        let (width, height) = image.dimensions();
        let window_size = 5u32;
        let mut variances = vec![0.0f32; (width * height) as usize];

        for y in 0..height {
            for x in 0..width {
                let mut values = Vec::new();

                // Collect values in window
                for wy in y.saturating_sub(window_size/2)..=(y + window_size/2).min(height - 1) {
                    for wx in x.saturating_sub(window_size/2)..=(x + window_size/2).min(width - 1) {
                        if let Some(pixel) = image.get_pixel_checked(wx, wy) {
                            values.push(pixel[0] as f32);
                        }
                    }
                }

                // Calculate variance
                if values.len() > 1 {
                    let mean = values.iter().sum::<f32>() / values.len() as f32;
                    let variance = values.iter()
                        .map(|&v| (v - mean).powi(2))
                        .sum::<f32>() / values.len() as f32;

                    variances[(y * width + x) as usize] = variance;
                }
            }
        }

        Ok(variances)
    }

    /// Analyze noise consistency across the image
    fn analyze_noise_consistency(&self, noise_map: &[f32]) -> TuShenResult<(f32, Vec<String>, Vec<ManipulationRegion>)> {
        if noise_map.is_empty() {
            return Ok((0.0, Vec::new(), Vec::new()));
        }

        // Calculate statistics
        let mean_noise = noise_map.iter().sum::<f32>() / noise_map.len() as f32;
        let noise_std = {
            let variance = noise_map.iter()
                .map(|&x| (x - mean_noise).powi(2))
                .sum::<f32>() / noise_map.len() as f32;
            variance.sqrt()
        };

        let mut anomalies = Vec::new();
        let regions = Vec::new();

        // Detect areas with significantly different noise
        let threshold = mean_noise + 2.0 * noise_std;
        let anomalous_pixels = noise_map.iter().filter(|&&x| x > threshold).count();
        let anomaly_ratio = anomalous_pixels as f32 / noise_map.len() as f32;

        let confidence = if anomaly_ratio > 0.1 {
            (anomaly_ratio * 2.0).min(0.9)
        } else {
            0.1
        };

        if anomaly_ratio > 0.05 {
            anomalies.push("Inconsistent noise patterns detected".to_string());
        }

        if noise_std > mean_noise * 0.5 {
            anomalies.push("High noise variance across image".to_string());
        }

        Ok((confidence, anomalies, regions))
    }

    /// Analyze image metadata for tampering indicators
    async fn analyze_metadata(&self, image_data: &ImageData) -> TuShenResult<AnalysisResult> {
        let anomalies = Vec::new();
        let mut metadata = HashMap::new();

        // This is a simplified metadata analysis
        // In a real implementation, you would use a library like exif-rs

        metadata.insert("format".to_string(), format!("{:?}", image_data.format));
        metadata.insert("size".to_string(), image_data.data.len().to_string());

        // Check for common tampering indicators in metadata
        let confidence = 0.5; // Neutral confidence for basic metadata analysis

        let analysis = AnalysisResult {
            analysis_type: AnalysisType::MetadataAnalysis,
            confidence,
            anomalies,
            metadata,
        };

        Ok(analysis)
    }
}
