// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.9.0.

#![allow(
    non_camel_case_types,
    unused,
    non_snake_case,
    clippy::needless_return,
    clippy::redundant_closure_call,
    clippy::redundant_closure,
    clippy::useless_conversion,
    clippy::unit_arg,
    clippy::unused_unit,
    clippy::double_parens,
    clippy::let_and_return,
    clippy::too_many_arguments,
    clippy::match_single_binding,
    clippy::clone_on_copy,
    clippy::let_unit_value,
    clippy::deref_addrof,
    clippy::explicit_auto_deref,
    clippy::borrow_deref_ref,
    clippy::needless_borrow
)]

// Section: imports

use crate::bridge::*;
use flutter_rust_bridge::for_generated::byteorder::{NativeEndian, ReadBytesExt, WriteBytesExt};
use flutter_rust_bridge::for_generated::{transform_result_dco, Lifetimeable, Lockable};
use flutter_rust_bridge::{<PERSON><PERSON>, IntoIntoDart};

// Section: boilerplate

flutter_rust_bridge::frb_generated_boilerplate!(
    default_stream_sink_codec = SseCodec,
    default_rust_opaque = RustOpaqueMoi,
    default_rust_auto_opaque = RustAutoOpaqueMoi,
);
pub(crate) const FLUTTER_RUST_BRIDGE_CODEGEN_VERSION: &str = "2.9.0";
pub(crate) const FLUTTER_RUST_BRIDGE_CODEGEN_CONTENT_HASH: i32 = 1768860821;

// Section: executor

flutter_rust_bridge::frb_generated_default_handler!();

// Section: wire_funcs

fn wire__crate__bridge__FilterEngineBridge_apply_filter_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "FilterEngineBridge_apply_filter",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<FilterEngineBridge>,
            >>::sse_decode(&mut deserializer);
            let api_image = <crate::bridge::ImageDataBridge>::sse_decode(&mut deserializer);
            let api_filter_type = <String>::sse_decode(&mut deserializer);
            let api_intensity = <f32>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, String>(
                    (move || async move {
                        let mut api_that_guard = None;
                        let decode_indices_ =
                            flutter_rust_bridge::for_generated::lockable_compute_decode_order(
                                vec![flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                                    &api_that, 0, false,
                                )],
                            );
                        for i in decode_indices_ {
                            match i {
                                0 => {
                                    api_that_guard =
                                        Some(api_that.lockable_decode_async_ref().await)
                                }
                                _ => unreachable!(),
                            }
                        }
                        let api_that_guard = api_that_guard.unwrap();
                        let output_ok = crate::bridge::FilterEngineBridge::apply_filter(
                            &*api_that_guard,
                            api_image,
                            api_filter_type,
                            api_intensity,
                        )
                        .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__bridge__FilterEngineBridge_get_available_filters_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "FilterEngineBridge_get_available_filters",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<FilterEngineBridge>,
            >>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let mut api_that_guard = None;
                let decode_indices_ =
                    flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                        flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                            &api_that, 0, false,
                        ),
                    ]);
                for i in decode_indices_ {
                    match i {
                        0 => api_that_guard = Some(api_that.lockable_decode_sync_ref()),
                        _ => unreachable!(),
                    }
                }
                let api_that_guard = api_that_guard.unwrap();
                let output_ok = Result::<_, ()>::Ok(
                    crate::bridge::FilterEngineBridge::get_available_filters(&*api_that_guard),
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__bridge__FilterEngineBridge_get_filter_preview_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "FilterEngineBridge_get_filter_preview",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<FilterEngineBridge>,
            >>::sse_decode(&mut deserializer);
            let api_image = <crate::bridge::ImageDataBridge>::sse_decode(&mut deserializer);
            let api_filter_type = <String>::sse_decode(&mut deserializer);
            let api_preview_size = <u32>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, String>(
                    (move || async move {
                        let mut api_that_guard = None;
                        let decode_indices_ =
                            flutter_rust_bridge::for_generated::lockable_compute_decode_order(
                                vec![flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                                    &api_that, 0, false,
                                )],
                            );
                        for i in decode_indices_ {
                            match i {
                                0 => {
                                    api_that_guard =
                                        Some(api_that.lockable_decode_async_ref().await)
                                }
                                _ => unreachable!(),
                            }
                        }
                        let api_that_guard = api_that_guard.unwrap();
                        let output_ok = crate::bridge::FilterEngineBridge::get_filter_preview(
                            &*api_that_guard,
                            api_image,
                            api_filter_type,
                            api_preview_size,
                        )
                        .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__bridge__FilterEngineBridge_new_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "FilterEngineBridge_new",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let output_ok = Result::<_, ()>::Ok(crate::bridge::FilterEngineBridge::new())?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__bridge__authentication_config_bridge_default_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_normal::<flutter_rust_bridge::for_generated::SseCodec, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "authentication_config_bridge_default",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            deserializer.end();
            move |context| {
                transform_result_sse::<_, ()>((move || {
                    let output_ok =
                        Result::<_, ()>::Ok(crate::bridge::AuthenticationConfigBridge::default())?;
                    Ok(output_ok)
                })())
            }
        },
    )
}
fn wire__crate__bridge__collage_engine_bridge_create_collage_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "collage_engine_bridge_create_collage",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <crate::bridge::CollageEngineBridge>::sse_decode(&mut deserializer);
            let api_images = <Vec<crate::bridge::ImageDataBridge>>::sse_decode(&mut deserializer);
            let api__layout = <String>::sse_decode(&mut deserializer);
            let api__canvas_width = <u32>::sse_decode(&mut deserializer);
            let api__canvas_height = <u32>::sse_decode(&mut deserializer);
            let api__spacing = <u32>::sse_decode(&mut deserializer);
            let api__background_color = <Option<String>>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, String>(
                    (move || async move {
                        let output_ok = crate::bridge::CollageEngineBridge::create_collage(
                            &api_that,
                            api_images,
                            api__layout,
                            api__canvas_width,
                            api__canvas_height,
                            api__spacing,
                            api__background_color,
                        )
                        .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__bridge__collage_engine_bridge_get_available_layouts_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "collage_engine_bridge_get_available_layouts",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <crate::bridge::CollageEngineBridge>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let output_ok = Result::<_, ()>::Ok(
                    crate::bridge::CollageEngineBridge::get_available_layouts(&api_that),
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__bridge__collage_engine_bridge_get_layout_preview_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "collage_engine_bridge_get_layout_preview",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <crate::bridge::CollageEngineBridge>::sse_decode(&mut deserializer);
            let api__layout = <String>::sse_decode(&mut deserializer);
            let api_canvas_width = <u32>::sse_decode(&mut deserializer);
            let api_canvas_height = <u32>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, String>(
                    (move || async move {
                        let output_ok = crate::bridge::CollageEngineBridge::get_layout_preview(
                            &api_that,
                            api__layout,
                            api_canvas_width,
                            api_canvas_height,
                        )
                        .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__bridge__collage_engine_bridge_new_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "collage_engine_bridge_new",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let output_ok = Result::<_, ()>::Ok(crate::bridge::CollageEngineBridge::new())?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__bridge__get_library_info_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "get_library_info",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let output_ok = Result::<_, ()>::Ok(crate::bridge::get_library_info())?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__bridge__image_authenticator_bridge_authenticate_image_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "image_authenticator_bridge_authenticate_image",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <crate::bridge::ImageAuthenticatorBridge>::sse_decode(&mut deserializer);
            let api_image_data = <crate::bridge::ImageDataBridge>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, String>(
                    (move || async move {
                        let output_ok =
                            crate::bridge::ImageAuthenticatorBridge::authenticate_image(
                                &api_that,
                                api_image_data,
                            )
                            .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__bridge__image_authenticator_bridge_new_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "image_authenticator_bridge_new",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let output_ok =
                    Result::<_, ()>::Ok(crate::bridge::ImageAuthenticatorBridge::new())?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__bridge__image_authenticator_bridge_with_config_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "image_authenticator_bridge_with_config",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_config =
                <crate::bridge::AuthenticationConfigBridge>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let output_ok = Result::<_, ()>::Ok(
                    crate::bridge::ImageAuthenticatorBridge::with_config(api_config),
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__bridge__image_compressor_bridge_compress_image_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "image_compressor_bridge_compress_image",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <crate::bridge::ImageCompressorBridge>::sse_decode(&mut deserializer);
            let api_image = <crate::bridge::ImageDataBridge>::sse_decode(&mut deserializer);
            let api_format = <String>::sse_decode(&mut deserializer);
            let api_quality = <u8>::sse_decode(&mut deserializer);
            let api_target_size_kb = <Option<u32>>::sse_decode(&mut deserializer);
            let api_resize_width = <Option<u32>>::sse_decode(&mut deserializer);
            let api_resize_height = <Option<u32>>::sse_decode(&mut deserializer);
            let api_maintain_aspect_ratio = <bool>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, String>(
                    (move || async move {
                        let output_ok = crate::bridge::ImageCompressorBridge::compress_image(
                            &api_that,
                            api_image,
                            api_format,
                            api_quality,
                            api_target_size_kb,
                            api_resize_width,
                            api_resize_height,
                            api_maintain_aspect_ratio,
                        )
                        .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__bridge__image_compressor_bridge_estimate_compression_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "image_compressor_bridge_estimate_compression",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <crate::bridge::ImageCompressorBridge>::sse_decode(&mut deserializer);
            let api_image = <crate::bridge::ImageDataBridge>::sse_decode(&mut deserializer);
            let api_format = <String>::sse_decode(&mut deserializer);
            let api_quality = <u8>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, String>(
                    (move || async move {
                        let output_ok = crate::bridge::ImageCompressorBridge::estimate_compression(
                            &api_that,
                            api_image,
                            api_format,
                            api_quality,
                        )
                        .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__bridge__image_compressor_bridge_get_supported_formats_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "image_compressor_bridge_get_supported_formats",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <crate::bridge::ImageCompressorBridge>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let output_ok = Result::<_, ()>::Ok(
                    crate::bridge::ImageCompressorBridge::get_supported_formats(&api_that),
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__bridge__image_compressor_bridge_new_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "image_compressor_bridge_new",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let output_ok = Result::<_, ()>::Ok(crate::bridge::ImageCompressorBridge::new())?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__bridge__image_processor_bridge_crop_image_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "image_processor_bridge_crop_image",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <crate::bridge::ImageProcessorBridge>::sse_decode(&mut deserializer);
            let api_image = <crate::bridge::ImageDataBridge>::sse_decode(&mut deserializer);
            let api_x = <u32>::sse_decode(&mut deserializer);
            let api_y = <u32>::sse_decode(&mut deserializer);
            let api_width = <u32>::sse_decode(&mut deserializer);
            let api_height = <u32>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, String>(
                    (move || async move {
                        let output_ok = crate::bridge::ImageProcessorBridge::crop_image(
                            &api_that, api_image, api_x, api_y, api_width, api_height,
                        )
                        .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__bridge__image_processor_bridge_load_image_from_bytes_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "image_processor_bridge_load_image_from_bytes",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <crate::bridge::ImageProcessorBridge>::sse_decode(&mut deserializer);
            let api_bytes = <Vec<u8>>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, String>(
                    (move || async move {
                        let output_ok = crate::bridge::ImageProcessorBridge::load_image_from_bytes(
                            &api_that, api_bytes,
                        )
                        .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__bridge__image_processor_bridge_load_image_from_path_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "image_processor_bridge_load_image_from_path",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <crate::bridge::ImageProcessorBridge>::sse_decode(&mut deserializer);
            let api_path = <String>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, String>(
                    (move || async move {
                        let output_ok = crate::bridge::ImageProcessorBridge::load_image_from_path(
                            &api_that, api_path,
                        )
                        .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__bridge__image_processor_bridge_new_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "image_processor_bridge_new",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let output_ok = Result::<_, ()>::Ok(crate::bridge::ImageProcessorBridge::new())?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__bridge__image_processor_bridge_resize_image_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "image_processor_bridge_resize_image",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <crate::bridge::ImageProcessorBridge>::sse_decode(&mut deserializer);
            let api_image = <crate::bridge::ImageDataBridge>::sse_decode(&mut deserializer);
            let api_width = <u32>::sse_decode(&mut deserializer);
            let api_height = <u32>::sse_decode(&mut deserializer);
            let api_maintain_aspect_ratio = <bool>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, String>(
                    (move || async move {
                        let output_ok = crate::bridge::ImageProcessorBridge::resize_image(
                            &api_that,
                            api_image,
                            api_width,
                            api_height,
                            api_maintain_aspect_ratio,
                        )
                        .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__bridge__image_processor_bridge_rotate_image_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "image_processor_bridge_rotate_image",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <crate::bridge::ImageProcessorBridge>::sse_decode(&mut deserializer);
            let api_image = <crate::bridge::ImageDataBridge>::sse_decode(&mut deserializer);
            let api_angle = <f32>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, String>(
                    (move || async move {
                        let output_ok = crate::bridge::ImageProcessorBridge::rotate_image(
                            &api_that, api_image, api_angle,
                        )
                        .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__bridge__image_processor_bridge_save_image_to_bytes_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "image_processor_bridge_save_image_to_bytes",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <crate::bridge::ImageProcessorBridge>::sse_decode(&mut deserializer);
            let api_image = <crate::bridge::ImageDataBridge>::sse_decode(&mut deserializer);
            let api__format = <String>::sse_decode(&mut deserializer);
            let api__quality = <Option<u8>>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, String>(
                    (move || async move {
                        let output_ok = crate::bridge::ImageProcessorBridge::save_image_to_bytes(
                            &api_that,
                            api_image,
                            api__format,
                            api__quality,
                        )
                        .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__bridge__init_tushen_core_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "init_tushen_core",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            deserializer.end();
            transform_result_sse::<_, String>((move || {
                let output_ok = crate::bridge::init_tushen_core()?;
                Ok(output_ok)
            })())
        },
    )
}

// Section: related_funcs

flutter_rust_bridge::frb_generated_moi_arc_impl_value!(
    flutter_rust_bridge::for_generated::RustAutoOpaqueInner<FilterEngineBridge>
);

// Section: dart2rust

impl SseDecode for FilterEngineBridge {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut inner = <RustOpaqueMoi<
            flutter_rust_bridge::for_generated::RustAutoOpaqueInner<FilterEngineBridge>,
        >>::sse_decode(deserializer);
        return flutter_rust_bridge::for_generated::rust_auto_opaque_decode_owned(inner);
    }
}

impl SseDecode
    for RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<FilterEngineBridge>>
{
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut inner = <usize>::sse_decode(deserializer);
        return decode_rust_opaque_moi(inner);
    }
}

impl SseDecode for String {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut inner = <Vec<u8>>::sse_decode(deserializer);
        return String::from_utf8(inner).unwrap();
    }
}

impl SseDecode for crate::bridge::AnalysisImageBridge {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut var_name = <String>::sse_decode(deserializer);
        let mut var_imageData = <crate::bridge::ImageDataBridge>::sse_decode(deserializer);
        return crate::bridge::AnalysisImageBridge {
            name: var_name,
            image_data: var_imageData,
        };
    }
}

impl SseDecode for crate::bridge::AnalysisResultBridge {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut var_analysisType = <String>::sse_decode(deserializer);
        let mut var_confidence = <f32>::sse_decode(deserializer);
        let mut var_anomalies = <Vec<String>>::sse_decode(deserializer);
        let mut var_metadata = <Vec<crate::bridge::MetadataPairBridge>>::sse_decode(deserializer);
        return crate::bridge::AnalysisResultBridge {
            analysis_type: var_analysisType,
            confidence: var_confidence,
            anomalies: var_anomalies,
            metadata: var_metadata,
        };
    }
}

impl SseDecode for crate::core::image_authenticator::AuthenticationConfig {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut var_elaQuality = <u8>::sse_decode(deserializer);
        let mut var_copyMoveThreshold = <f32>::sse_decode(deserializer);
        let mut var_blockSize = <u32>::sse_decode(deserializer);
        let mut var_noiseSensitivity = <f32>::sse_decode(deserializer);
        let mut var_analyzeMetadata = <bool>::sse_decode(deserializer);
        return crate::core::image_authenticator::AuthenticationConfig {
            ela_quality: var_elaQuality,
            copy_move_threshold: var_copyMoveThreshold,
            block_size: var_blockSize,
            noise_sensitivity: var_noiseSensitivity,
            analyze_metadata: var_analyzeMetadata,
        };
    }
}

impl SseDecode for crate::bridge::AuthenticationConfigBridge {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut var_elaQuality = <u8>::sse_decode(deserializer);
        let mut var_copyMoveThreshold = <f32>::sse_decode(deserializer);
        let mut var_blockSize = <u32>::sse_decode(deserializer);
        let mut var_noiseSensitivity = <f32>::sse_decode(deserializer);
        let mut var_analyzeMetadata = <bool>::sse_decode(deserializer);
        return crate::bridge::AuthenticationConfigBridge {
            ela_quality: var_elaQuality,
            copy_move_threshold: var_copyMoveThreshold,
            block_size: var_blockSize,
            noise_sensitivity: var_noiseSensitivity,
            analyze_metadata: var_analyzeMetadata,
        };
    }
}

impl SseDecode for crate::bridge::AuthenticationResultBridge {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut var_authenticityScore = <f32>::sse_decode(deserializer);
        let mut var_analyses = <Vec<crate::bridge::AnalysisResultBridge>>::sse_decode(deserializer);
        let mut var_manipulationRegions =
            <Vec<crate::bridge::ManipulationRegionBridge>>::sse_decode(deserializer);
        let mut var_processingTimeMs = <u64>::sse_decode(deserializer);
        let mut var_analysisImages =
            <Vec<crate::bridge::AnalysisImageBridge>>::sse_decode(deserializer);
        return crate::bridge::AuthenticationResultBridge {
            authenticity_score: var_authenticityScore,
            analyses: var_analyses,
            manipulation_regions: var_manipulationRegions,
            processing_time_ms: var_processingTimeMs,
            analysis_images: var_analysisImages,
        };
    }
}

impl SseDecode for bool {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        deserializer.cursor.read_u8().unwrap() != 0
    }
}

impl SseDecode for crate::bridge::CollageEngineBridge {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        return crate::bridge::CollageEngineBridge {};
    }
}

impl SseDecode for crate::bridge::CompressionEstimateBridge {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut var_estimatedSize = <u64>::sse_decode(deserializer);
        let mut var_compressionRatio = <f32>::sse_decode(deserializer);
        let mut var_spaceSaved = <u64>::sse_decode(deserializer);
        return crate::bridge::CompressionEstimateBridge {
            estimated_size: var_estimatedSize,
            compression_ratio: var_compressionRatio,
            space_saved: var_spaceSaved,
        };
    }
}

impl SseDecode for crate::bridge::CompressionResultBridge {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut var_imageData = <crate::bridge::ImageDataBridge>::sse_decode(deserializer);
        let mut var_originalSize = <u64>::sse_decode(deserializer);
        let mut var_compressedSize = <u64>::sse_decode(deserializer);
        let mut var_compressionRatio = <f32>::sse_decode(deserializer);
        let mut var_spaceSaved = <u64>::sse_decode(deserializer);
        let mut var_spaceSavedPercentage = <f32>::sse_decode(deserializer);
        let mut var_finalQuality = <u8>::sse_decode(deserializer);
        let mut var_iterations = <u32>::sse_decode(deserializer);
        let mut var_targetAchieved = <bool>::sse_decode(deserializer);
        return crate::bridge::CompressionResultBridge {
            image_data: var_imageData,
            original_size: var_originalSize,
            compressed_size: var_compressedSize,
            compression_ratio: var_compressionRatio,
            space_saved: var_spaceSaved,
            space_saved_percentage: var_spaceSavedPercentage,
            final_quality: var_finalQuality,
            iterations: var_iterations,
            target_achieved: var_targetAchieved,
        };
    }
}

impl SseDecode for f32 {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        deserializer.cursor.read_f32::<NativeEndian>().unwrap()
    }
}

impl SseDecode for crate::core::image_authenticator::ImageAuthenticator {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut var_config =
            <crate::core::image_authenticator::AuthenticationConfig>::sse_decode(deserializer);
        return crate::core::image_authenticator::ImageAuthenticator { config: var_config };
    }
}

impl SseDecode for crate::bridge::ImageAuthenticatorBridge {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut var_authenticator =
            <crate::core::image_authenticator::ImageAuthenticator>::sse_decode(deserializer);
        return crate::bridge::ImageAuthenticatorBridge {
            authenticator: var_authenticator,
        };
    }
}

impl SseDecode for crate::bridge::ImageCompressorBridge {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        return crate::bridge::ImageCompressorBridge {};
    }
}

impl SseDecode for crate::bridge::ImageDataBridge {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut var_width = <u32>::sse_decode(deserializer);
        let mut var_height = <u32>::sse_decode(deserializer);
        let mut var_format = <String>::sse_decode(deserializer);
        let mut var_colorSpace = <String>::sse_decode(deserializer);
        let mut var_data = <Vec<u8>>::sse_decode(deserializer);
        return crate::bridge::ImageDataBridge {
            width: var_width,
            height: var_height,
            format: var_format,
            color_space: var_colorSpace,
            data: var_data,
        };
    }
}

impl SseDecode for crate::bridge::ImageProcessorBridge {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        return crate::bridge::ImageProcessorBridge {};
    }
}

impl SseDecode for Vec<String> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut len_ = <i32>::sse_decode(deserializer);
        let mut ans_ = vec![];
        for idx_ in 0..len_ {
            ans_.push(<String>::sse_decode(deserializer));
        }
        return ans_;
    }
}

impl SseDecode for Vec<crate::bridge::AnalysisImageBridge> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut len_ = <i32>::sse_decode(deserializer);
        let mut ans_ = vec![];
        for idx_ in 0..len_ {
            ans_.push(<crate::bridge::AnalysisImageBridge>::sse_decode(
                deserializer,
            ));
        }
        return ans_;
    }
}

impl SseDecode for Vec<crate::bridge::AnalysisResultBridge> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut len_ = <i32>::sse_decode(deserializer);
        let mut ans_ = vec![];
        for idx_ in 0..len_ {
            ans_.push(<crate::bridge::AnalysisResultBridge>::sse_decode(
                deserializer,
            ));
        }
        return ans_;
    }
}

impl SseDecode for Vec<crate::bridge::ImageDataBridge> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut len_ = <i32>::sse_decode(deserializer);
        let mut ans_ = vec![];
        for idx_ in 0..len_ {
            ans_.push(<crate::bridge::ImageDataBridge>::sse_decode(deserializer));
        }
        return ans_;
    }
}

impl SseDecode for Vec<crate::bridge::ManipulationRegionBridge> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut len_ = <i32>::sse_decode(deserializer);
        let mut ans_ = vec![];
        for idx_ in 0..len_ {
            ans_.push(<crate::bridge::ManipulationRegionBridge>::sse_decode(
                deserializer,
            ));
        }
        return ans_;
    }
}

impl SseDecode for Vec<crate::bridge::MetadataPairBridge> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut len_ = <i32>::sse_decode(deserializer);
        let mut ans_ = vec![];
        for idx_ in 0..len_ {
            ans_.push(<crate::bridge::MetadataPairBridge>::sse_decode(
                deserializer,
            ));
        }
        return ans_;
    }
}

impl SseDecode for Vec<u8> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut len_ = <i32>::sse_decode(deserializer);
        let mut ans_ = vec![];
        for idx_ in 0..len_ {
            ans_.push(<u8>::sse_decode(deserializer));
        }
        return ans_;
    }
}

impl SseDecode for Vec<crate::bridge::RectBridge> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut len_ = <i32>::sse_decode(deserializer);
        let mut ans_ = vec![];
        for idx_ in 0..len_ {
            ans_.push(<crate::bridge::RectBridge>::sse_decode(deserializer));
        }
        return ans_;
    }
}

impl SseDecode for crate::bridge::ManipulationRegionBridge {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut var_x = <u32>::sse_decode(deserializer);
        let mut var_y = <u32>::sse_decode(deserializer);
        let mut var_width = <u32>::sse_decode(deserializer);
        let mut var_height = <u32>::sse_decode(deserializer);
        let mut var_confidence = <f32>::sse_decode(deserializer);
        let mut var_manipulationType = <String>::sse_decode(deserializer);
        let mut var_details = <String>::sse_decode(deserializer);
        return crate::bridge::ManipulationRegionBridge {
            x: var_x,
            y: var_y,
            width: var_width,
            height: var_height,
            confidence: var_confidence,
            manipulation_type: var_manipulationType,
            details: var_details,
        };
    }
}

impl SseDecode for crate::bridge::MetadataPairBridge {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut var_key = <String>::sse_decode(deserializer);
        let mut var_value = <String>::sse_decode(deserializer);
        return crate::bridge::MetadataPairBridge {
            key: var_key,
            value: var_value,
        };
    }
}

impl SseDecode for Option<String> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        if (<bool>::sse_decode(deserializer)) {
            return Some(<String>::sse_decode(deserializer));
        } else {
            return None;
        }
    }
}

impl SseDecode for Option<u32> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        if (<bool>::sse_decode(deserializer)) {
            return Some(<u32>::sse_decode(deserializer));
        } else {
            return None;
        }
    }
}

impl SseDecode for Option<u8> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        if (<bool>::sse_decode(deserializer)) {
            return Some(<u8>::sse_decode(deserializer));
        } else {
            return None;
        }
    }
}

impl SseDecode for crate::bridge::RectBridge {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut var_x = <u32>::sse_decode(deserializer);
        let mut var_y = <u32>::sse_decode(deserializer);
        let mut var_width = <u32>::sse_decode(deserializer);
        let mut var_height = <u32>::sse_decode(deserializer);
        return crate::bridge::RectBridge {
            x: var_x,
            y: var_y,
            width: var_width,
            height: var_height,
        };
    }
}

impl SseDecode for u32 {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        deserializer.cursor.read_u32::<NativeEndian>().unwrap()
    }
}

impl SseDecode for u64 {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        deserializer.cursor.read_u64::<NativeEndian>().unwrap()
    }
}

impl SseDecode for u8 {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        deserializer.cursor.read_u8().unwrap()
    }
}

impl SseDecode for () {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {}
}

impl SseDecode for usize {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        deserializer.cursor.read_u64::<NativeEndian>().unwrap() as _
    }
}

impl SseDecode for i32 {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        deserializer.cursor.read_i32::<NativeEndian>().unwrap()
    }
}

fn pde_ffi_dispatcher_primary_impl(
    func_id: i32,
    port: flutter_rust_bridge::for_generated::MessagePort,
    ptr: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len: i32,
    data_len: i32,
) {
    // Codec=Pde (Serialization + dispatch), see doc to use other codecs
    match func_id {
        1 => wire__crate__bridge__FilterEngineBridge_apply_filter_impl(
            port,
            ptr,
            rust_vec_len,
            data_len,
        ),
        3 => wire__crate__bridge__FilterEngineBridge_get_filter_preview_impl(
            port,
            ptr,
            rust_vec_len,
            data_len,
        ),
        5 => wire__crate__bridge__authentication_config_bridge_default_impl(
            port,
            ptr,
            rust_vec_len,
            data_len,
        ),
        6 => wire__crate__bridge__collage_engine_bridge_create_collage_impl(
            port,
            ptr,
            rust_vec_len,
            data_len,
        ),
        8 => wire__crate__bridge__collage_engine_bridge_get_layout_preview_impl(
            port,
            ptr,
            rust_vec_len,
            data_len,
        ),
        11 => wire__crate__bridge__image_authenticator_bridge_authenticate_image_impl(
            port,
            ptr,
            rust_vec_len,
            data_len,
        ),
        14 => wire__crate__bridge__image_compressor_bridge_compress_image_impl(
            port,
            ptr,
            rust_vec_len,
            data_len,
        ),
        15 => wire__crate__bridge__image_compressor_bridge_estimate_compression_impl(
            port,
            ptr,
            rust_vec_len,
            data_len,
        ),
        18 => wire__crate__bridge__image_processor_bridge_crop_image_impl(
            port,
            ptr,
            rust_vec_len,
            data_len,
        ),
        19 => wire__crate__bridge__image_processor_bridge_load_image_from_bytes_impl(
            port,
            ptr,
            rust_vec_len,
            data_len,
        ),
        20 => wire__crate__bridge__image_processor_bridge_load_image_from_path_impl(
            port,
            ptr,
            rust_vec_len,
            data_len,
        ),
        22 => wire__crate__bridge__image_processor_bridge_resize_image_impl(
            port,
            ptr,
            rust_vec_len,
            data_len,
        ),
        23 => wire__crate__bridge__image_processor_bridge_rotate_image_impl(
            port,
            ptr,
            rust_vec_len,
            data_len,
        ),
        24 => wire__crate__bridge__image_processor_bridge_save_image_to_bytes_impl(
            port,
            ptr,
            rust_vec_len,
            data_len,
        ),
        _ => unreachable!(),
    }
}

fn pde_ffi_dispatcher_sync_impl(
    func_id: i32,
    ptr: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len: i32,
    data_len: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    // Codec=Pde (Serialization + dispatch), see doc to use other codecs
    match func_id {
        2 => wire__crate__bridge__FilterEngineBridge_get_available_filters_impl(
            ptr,
            rust_vec_len,
            data_len,
        ),
        4 => wire__crate__bridge__FilterEngineBridge_new_impl(ptr, rust_vec_len, data_len),
        7 => wire__crate__bridge__collage_engine_bridge_get_available_layouts_impl(
            ptr,
            rust_vec_len,
            data_len,
        ),
        9 => wire__crate__bridge__collage_engine_bridge_new_impl(ptr, rust_vec_len, data_len),
        10 => wire__crate__bridge__get_library_info_impl(ptr, rust_vec_len, data_len),
        12 => wire__crate__bridge__image_authenticator_bridge_new_impl(ptr, rust_vec_len, data_len),
        13 => wire__crate__bridge__image_authenticator_bridge_with_config_impl(
            ptr,
            rust_vec_len,
            data_len,
        ),
        16 => wire__crate__bridge__image_compressor_bridge_get_supported_formats_impl(
            ptr,
            rust_vec_len,
            data_len,
        ),
        17 => wire__crate__bridge__image_compressor_bridge_new_impl(ptr, rust_vec_len, data_len),
        21 => wire__crate__bridge__image_processor_bridge_new_impl(ptr, rust_vec_len, data_len),
        25 => wire__crate__bridge__init_tushen_core_impl(ptr, rust_vec_len, data_len),
        _ => unreachable!(),
    }
}

// Section: rust2dart

// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for FrbWrapper<FilterEngineBridge> {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        flutter_rust_bridge::for_generated::rust_auto_opaque_encode::<_, MoiArc<_>>(self.0)
            .into_dart()
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive
    for FrbWrapper<FilterEngineBridge>
{
}

impl flutter_rust_bridge::IntoIntoDart<FrbWrapper<FilterEngineBridge>> for FilterEngineBridge {
    fn into_into_dart(self) -> FrbWrapper<FilterEngineBridge> {
        self.into()
    }
}

// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for crate::bridge::AnalysisImageBridge {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        [
            self.name.into_into_dart().into_dart(),
            self.image_data.into_into_dart().into_dart(),
        ]
        .into_dart()
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive
    for crate::bridge::AnalysisImageBridge
{
}
impl flutter_rust_bridge::IntoIntoDart<crate::bridge::AnalysisImageBridge>
    for crate::bridge::AnalysisImageBridge
{
    fn into_into_dart(self) -> crate::bridge::AnalysisImageBridge {
        self
    }
}
// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for crate::bridge::AnalysisResultBridge {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        [
            self.analysis_type.into_into_dart().into_dart(),
            self.confidence.into_into_dart().into_dart(),
            self.anomalies.into_into_dart().into_dart(),
            self.metadata.into_into_dart().into_dart(),
        ]
        .into_dart()
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive
    for crate::bridge::AnalysisResultBridge
{
}
impl flutter_rust_bridge::IntoIntoDart<crate::bridge::AnalysisResultBridge>
    for crate::bridge::AnalysisResultBridge
{
    fn into_into_dart(self) -> crate::bridge::AnalysisResultBridge {
        self
    }
}
// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for crate::core::image_authenticator::AuthenticationConfig {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        [
            self.ela_quality.into_into_dart().into_dart(),
            self.copy_move_threshold.into_into_dart().into_dart(),
            self.block_size.into_into_dart().into_dart(),
            self.noise_sensitivity.into_into_dart().into_dart(),
            self.analyze_metadata.into_into_dart().into_dart(),
        ]
        .into_dart()
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive
    for crate::core::image_authenticator::AuthenticationConfig
{
}
impl flutter_rust_bridge::IntoIntoDart<crate::core::image_authenticator::AuthenticationConfig>
    for crate::core::image_authenticator::AuthenticationConfig
{
    fn into_into_dart(self) -> crate::core::image_authenticator::AuthenticationConfig {
        self
    }
}
// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for crate::bridge::AuthenticationConfigBridge {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        [
            self.ela_quality.into_into_dart().into_dart(),
            self.copy_move_threshold.into_into_dart().into_dart(),
            self.block_size.into_into_dart().into_dart(),
            self.noise_sensitivity.into_into_dart().into_dart(),
            self.analyze_metadata.into_into_dart().into_dart(),
        ]
        .into_dart()
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive
    for crate::bridge::AuthenticationConfigBridge
{
}
impl flutter_rust_bridge::IntoIntoDart<crate::bridge::AuthenticationConfigBridge>
    for crate::bridge::AuthenticationConfigBridge
{
    fn into_into_dart(self) -> crate::bridge::AuthenticationConfigBridge {
        self
    }
}
// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for crate::bridge::AuthenticationResultBridge {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        [
            self.authenticity_score.into_into_dart().into_dart(),
            self.analyses.into_into_dart().into_dart(),
            self.manipulation_regions.into_into_dart().into_dart(),
            self.processing_time_ms.into_into_dart().into_dart(),
            self.analysis_images.into_into_dart().into_dart(),
        ]
        .into_dart()
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive
    for crate::bridge::AuthenticationResultBridge
{
}
impl flutter_rust_bridge::IntoIntoDart<crate::bridge::AuthenticationResultBridge>
    for crate::bridge::AuthenticationResultBridge
{
    fn into_into_dart(self) -> crate::bridge::AuthenticationResultBridge {
        self
    }
}
// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for crate::bridge::CollageEngineBridge {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        Vec::<u8>::new().into_dart()
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive
    for crate::bridge::CollageEngineBridge
{
}
impl flutter_rust_bridge::IntoIntoDart<crate::bridge::CollageEngineBridge>
    for crate::bridge::CollageEngineBridge
{
    fn into_into_dart(self) -> crate::bridge::CollageEngineBridge {
        self
    }
}
// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for crate::bridge::CompressionEstimateBridge {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        [
            self.estimated_size.into_into_dart().into_dart(),
            self.compression_ratio.into_into_dart().into_dart(),
            self.space_saved.into_into_dart().into_dart(),
        ]
        .into_dart()
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive
    for crate::bridge::CompressionEstimateBridge
{
}
impl flutter_rust_bridge::IntoIntoDart<crate::bridge::CompressionEstimateBridge>
    for crate::bridge::CompressionEstimateBridge
{
    fn into_into_dart(self) -> crate::bridge::CompressionEstimateBridge {
        self
    }
}
// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for crate::bridge::CompressionResultBridge {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        [
            self.image_data.into_into_dart().into_dart(),
            self.original_size.into_into_dart().into_dart(),
            self.compressed_size.into_into_dart().into_dart(),
            self.compression_ratio.into_into_dart().into_dart(),
            self.space_saved.into_into_dart().into_dart(),
            self.space_saved_percentage.into_into_dart().into_dart(),
            self.final_quality.into_into_dart().into_dart(),
            self.iterations.into_into_dart().into_dart(),
            self.target_achieved.into_into_dart().into_dart(),
        ]
        .into_dart()
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive
    for crate::bridge::CompressionResultBridge
{
}
impl flutter_rust_bridge::IntoIntoDart<crate::bridge::CompressionResultBridge>
    for crate::bridge::CompressionResultBridge
{
    fn into_into_dart(self) -> crate::bridge::CompressionResultBridge {
        self
    }
}
// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for crate::core::image_authenticator::ImageAuthenticator {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        [self.config.into_into_dart().into_dart()].into_dart()
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive
    for crate::core::image_authenticator::ImageAuthenticator
{
}
impl flutter_rust_bridge::IntoIntoDart<crate::core::image_authenticator::ImageAuthenticator>
    for crate::core::image_authenticator::ImageAuthenticator
{
    fn into_into_dart(self) -> crate::core::image_authenticator::ImageAuthenticator {
        self
    }
}
// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for crate::bridge::ImageAuthenticatorBridge {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        [self.authenticator.into_into_dart().into_dart()].into_dart()
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive
    for crate::bridge::ImageAuthenticatorBridge
{
}
impl flutter_rust_bridge::IntoIntoDart<crate::bridge::ImageAuthenticatorBridge>
    for crate::bridge::ImageAuthenticatorBridge
{
    fn into_into_dart(self) -> crate::bridge::ImageAuthenticatorBridge {
        self
    }
}
// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for crate::bridge::ImageCompressorBridge {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        Vec::<u8>::new().into_dart()
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive
    for crate::bridge::ImageCompressorBridge
{
}
impl flutter_rust_bridge::IntoIntoDart<crate::bridge::ImageCompressorBridge>
    for crate::bridge::ImageCompressorBridge
{
    fn into_into_dart(self) -> crate::bridge::ImageCompressorBridge {
        self
    }
}
// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for crate::bridge::ImageDataBridge {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        [
            self.width.into_into_dart().into_dart(),
            self.height.into_into_dart().into_dart(),
            self.format.into_into_dart().into_dart(),
            self.color_space.into_into_dart().into_dart(),
            self.data.into_into_dart().into_dart(),
        ]
        .into_dart()
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive
    for crate::bridge::ImageDataBridge
{
}
impl flutter_rust_bridge::IntoIntoDart<crate::bridge::ImageDataBridge>
    for crate::bridge::ImageDataBridge
{
    fn into_into_dart(self) -> crate::bridge::ImageDataBridge {
        self
    }
}
// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for crate::bridge::ImageProcessorBridge {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        Vec::<u8>::new().into_dart()
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive
    for crate::bridge::ImageProcessorBridge
{
}
impl flutter_rust_bridge::IntoIntoDart<crate::bridge::ImageProcessorBridge>
    for crate::bridge::ImageProcessorBridge
{
    fn into_into_dart(self) -> crate::bridge::ImageProcessorBridge {
        self
    }
}
// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for crate::bridge::ManipulationRegionBridge {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        [
            self.x.into_into_dart().into_dart(),
            self.y.into_into_dart().into_dart(),
            self.width.into_into_dart().into_dart(),
            self.height.into_into_dart().into_dart(),
            self.confidence.into_into_dart().into_dart(),
            self.manipulation_type.into_into_dart().into_dart(),
            self.details.into_into_dart().into_dart(),
        ]
        .into_dart()
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive
    for crate::bridge::ManipulationRegionBridge
{
}
impl flutter_rust_bridge::IntoIntoDart<crate::bridge::ManipulationRegionBridge>
    for crate::bridge::ManipulationRegionBridge
{
    fn into_into_dart(self) -> crate::bridge::ManipulationRegionBridge {
        self
    }
}
// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for crate::bridge::MetadataPairBridge {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        [
            self.key.into_into_dart().into_dart(),
            self.value.into_into_dart().into_dart(),
        ]
        .into_dart()
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive
    for crate::bridge::MetadataPairBridge
{
}
impl flutter_rust_bridge::IntoIntoDart<crate::bridge::MetadataPairBridge>
    for crate::bridge::MetadataPairBridge
{
    fn into_into_dart(self) -> crate::bridge::MetadataPairBridge {
        self
    }
}
// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for crate::bridge::RectBridge {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        [
            self.x.into_into_dart().into_dart(),
            self.y.into_into_dart().into_dart(),
            self.width.into_into_dart().into_dart(),
            self.height.into_into_dart().into_dart(),
        ]
        .into_dart()
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive for crate::bridge::RectBridge {}
impl flutter_rust_bridge::IntoIntoDart<crate::bridge::RectBridge> for crate::bridge::RectBridge {
    fn into_into_dart(self) -> crate::bridge::RectBridge {
        self
    }
}

impl SseEncode for FilterEngineBridge {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<FilterEngineBridge>>>::sse_encode(flutter_rust_bridge::for_generated::rust_auto_opaque_encode::<_, MoiArc<_>>(self), serializer);
    }
}

impl SseEncode
    for RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<FilterEngineBridge>>
{
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        let (ptr, size) = self.sse_encode_raw();
        <usize>::sse_encode(ptr, serializer);
        <i32>::sse_encode(size, serializer);
    }
}

impl SseEncode for String {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <Vec<u8>>::sse_encode(self.into_bytes(), serializer);
    }
}

impl SseEncode for crate::bridge::AnalysisImageBridge {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <String>::sse_encode(self.name, serializer);
        <crate::bridge::ImageDataBridge>::sse_encode(self.image_data, serializer);
    }
}

impl SseEncode for crate::bridge::AnalysisResultBridge {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <String>::sse_encode(self.analysis_type, serializer);
        <f32>::sse_encode(self.confidence, serializer);
        <Vec<String>>::sse_encode(self.anomalies, serializer);
        <Vec<crate::bridge::MetadataPairBridge>>::sse_encode(self.metadata, serializer);
    }
}

impl SseEncode for crate::core::image_authenticator::AuthenticationConfig {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <u8>::sse_encode(self.ela_quality, serializer);
        <f32>::sse_encode(self.copy_move_threshold, serializer);
        <u32>::sse_encode(self.block_size, serializer);
        <f32>::sse_encode(self.noise_sensitivity, serializer);
        <bool>::sse_encode(self.analyze_metadata, serializer);
    }
}

impl SseEncode for crate::bridge::AuthenticationConfigBridge {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <u8>::sse_encode(self.ela_quality, serializer);
        <f32>::sse_encode(self.copy_move_threshold, serializer);
        <u32>::sse_encode(self.block_size, serializer);
        <f32>::sse_encode(self.noise_sensitivity, serializer);
        <bool>::sse_encode(self.analyze_metadata, serializer);
    }
}

impl SseEncode for crate::bridge::AuthenticationResultBridge {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <f32>::sse_encode(self.authenticity_score, serializer);
        <Vec<crate::bridge::AnalysisResultBridge>>::sse_encode(self.analyses, serializer);
        <Vec<crate::bridge::ManipulationRegionBridge>>::sse_encode(
            self.manipulation_regions,
            serializer,
        );
        <u64>::sse_encode(self.processing_time_ms, serializer);
        <Vec<crate::bridge::AnalysisImageBridge>>::sse_encode(self.analysis_images, serializer);
    }
}

impl SseEncode for bool {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        serializer.cursor.write_u8(self as _).unwrap();
    }
}

impl SseEncode for crate::bridge::CollageEngineBridge {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {}
}

impl SseEncode for crate::bridge::CompressionEstimateBridge {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <u64>::sse_encode(self.estimated_size, serializer);
        <f32>::sse_encode(self.compression_ratio, serializer);
        <u64>::sse_encode(self.space_saved, serializer);
    }
}

impl SseEncode for crate::bridge::CompressionResultBridge {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <crate::bridge::ImageDataBridge>::sse_encode(self.image_data, serializer);
        <u64>::sse_encode(self.original_size, serializer);
        <u64>::sse_encode(self.compressed_size, serializer);
        <f32>::sse_encode(self.compression_ratio, serializer);
        <u64>::sse_encode(self.space_saved, serializer);
        <f32>::sse_encode(self.space_saved_percentage, serializer);
        <u8>::sse_encode(self.final_quality, serializer);
        <u32>::sse_encode(self.iterations, serializer);
        <bool>::sse_encode(self.target_achieved, serializer);
    }
}

impl SseEncode for f32 {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        serializer.cursor.write_f32::<NativeEndian>(self).unwrap();
    }
}

impl SseEncode for crate::core::image_authenticator::ImageAuthenticator {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <crate::core::image_authenticator::AuthenticationConfig>::sse_encode(
            self.config,
            serializer,
        );
    }
}

impl SseEncode for crate::bridge::ImageAuthenticatorBridge {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <crate::core::image_authenticator::ImageAuthenticator>::sse_encode(
            self.authenticator,
            serializer,
        );
    }
}

impl SseEncode for crate::bridge::ImageCompressorBridge {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {}
}

impl SseEncode for crate::bridge::ImageDataBridge {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <u32>::sse_encode(self.width, serializer);
        <u32>::sse_encode(self.height, serializer);
        <String>::sse_encode(self.format, serializer);
        <String>::sse_encode(self.color_space, serializer);
        <Vec<u8>>::sse_encode(self.data, serializer);
    }
}

impl SseEncode for crate::bridge::ImageProcessorBridge {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {}
}

impl SseEncode for Vec<String> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <i32>::sse_encode(self.len() as _, serializer);
        for item in self {
            <String>::sse_encode(item, serializer);
        }
    }
}

impl SseEncode for Vec<crate::bridge::AnalysisImageBridge> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <i32>::sse_encode(self.len() as _, serializer);
        for item in self {
            <crate::bridge::AnalysisImageBridge>::sse_encode(item, serializer);
        }
    }
}

impl SseEncode for Vec<crate::bridge::AnalysisResultBridge> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <i32>::sse_encode(self.len() as _, serializer);
        for item in self {
            <crate::bridge::AnalysisResultBridge>::sse_encode(item, serializer);
        }
    }
}

impl SseEncode for Vec<crate::bridge::ImageDataBridge> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <i32>::sse_encode(self.len() as _, serializer);
        for item in self {
            <crate::bridge::ImageDataBridge>::sse_encode(item, serializer);
        }
    }
}

impl SseEncode for Vec<crate::bridge::ManipulationRegionBridge> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <i32>::sse_encode(self.len() as _, serializer);
        for item in self {
            <crate::bridge::ManipulationRegionBridge>::sse_encode(item, serializer);
        }
    }
}

impl SseEncode for Vec<crate::bridge::MetadataPairBridge> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <i32>::sse_encode(self.len() as _, serializer);
        for item in self {
            <crate::bridge::MetadataPairBridge>::sse_encode(item, serializer);
        }
    }
}

impl SseEncode for Vec<u8> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <i32>::sse_encode(self.len() as _, serializer);
        for item in self {
            <u8>::sse_encode(item, serializer);
        }
    }
}

impl SseEncode for Vec<crate::bridge::RectBridge> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <i32>::sse_encode(self.len() as _, serializer);
        for item in self {
            <crate::bridge::RectBridge>::sse_encode(item, serializer);
        }
    }
}

impl SseEncode for crate::bridge::ManipulationRegionBridge {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <u32>::sse_encode(self.x, serializer);
        <u32>::sse_encode(self.y, serializer);
        <u32>::sse_encode(self.width, serializer);
        <u32>::sse_encode(self.height, serializer);
        <f32>::sse_encode(self.confidence, serializer);
        <String>::sse_encode(self.manipulation_type, serializer);
        <String>::sse_encode(self.details, serializer);
    }
}

impl SseEncode for crate::bridge::MetadataPairBridge {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <String>::sse_encode(self.key, serializer);
        <String>::sse_encode(self.value, serializer);
    }
}

impl SseEncode for Option<String> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <bool>::sse_encode(self.is_some(), serializer);
        if let Some(value) = self {
            <String>::sse_encode(value, serializer);
        }
    }
}

impl SseEncode for Option<u32> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <bool>::sse_encode(self.is_some(), serializer);
        if let Some(value) = self {
            <u32>::sse_encode(value, serializer);
        }
    }
}

impl SseEncode for Option<u8> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <bool>::sse_encode(self.is_some(), serializer);
        if let Some(value) = self {
            <u8>::sse_encode(value, serializer);
        }
    }
}

impl SseEncode for crate::bridge::RectBridge {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <u32>::sse_encode(self.x, serializer);
        <u32>::sse_encode(self.y, serializer);
        <u32>::sse_encode(self.width, serializer);
        <u32>::sse_encode(self.height, serializer);
    }
}

impl SseEncode for u32 {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        serializer.cursor.write_u32::<NativeEndian>(self).unwrap();
    }
}

impl SseEncode for u64 {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        serializer.cursor.write_u64::<NativeEndian>(self).unwrap();
    }
}

impl SseEncode for u8 {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        serializer.cursor.write_u8(self).unwrap();
    }
}

impl SseEncode for () {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {}
}

impl SseEncode for usize {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        serializer
            .cursor
            .write_u64::<NativeEndian>(self as _)
            .unwrap();
    }
}

impl SseEncode for i32 {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        serializer.cursor.write_i32::<NativeEndian>(self).unwrap();
    }
}

#[cfg(not(target_family = "wasm"))]
mod io {
    // This file is automatically generated, so please do not edit it.
    // @generated by `flutter_rust_bridge`@ 2.9.0.

    // Section: imports

    use super::*;
    use crate::bridge::*;
    use flutter_rust_bridge::for_generated::byteorder::{
        NativeEndian, ReadBytesExt, WriteBytesExt,
    };
    use flutter_rust_bridge::for_generated::{transform_result_dco, Lifetimeable, Lockable};
    use flutter_rust_bridge::{Handler, IntoIntoDart};

    // Section: boilerplate

    flutter_rust_bridge::frb_generated_boilerplate_io!();

    #[unsafe(no_mangle)]
    pub extern "C" fn frbgen_tushen_rust_arc_increment_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFilterEngineBridge(
        ptr: *const std::ffi::c_void,
    ) {
        MoiArc::<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<FilterEngineBridge>>::increment_strong_count(ptr as _);
    }

    #[unsafe(no_mangle)]
    pub extern "C" fn frbgen_tushen_rust_arc_decrement_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFilterEngineBridge(
        ptr: *const std::ffi::c_void,
    ) {
        MoiArc::<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<FilterEngineBridge>>::decrement_strong_count(ptr as _);
    }
}
#[cfg(not(target_family = "wasm"))]
pub use io::*;

/// cbindgen:ignore
#[cfg(target_family = "wasm")]
mod web {
    // This file is automatically generated, so please do not edit it.
    // @generated by `flutter_rust_bridge`@ 2.9.0.

    // Section: imports

    use super::*;
    use crate::bridge::*;
    use flutter_rust_bridge::for_generated::byteorder::{
        NativeEndian, ReadBytesExt, WriteBytesExt,
    };
    use flutter_rust_bridge::for_generated::wasm_bindgen;
    use flutter_rust_bridge::for_generated::wasm_bindgen::prelude::*;
    use flutter_rust_bridge::for_generated::{transform_result_dco, Lifetimeable, Lockable};
    use flutter_rust_bridge::{Handler, IntoIntoDart};

    // Section: boilerplate

    flutter_rust_bridge::frb_generated_boilerplate_web!();

    #[wasm_bindgen]
    pub fn rust_arc_increment_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFilterEngineBridge(
        ptr: *const std::ffi::c_void,
    ) {
        MoiArc::<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<FilterEngineBridge>>::increment_strong_count(ptr as _);
    }

    #[wasm_bindgen]
    pub fn rust_arc_decrement_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerFilterEngineBridge(
        ptr: *const std::ffi::c_void,
    ) {
        MoiArc::<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<FilterEngineBridge>>::decrement_strong_count(ptr as _);
    }
}
#[cfg(target_family = "wasm")]
pub use web::*;
