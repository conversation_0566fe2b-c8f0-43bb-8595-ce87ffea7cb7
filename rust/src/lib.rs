//! TuShen Core - High-performance image processing library
//!
//! This library provides the core image processing functionality for the TuShen app,
//! including filters, collage creation, format conversion, and live photo generation.

mod bridge_generated; /* AUTO INJECTED BY flutter_rust_bridge. This line may not be accurate, and you can change it according to your needs. */

pub mod bridge;
pub mod core;
pub mod filters;
pub mod utils;
pub mod error;

// Re-export main types
pub use crate::core::{
    TuShenCore,
    ImageProcessor,
    CollageEngine,
    CollageLayout,
    LayoutType,
    Region,
    FormatConverter,
    LivePhotoEngine,
};

pub use crate::filters::{
    FilterEngine,
    FilterType,
    FilterCategory,
    PortraitFilter,
    LandscapeFilter,
    ArchitectureFilter,
    FoodFilter,
    ArtisticFilter,
};

pub use crate::error::{
    TuShenError,
    ProcessingError,
    CollageError,
    ConversionError,
    FilterError,
};

pub use crate::utils::{
    ImageData,
    ImageFormat,
    ProcessingOptions,
    PerformanceMonitor,
};

use once_cell::sync::Lazy;
use std::sync::Mutex;

/// Global performance monitor instance
static PERFORMANCE_MONITOR: Lazy<Mutex<PerformanceMonitor>> = 
    Lazy::new(|| Mutex::new(PerformanceMonitor::new()));

/// Initialize the TuShen core library
/// 
/// This function should be called once when the library is loaded.
/// It sets up logging, performance monitoring, and other global state.
pub fn init() -> Result<(), TuShenError> {
    #[cfg(feature = "debug-mode")]
    {
        env_logger::init();
        log::info!("TuShen Core initialized with debug mode");
    }
    
    // Initialize performance monitoring
    let mut monitor = PERFORMANCE_MONITOR.lock().map_err(|_| {
        TuShenError::Internal("Failed to acquire performance monitor lock".to_string())
    })?;
    
    monitor.start_session();
    
    #[cfg(feature = "gpu-acceleration")]
    {
        // Initialize GPU acceleration if available
        if let Err(_e) = init_gpu_acceleration() {
            #[cfg(feature = "log")]
            log::warn!("GPU acceleration initialization failed: {}", e);
        }
    }
    
    Ok(())
}

/// Cleanup resources when the library is unloaded
pub fn cleanup() -> Result<(), TuShenError> {
    let mut monitor = PERFORMANCE_MONITOR.lock().map_err(|_| {
        TuShenError::Internal("Failed to acquire performance monitor lock".to_string())
    })?;
    
    monitor.end_session();
    
    #[cfg(feature = "gpu-acceleration")]
    {
        cleanup_gpu_acceleration();
    }
    
    #[cfg(feature = "debug-mode")]
    {
        log::info!("TuShen Core cleanup completed");
    }
    
    Ok(())
}

/// Get performance statistics
pub fn get_performance_stats() -> Result<String, TuShenError> {
    let monitor = PERFORMANCE_MONITOR.lock().map_err(|_| {
        TuShenError::Internal("Failed to acquire performance monitor lock".to_string())
    })?;
    
    Ok(monitor.get_stats_json())
}

#[cfg(feature = "gpu-acceleration")]
fn init_gpu_acceleration() -> Result<(), TuShenError> {
    #[cfg(target_os = "android")]
    {
        // Initialize Vulkan for Android
        // Implementation would go here
    }
    
    #[cfg(any(target_os = "ios", target_os = "macos"))]
    {
        // Initialize Metal for iOS/macOS
        // Implementation would go here
    }
    
    #[cfg(target_os = "windows")]
    {
        // Initialize DirectX for Windows
        // Implementation would go here
    }
    
    Ok(())
}

#[cfg(feature = "gpu-acceleration")]
fn cleanup_gpu_acceleration() {
    // Cleanup GPU resources
    // Implementation would go here
}

/// Version information
pub const VERSION: &str = env!("CARGO_PKG_VERSION");
pub const NAME: &str = env!("CARGO_PKG_NAME");
pub const DESCRIPTION: &str = env!("CARGO_PKG_DESCRIPTION");

/// Get library information
pub fn get_library_info() -> serde_json::Value {
    serde_json::json!({
        "name": NAME,
        "version": VERSION,
        "description": DESCRIPTION,
        "features": get_enabled_features(),
        "platform": get_platform_info(),
    })
}

fn get_enabled_features() -> Vec<&'static str> {
    let mut features = Vec::new();
    
    #[cfg(feature = "parallel")]
    features.push("parallel");
    
    #[cfg(feature = "gpu-acceleration")]
    features.push("gpu-acceleration");
    
    #[cfg(feature = "debug-mode")]
    features.push("debug-mode");
    
    #[cfg(feature = "heic")]
    features.push("heic");
    
    #[cfg(feature = "raw")]
    features.push("raw");
    
    #[cfg(feature = "avif")]
    features.push("avif");
    
    #[cfg(feature = "ai-filters")]
    features.push("ai-filters");
    
    #[cfg(feature = "video-processing")]
    features.push("video-processing");
    
    #[cfg(feature = "live-photo")]
    features.push("live-photo");
    
    features
}

fn get_platform_info() -> serde_json::Value {
    serde_json::json!({
        "os": std::env::consts::OS,
        "arch": std::env::consts::ARCH,
        "family": std::env::consts::FAMILY,
    })
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_init_and_cleanup() {
        assert!(init().is_ok());
        assert!(cleanup().is_ok());
    }

    #[test]
    fn test_library_info() {
        let info = get_library_info();
        assert_eq!(info["name"], NAME);
        assert_eq!(info["version"], VERSION);
        assert!(info["features"].is_array());
        assert!(info["platform"].is_object());
    }

    #[test]
    fn test_performance_stats() {
        init().unwrap();
        let stats = get_performance_stats();
        assert!(stats.is_ok());
        cleanup().unwrap();
    }
}

// FFI exports for Flutter
#[cfg(feature = "flutter_rust_bridge")]
pub use bridge::*;
